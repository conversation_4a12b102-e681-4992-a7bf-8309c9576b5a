---
# https://github.com/KPMG-UK/ie-cdd-platform-helm/blob/main/charts/common/values.yaml
common:
  names:
    projectShort: scf
    project: scf
    serviceName: mono-claims-portal
  namespace: ms
  image:
    repository: 109286117078.dkr.ecr.eu-west-2.amazonaws.com/mono-claims-portal
    tag: latest
    includeImagePullSecrets: false
  deployment:
    annotations:
      reloader.stakater.com/auto: 'true'
    autoscaling:
      enabled: true
  pod:
    securityContext:
      runAsUser: 1000
      runAsGroup: 1000
    annotations: {}
  container:
    env:
      configmaps:
        - name: mono-claims-portal
      secrets:
        - name: mono-claims-portal
    liveness:
      enabled: true
      path: /liveness
      period: 20
      failureThreshold: 3
      timeout: 5
    readiness:
      enabled: true
      path: /readiness
      period: 30
      timeout: 5
    resources:
      requests:
        memory: 768Mi  # 1Gi - 256Mi (Fargate)
        cpu: 500m
      limits:
        memory: 24Gi  # Intentionally huge values, using Fargate pod limit, cannot set null or argo crashes
        cpu: '8'
  ingress:
    # Can be set to false for non public service
    # See env for host setting
    enabled: true
    hosts:
      - host: claims-portal.{{ include "data.domain" . }}
    annotations:
      kubernetes.io/tls-acme: 'true'
      cert-manager.io/cluster-issuer: letsencrypt
  serviceAccount:
    create: false
    name: mono-claims-portal
  services:
    xray:
      enabled: false
