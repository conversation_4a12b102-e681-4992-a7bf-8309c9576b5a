---
job:
  nameOverride: mono-scuk-db
  image:
    repository: ************.dkr.ecr.eu-west-2.amazonaws.com/mono-scuk-db
    tag: latest
  command:
  serviceAccount:
    annotations:
      version: '{{ .Values.image.tag }}'
    create: true
  container:
    securityContext:
      readOnlyRootFilesystem: false
  job:
    annotations:
      argocd.argoproj.io/hook: PostSync
      argocd.argoproj.io/hook-delete-policy: BeforeHookCreation
  pod:
    env:
      standard:
        - name: SERVICENAME
          value: mono-scuk-db
        - name: PG_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: mono-scuk-db
              key: pg-connection-string
