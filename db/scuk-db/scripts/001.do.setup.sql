CREATE SCHEMA IF NOT EXISTS scuk;

CREATE TABLE IF NOT EXISTS scuk.customers (
  customer_id TEXT PRIMARY KEY,
  data JSONB NOT NULL
);

CREATE TABLE IF NOT EXISTS scuk.contracts (
  contract_id TEXT PRIMARY KEY,
  customer_id TEXT REFERENCES scuk.customers(customer_id) ON DELETE CASCADE,
  data JSONB NOT NULL
);

CREATE TABLE IF NOT EXISTS scuk.outreach (
  outreach_id TEXT PRIMARY KEY,
  outreach_type TEXT NOT NULL
);

CREATE TABLE IF NOT EXISTS scuk.customer_outreach_links (
  customer_id TEXT REFERENCES scuk.customers(customer_id) ON DELETE CASCADE,
  outreach_id TEXT REFERENCES scuk.outreach(outreach_id) ON DELETE CASCADE,
  PRIMARY KEY (customer_id, outreach_id)
);
