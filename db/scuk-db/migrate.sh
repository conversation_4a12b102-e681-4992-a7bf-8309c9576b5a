#!/bin/sh
set -e

# This is to be used by the container init

cd "$(dirname "$0")"

mv scripts dbscripts
mkdir scripts
cd dbscripts

find * -maxdepth 1 -type f -name "*.sql" | while read i
do
    fcount=`echo "$i" | cut -d ' ' -f 1 |cut -d '.' -f 1` # Take the prefix number
    fcount=`echo "$((10#$fcount+0))"` # Convert to int
    fhex=`printf '00000000-%06d.sql' $fcount` # Transform into sem-apply format: https://github.com/mbryzek/schema-evolution-manager/blob/0.9.41/lib/schema-evolution-manager/scripts.rb#L76
    cp -v "${i}" "../scripts/${fhex}"
done
cd ..

sem-apply --url "'$PG_CONNECTION_STRING'" --dry_run | grep -v 'Upgrading schema for'
sem-apply --url "'$PG_CONNECTION_STRING'" | grep -v 'Upgrading schema for'

rm -rf scripts
mv dbscripts scripts
