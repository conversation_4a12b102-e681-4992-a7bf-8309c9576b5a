import PropTypes from 'prop-types'
import styles from './layout.module.scss'
import logo from './public/images/logo.png'

export default function Layout({ children }) {
  return (
    <div className={styles.layout}>
      <header>
        <div className="viewport">
          <img src={logo} alt="Logo" />
        </div>
      </header>
      <article>
        {children}
      </article>
      <footer>
        <div className="container">
          <div className="row">
            <span className="copy">&copy; 2025 Santander Consumer (UK) plc</span>
          </div>
          <div className="row disclaimer">
            <span>
              {/* eslint-disable-next-line max-len */}
              Santander Consumer (UK) plc trading as Santander Consumer Finance. Registered Office: Santander House, 86 Station Road, Redhill. Surrey RH1 1SR: Registered in England and Wales. Company registration number: 2248870. VAT registration number: *********. Santander Consumer (UK) plc is authorised and regulated by the Financial Conduct Authority reference number 444327. A member of the Finance & Leasing Association and complies with its Lending Code, a copy of which we will provide on request or is available at www.fla.org.uk.
            </span>
          </div>
        </div>
      </footer>
    </div>
  )
}

Layout.propTypes = { children: PropTypes.node.isRequired }
