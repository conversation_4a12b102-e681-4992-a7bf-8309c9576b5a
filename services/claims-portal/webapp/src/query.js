/* eslint-disable max-classes-per-file */
import { QueryClient } from '@tanstack/react-query'
import { getStepRoute } from './stepRoutes.js'

export class QueryError extends Error {
  /**
   * @param {string} message
   * @param {number} status
   */
  constructor(message, status) {
    super(message)
    this.name = 'QueryError'
    this.status = status
  }
}

export class StepError extends Error {
  /**
   * @param {string} message
   * @param {string} step
   */
  constructor(message, step) {
    super(message)
    this.name = 'StepError'
    this.step = step
  }
}

const totalAttempts = 3

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
      retry: (retryAttempts, error) => {
        if (retryAttempts >= totalAttempts - 1) { return false }
        if (error instanceof StepError) { return false }
        if (error instanceof QueryError) {
          return error.status >= 500
        }
        return true
      },
    },
    mutations: {
      retry: (retryAttempts, error) => {
        if (retryAttempts < totalAttempts - 1 && error.name === 'TimeoutError') {
          return true
        }
        return false
      },
    },
  },
})

export function preSessionRetry(retryAttempts, error) {
  if (retryAttempts >= totalAttempts - 1) { return false }
  if (error instanceof QueryError) {
    return error.status >= 500
  }
  return true
}

/**
 * @typedef RequestOptions
 * @property {string} url - The URL to fetch
 * @property {'GET' | 'POST'} method - The HTTP method to use
 * @property {HeadersInit} [headers] - Additional headers to include
 * @property {any} [body] - The body of the request, for POST requests
 * @property {File} [file] - The file to send, for POST requests
 * @property {number} [timeout=10000] - Timeout in milliseconds
 * @property {string} errorMessage - The operation to perform
 */

/**
 * @param {RequestOptions} options
 */
export async function request(options) {
  const headers = new Headers(options.headers || {})
  let body
  if (typeof options.body === 'object' && options.body !== null) {
    body = JSON.stringify(options.body)
    headers.set('Content-Type', 'application/json')
  } else if (options.file !== undefined) {
    body = options.file
    if (!headers.has('Content-Type')) {
      headers.set('Content-Type', 'application/octet-stream')
    }
  }
  const result = await fetch(options.url, {
    method: options.method,
    headers,
    body,
    signal: AbortSignal.timeout(options.timeout ?? 10000),
  })
  if (!result.ok) {
    throw new QueryError(options.errorMessage, result.status)
  }
  if (!result.headers.get('Content-Type')?.includes('application/json')) {
    return null
  }
  const data = await result.json()
  if (data.step) {
    throw new StepError(options.errorMessage, data.step)
  }
  return data.result ?? null
}

/**
 * @param {Error} error
 * @param {ReturnType<import('react-router').useNavigate>} navigate
 */
export function handleMutationError(error, navigate) {
  if (error instanceof StepError) {
    navigate(getStepRoute(error.step), { replace: true })
    return true
  }
  if (error instanceof QueryError) {
    if (error.status === 401) {
      navigate('/session-expired', { replace: true })
      return true
    }
  }
  return false
}
