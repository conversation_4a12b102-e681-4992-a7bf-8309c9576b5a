@use './public/css/colours.scss' as colours;
@use './public/css/fontFaces.scss';
@use './public/css/fonts.scss';

*, *:before, *:after {
  box-sizing: border-box;
}

html, body, #app {
  min-height: 100vh;
  width: 100vw;
  margin: 0;
  color: colours.$mid-grey;
}

body {
  background-color: #fff;
}

#app {
  isolation: isolate;
}

header {
  width: 100%;
  height: 64px;
  background-color: colours.$red;
  color: colours.$white;

  img {
    margin-left: -24px;
    height: 64px;
  }
}

footer {
  width: 100%;
  height: 214px;
  padding: 38px 0;
  background-color: colours.$red;
  color: colours.$white;

  .copy {
    font-family: fonts.$microtext-rg;
    font-size: 16px;
  }

  > div {
    max-width: 1140px;
    margin: 0 auto;

    .row {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
    }

    .disclaimer {
      padding: 20px;

      span {
        font-family: fonts.$microtext-rg;
        font-weight: 400;
        line-height: 18px;
        font-size: 12px;
        text-align: center;
      }
    }
  }
}

section {
  width: 100%;
}

form > * + * {
  margin-top: 16px;
}

.viewport {
  max-width: 1152px;
  margin: 0 auto;
}
