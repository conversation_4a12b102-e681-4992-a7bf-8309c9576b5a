@use './public/css/colours.scss' as colours;
@use './public/css/fontFaces.scss';
@use './public/css/fonts.scss';

*, *:before, *:after {
  box-sizing: border-box;
}

html, body, #app {
  min-height: 100vh;
  width: 100vw;
  margin: 0;
  color: colours.$text-default;
}

body {
  background-color: colours.$background-default;
}

#app {
  isolation: isolate;
}

.viewport {
  margin: 0 auto;
  max-width: 1152px;
  padding: 0;

  @media screen and (min-width: 450px) {
    padding: 0 24px;
  }
}

form > *:not(p) + *, form > p + p {
  margin-top: 16px;
}
