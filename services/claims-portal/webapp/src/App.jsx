import { BrowserRouter, Routes, Route } from 'react-router'
import { QueryClientProvider } from '@tanstack/react-query'
import 'react-widgets/styles.css'
import './public/favicon.ico'
import './styles.scss'
import Layout from './Layout.jsx'
import LandingPage from './routes/LandingPage/index.jsx'
import GetStarted from './routes/GetStarted/index.jsx'
import AdditionalInfo from './routes/AdditionalInfo/index.jsx'
import Verification from './routes/Verification/index.jsx'
import VerificationFailed from './routes/VerificationFailed/index.jsx'
import ContactDetails from './routes/ContactDetails/index.jsx'
import LoanDetails from './routes/LoanDetails/index.jsx'
import LoanDetailsOffer from './routes/LoanDetails/LoanDetailsOffer/index.jsx'
import PaymentDetails from './routes/PaymentDetails/index.jsx'
import ClaimComplete from './routes/ClaimComplete/index.jsx'
import SessionExpired from './routes/SessionExpired/index.jsx'
import NotFound from './routes/NotFound.jsx'
import TestData from './routes/TestData/index.jsx'
import { queryClient } from './query.js'

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route index element={<LandingPage />} />
            <Route path="/get-started" element={<GetStarted />} />
            <Route path="/additional-information" element={<AdditionalInfo />} />
            <Route path="/verification" element={<Verification />} />
            <Route path="/verification-failed" element={<VerificationFailed />} />
            <Route path="/contact-details" element={<ContactDetails />} />
            <Route path="/loan-details/:agreementNumber" element={<LoanDetailsOffer />} />
            <Route path="/loan-details" element={<LoanDetails />} />
            <Route path="/payment-details" element={<PaymentDetails />} />
            <Route path="/complete" element={<ClaimComplete />} />
            <Route path="/session-expired" element={<SessionExpired />} />
            <Route path="/test-data" element={<TestData />} />
            <Route path="/*" element={<NotFound />} />
          </Routes>
        </Layout>
      </BrowserRouter>
    </QueryClientProvider>
  )
}
