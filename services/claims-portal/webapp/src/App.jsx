import { BrowserRouter, Routes, Route } from 'react-router'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import 'react-widgets/styles.css'
import './public/favicon.ico'
import './styles.scss'
import Layout from './Layout.jsx'
import ClaimComplete from './routes/ClaimComplete'
import LandingPage from './routes/LandingPage/index.jsx'
import GetStarted from './routes/GetStarted/index.jsx'
import AdditionalInfo from './routes/AdditionalInfo/index.jsx'
import Verification from './routes/Verification/index.jsx'
import VerificationFailed from './routes/VerificationFailed/index.jsx'
import NotFound from './routes/NotFound.jsx'
import QueryError from './query.js'

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      retry: (retryAttempts, error) => {
        if (retryAttempts >= 1) { return false }
        if (error instanceof QueryError) {
          return error.status >= 500
        }
        return true
      },
    },
    mutations: {
      retry: (retryAttempts, error) => {
        if (retryAttempts >= 1) { return false }
        if (error instanceof QueryError) {
          return error.status >= 500
        }
        return true
      },
    },
  },
})

export default function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <Layout>
          <Routes>
            <Route index element={<LandingPage />} />
            <Route path="/get-started" element={<GetStarted />} />
            <Route path="/additional-information" element={<AdditionalInfo />} />
            <Route path="/verification" element={<Verification />} />
            <Route path="/verification-failed" element={<VerificationFailed />} />
            <Route path="/claim-complete" element={<ClaimComplete />} />
            <Route path="/*" element={<NotFound />} />
          </Routes>
        </Layout>
      </BrowserRouter>
    </QueryClientProvider>
  )
}
