import PropTypes from 'prop-types'
import TextInput from '../TextInput/index.jsx'

const nonDigitRegex = /\D/g

/**
 * @typedef NumericInputProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the input is disabled
 * @property {string} [errorText=''] Error message to display
 * @property {string} [helpText=''] Help text to display
 * @property {string} id HTML ID for the input element
 * @property {string} label Label text for the input
 * @property {number} [maxLength] Maximum length of the input value
 * @property {string} [placeholder=''] Placeholder text for the input
 * @property {boolean} [readonly=false] Whether the input is read-only
 * @property {boolean} [required=false] Whether the input is required
 * @property {string} [tooltip=''] Tooltip text for the label
 * @property {string} [value] Input value
 * @property {(value: string) => void} [onChange] Change event handler
 */

/**
 * @param {NumericInputProps} props
 */
export default function NumericInput({
  className,
  disabled = false,
  errorText = '',
  helpText = '',
  id,
  label,
  maxLength,
  placeholder = '',
  readonly = false,
  required = false,
  tooltip = '',
  value,
  onChange,
}) {
  return (
    <TextInput
      className={className}
      disabled={disabled}
      errorText={errorText}
      helpText={helpText}
      id={id}
      label={label}
      maxLength={maxLength}
      placeholder={placeholder}
      readonly={readonly}
      removeCharRegex={nonDigitRegex}
      required={required}
      tooltip={tooltip}
      value={value}
      onChange={onChange}
    />
  )
}

NumericInput.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  errorText: PropTypes.string,
  helpText: PropTypes.string,
  id: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  maxLength: PropTypes.number,
  placeholder: PropTypes.string,
  readonly: PropTypes.bool,
  required: PropTypes.bool,
  tooltip: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
}
