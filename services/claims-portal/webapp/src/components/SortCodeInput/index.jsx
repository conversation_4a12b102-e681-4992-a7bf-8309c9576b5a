import { useState } from 'react'
import PropTypes from 'prop-types'
import TextInput from '../TextInput/index.jsx'

const nonDigitRegex = /\D/g

function formatSortCode(value) {
  let formatted = ''
  if (typeof value !== 'string') {
    return formatted
  }
  for (let i = 0; i < value.length; i += 1) {
    if (i !== 0 && i % 2 === 0) {
      formatted += '-'
    }
    formatted += value[i]
  }
  return formatted
}

/**
 * @typedef SortCodeInputProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the input is disabled
 * @property {string} [errorText=''] Error message to display
 * @property {string} [helpText=''] Help text to display
 * @property {string} id HTML ID for the input element
 * @property {string} label Label text for the input
 * @property {boolean} [readonly=false] Whether the input is read-only
 * @property {boolean} [required=false] Whether the input is required
 * @property {string} [tooltip=''] Tooltip text for the label
 * @property {string} [value] Input value
 * @property {(value: string) => void} [onChange] Change event handler
 */

/**
 * @param {SortCodeInputProps} props
 */
export default function SortCodeInput({
  className,
  disabled = false,
  errorText = '',
  helpText = '',
  id,
  label,
  readonly = false,
  required = false,
  tooltip = '',
  value,
  onChange,
}) {
  const [isFocused, setIsFocused] = useState(false)

  return (
    <TextInput
      className={className}
      disabled={disabled}
      errorText={errorText}
      helpText={helpText}
      id={id}
      label={label}
      maxLength={6}
      readonly={readonly}
      removeCharRegex={nonDigitRegex}
      required={required}
      tooltip={tooltip}
      value={isFocused ? value : formatSortCode(value)}
      onBlur={() => setIsFocused(false)}
      onChange={isFocused ? onChange : undefined}
      onFocus={() => setIsFocused(true)}
    />
  )
}

SortCodeInput.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  errorText: PropTypes.string,
  helpText: PropTypes.string,
  id: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  readonly: PropTypes.bool,
  required: PropTypes.bool,
  tooltip: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
}
