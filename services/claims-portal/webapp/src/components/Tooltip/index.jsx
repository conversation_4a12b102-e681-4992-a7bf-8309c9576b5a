// eslint-disable-next-line import/no-unresolved
import { Popover } from '@base-ui-components/react/popover'
import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './Tooltip.module.scss'

function ArrowSvg() {
  return (
    <svg width="20" height="10" viewBox="0 0 20 10" fill="none">
      <path
        d="M9.66437 2.60207L4.80758 6.97318C4.07308 7.63423 3.11989 8 2.13172 8H0V10H20V8H18.5349C17.5468 8 16.5936 7.63423 15.8591 6.97318L11.0023 2.60207C10.622 2.2598 10.0447 2.25979 9.66437 2.60207Z"
        className={styles.arrowFill}
      />
      <path
        d="M8.99542 1.85876C9.75604 1.17425 10.9106 1.17422 11.6713 1.85878L16.5281 6.22989C17.0789 6.72568 17.7938 7.00001 18.5349 7.00001L15.89 7L11.0023 2.60207C10.622 2.2598 10.0447 2.2598 9.66436 2.60207L4.77734 7L2.13171 7.00001C2.87284 7.00001 3.58774 6.72568 4.13861 6.22989L8.99542 1.85876Z"
        className={styles.arrowStroke}
      />
    </svg>
  )
}

/**
 * @typedef TooltipProps
 * @property {React.ReactNode} children Content to have a tooltip
 * @property {string} [className] Additional CSS classes to apply
 * @property {string} text Tooltip text to display
 */

/**
 * @param {TooltipProps} props
 */
export default function Tooltip({
  children,
  className,
  text,
}) {
  const classes = clsx(
    styles.button,
    className,
  )
  return (
    <Popover.Root>
      <Popover.Trigger className={classes} aria-label={text}>
        {children}
      </Popover.Trigger>
      <Popover.Portal>
        <Popover.Positioner sideOffset={10} side="top" align="start" alignOffset={-10}>
          <Popover.Popup className={styles.popup}>
            <Popover.Arrow className={styles.arrow}>
              <ArrowSvg />
            </Popover.Arrow>
            {text}
          </Popover.Popup>
        </Popover.Positioner>
      </Popover.Portal>
    </Popover.Root>
  )
}

Tooltip.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  text: PropTypes.string.isRequired,
}
