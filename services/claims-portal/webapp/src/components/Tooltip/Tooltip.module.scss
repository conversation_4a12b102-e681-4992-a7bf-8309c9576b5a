@use '../../public/css/colours.scss' as colours;
@use '../../public/css/fonts.scss' as fonts;
@use '../../public/css/shadows.scss' as shadows;

.button {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  margin: 0;
  outline: 0;
  border: 0;
  background-color: transparent;
  user-select: none;

  // &:focus-within {
  //   outline: 1px solid colours.$border-hover;
  //   outline-offset: 1px;
  // }
}

.popup {
  background-color: colours.$surface-floating;
  border-radius: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  max-width: 288px;
  outline: 1px solid colours.$border-soft;
  padding: 16px;
  transform-origin: var(--transform-origin);
  transition: transform 150ms, opacity 150ms;

  @include shadows.elevation-floating();

  &[data-starting-style],
  &[data-ending-style] {
    opacity: 0;
    transform: scale(0.9);
  }

  &[data-instant] {
    transition-duration: 0ms;
  }
}

.arrow {
  display: flex;

  &[data-side="top"] {
    bottom: -8px;
    rotate: 180deg;
  }

  &[data-side="bottom"] {
    top: -8px;
    rotate: 0deg;
  }

  &[data-side="left"] {
    right: -13px;
    rotate: 90deg;
  }

  &[data-side="right"] {
    left: -13px;
    rotate: -90deg;
  }
}

.arrowFill {
  fill: colours.$surface-floating;
}

.arrowStroke {
  fill: colours.$border-soft;
}
