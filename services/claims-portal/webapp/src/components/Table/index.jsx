import clsx from 'clsx'
import PropTypes from 'prop-types'
import { useReactTable, getCoreRowModel, flexRender } from '@tanstack/react-table'
import styles from './Table.module.scss'

/**
 * @typedef TableProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {import('@tanstack/react-table').ColumnDef<string>[]} columns
 * @property {any[]} data Data to be displayed in the table
 */

/**
 * @param {TableProps} props
 */
export default function Table({ className, columns, data }) {
  const table = useReactTable({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
  })

  const classes = clsx(
    styles.table,
    className,
  )
  return (
    <table className={classes}>
      <thead>
        {table.getHeaderGroups().map(headerGroup => (
          <tr key={headerGroup.id}>
            {headerGroup.headers.map(header => (
              <th key={header.id}>
                {header.isPlaceholder ? null : flexRender(
                  header.column.columnDef.header,
                  header.getContext(),
                )}
              </th>
            ))}
          </tr>
        ))}
      </thead>
      <tbody>
        {table.getRowModel().rows.map(row => (
          <tr key={row.id}>
            {row.getVisibleCells().map(cell => (
              <td key={cell.id}>
                {flexRender(cell.column.columnDef.cell, cell.getContext())}
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  )
}

Table.propTypes = {
  className: PropTypes.string,
  columns: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  data: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
}
