import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './IconButton.module.scss'

const preventDefault = (e) => { e.preventDefault() }

/**
 * @typedef IconButtonProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {React.ReactNode} children Icon to display
 * @property {number} [tabIndex] Button tab index
 * @property {React.MouseEventHandler<HTMLButtonElement>} [onClick] Click event handler
 */

/**
 * @param {IconButtonProps} props
 */
export default function IconButton({
  className,
  children,
  tabIndex,
  onClick,
}) {
  const classes = clsx(
    styles.button,
    typeof onClick === 'function' && styles.clickable,
    className,
  )
  return (
    <button
      type="button"
      className={classes}
      tabIndex={tabIndex}
      onClick={onClick}
      onMouseDown={preventDefault}
      onMouseUp={preventDefault}
    >
      {children}
    </button>
  )
}

IconButton.propTypes = {
  className: PropTypes.string,
  children: PropTypes.node.isRequired,
  tabIndex: PropTypes.number,
  onClick: PropTypes.func,
}
