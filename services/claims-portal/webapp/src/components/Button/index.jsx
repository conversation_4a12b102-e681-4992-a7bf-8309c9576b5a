import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './Button.module.scss'

export const noop = () => {}

/**
 * @typedef ButtonProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {'primary' | 'secondary'} [type='primary'] Button display type
 * @property {boolean} [disabled=false] Whether the button is disabled
 * @property {boolean} [submit=false] Whether the button is a submit button
 * @property {React.ReactNode} children Content to display inside the button
 * @property {React.MouseEventHandler<HTMLButtonElement>} [onClick] Click event handler
 */

/**
 * @param {ButtonProps} props
 */
export default function Button({
  className,
  type = 'primary',
  disabled = false,
  submit = false,
  children,
  onClick,
}) {
  const classes = clsx(
    type === 'primary' && styles.primary,
    type === 'secondary' && styles.secondary,
    disabled && styles.disabled,
    className,
  )
  return (
    <button
      type={submit ? 'submit' : 'button'}
      className={classes}
      onClick={disabled ? noop : onClick}
      aria-disabled={disabled}
    >
      {children}
    </button>
  )
}

Button.propTypes = {
  className: PropTypes.string,
  type: PropTypes.oneOf(['primary', 'secondary']),
  disabled: PropTypes.bool,
  submit: PropTypes.bool,
  children: PropTypes.node.isRequired,
  onClick: PropTypes.func,
}
