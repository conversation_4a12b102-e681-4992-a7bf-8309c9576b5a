@use '../../public/css/colours.scss' as colours;
@use '../../public/css/fonts.scss' as fonts;
@use '../../public/css/shadows.scss' as shadows;

@keyframes ripple {
  from {
    opacity: 1;
    transform: scale(0.5);
  }
  to {
    opacity: 0;
    transform: scale(5);
  }
}

%disabled-button {
  background-color: colours.$action-disabled;
  color: colours.$action-on-disabled;
  outline: none;
  cursor: not-allowed;
}

%button {
  align-items: center;
  border: none;
  border-radius: 50px;
  box-shadow: 0px 1px 6px rgba(163, 163, 163, 0.4);
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  font-family: fonts.$microtext-bold;
  font-size: 16px;
  font-weight: 700;
  gap: 8px;
  justify-content: center;
  line-height: 24px;
  max-height: 48px;
  min-height: 48px;
  min-width: 136px;
  overflow: hidden;
  padding: 0 20px;
  position: relative;
  text-align: center;
  text-decoration: none;

  &::after {
    display: none;
    content: "";
    position: absolute;
    border-radius: 50%;
    background-color: colours.$surface-state-pressed;
    
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    
    /* Center the ripple */
    top: 50%;
    left: 50%;
  
    animation: ripple 1s;
    opacity: 0;
  }

  &:focus-visible:not(.disabled) {
    outline: none;

    @include shadows.elevation-raised-focused();
  }

  &.disabled {
    @extend %disabled-button;
  }
}

%primaryButton {
  @extend %button;

  background-color: colours.$action-main-default;
  color: colours.$action-on-main-default;
  
  &:hover:not(.disabled) {
    background-color: colours.$action-main-hover;
    color: colours.$action-on-main-hover;
  }
}

%secondaryButton {
  @extend %button;

  background-color: colours.$button-secondary-default;
  color: colours.$button-on-secondary-default;

  &:hover:not(.disabled) {
    background-color: colours.$button-secondary-hover;
    color: colours.$button-on-secondary-hover;
  }
}

.primary {
  @extend %primaryButton;
}

.secondary {
  @extend %secondaryButton;
}
