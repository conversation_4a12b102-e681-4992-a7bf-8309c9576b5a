@use '../../public/css/colours.scss' as colours;
@use '../../public/css/fonts.scss' as fonts;

@keyframes ripple {
  from {
    opacity: 1;
    transform: scale(0.5);
  }
  to {
    opacity: 0;
    transform: scale(5);
  }
}

%disabled-button {
  background-color: colours.$surface-disabled;
  color: colours.$text-disabled;
  outline: none;
  cursor: not-allowed;
}

%button {
  align-items: center;
  border: none;
  border-radius: 50px;
  box-shadow: 0px 1px 6px rgba(163, 163, 163, 0.4);
  box-sizing: border-box;
  cursor: pointer;
  display: flex;
  flex-direction: row;
  font-family: fonts.$microtext-bold;
  font-size: 16px;
  font-weight: 700;
  gap: 8px;
  justify-content: center;
  line-height: 24px;
  max-height: 48px;
  min-height: 48px;
  min-width: 136px;
  overflow: hidden;
  padding: 0 20px;
  position: relative;
  text-align: center;
  text-decoration: none;

  &::after {
    display: none;
    content: "";
    position: absolute;
    border-radius: 50%;
    background-color: rgba(34, 34, 34, 0.1);
    
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -50px;
    
    /* Center the ripple */
    top: 50%;
    left: 50%;
  
    animation: ripple 1s;
    opacity: 0;
  }

  &:focus-visible:not(.disabled) {
    outline: solid 2px colours.$blue;
    outline-offset: 2px;
  }

  &.disabled {
    @extend %disabled-button;
  }
}

%primaryButton {
  @extend %button;

  background-color: colours.$red;
  color: colours.$white;
  
  &:hover:not(.disabled) {
    background-color: colours.$medium-red;
    color: colours.$white;
  }
}

%secondaryButton {
  @extend %button;

  background-color: colours.$white;
  color: colours.$red;

  &:hover:not(.disabled) {
    background-color: colours.$off-white;
    color: colours.$medium-red;
  }
}

.primary {
  @extend %primaryButton;
}

.secondary {
  @extend %secondaryButton;
}
