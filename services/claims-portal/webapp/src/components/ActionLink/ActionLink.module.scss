@use '../../public/css/colours.scss' as colours;
@use '../../public/css/fonts.scss' as fonts;

.button {
  align-items: center;
  appearance: none;
  background-color: transparent;
  border: none;
  border-radius: 2px;
  box-sizing: border-box;
  color: colours.$action-alternative-default;
  cursor: pointer;
  display: flex;
  font-family: fonts.$microtext-bold;
  font-size: 14px;
  justify-content: center;
  line-height: 20px;
  padding: 0;

  &:hover, &:active {
    color: colours.$action-alternative-hover;
  }

  &:focus-visible {
    box-shadow: 0px 0px 0px 2px #FFFFFF, 0px 0px 0px 4px #3366FF;
    outline: none;
  }
}

.disabled {
  color: colours.$action-on-disabled;
  cursor: not-allowed;
}
