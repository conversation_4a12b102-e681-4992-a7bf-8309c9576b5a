@use '../../public/css/colours.scss' as colours;
@use '../../public/css/fonts.scss' as fonts;
@use '../../public/css/shadows.scss' as shadows;

.button {
  align-items: center;
  appearance: none;
  background-color: transparent;
  border: none;
  border-radius: 2px;
  box-sizing: border-box;
  color: colours.$action-alternative-default;
  cursor: pointer;
  display: flex;
  font-family: fonts.$microtext-bold;
  font-size: 14px;
  justify-content: center;
  line-height: 20px;
  padding: 0;

  &:hover:not(.disabled), &:active:not(.disabled) {
    color: colours.$action-alternative-hover;
  }

  &:focus-visible {
    box-shadow: 0px 0px 0px 2px colours.$effect-focus-inner, 0px 0px 0px 4px colours.$effect-focus-outer;
    outline: none;
  }
}

.link {
  border: none;
  border-radius: 2px;
  color: colours.$action-alternative-default;
  cursor: pointer;
  font-family: fonts.$microtext-bold;
  font-size: 14px;
  justify-content: center;
  line-height: 20px;

  &:hover:not(.disabled), &:active:not(.disabled) {
    color: colours.$action-alternative-hover;
  }

  &:focus-visible {
    outline: none;

    @include shadows.elevation-flat-focus-outer();
  }
}

.disabled {
  color: colours.$action-on-disabled;
  cursor: not-allowed;
}
