import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './ActionLink.module.scss'
import { noop } from '../Button/index.jsx'

/**
 * @typedef ActionLinkProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the button is disabled
 * @property {'button' | 'a'} [element='button'] Element type to render ('button' or 'a')
 * @property {string} [href] URL to link to if element is 'a'
 * @property {React.ReactNode} children Content to display inside the button
 * @property {import('react').MouseEventHandler<HTMLButtonElement>} [onClick] Click event handler
 */

/**
 * @param {ActionLinkProps} props
 */
export default function ActionLink({
  children,
  className,
  disabled = false,
  element = 'button',
  href,
  onClick,
}) {
  if (element === 'button') {
    const classes = clsx(
      styles.button,
      disabled && styles.disabled,
      className,
    )
    return (
      <button
        type="button"
        className={classes}
        onClick={disabled ? noop : onClick}
        aria-disabled={disabled}
      >
        {children}
      </button>
    )
  }
  if (element === 'a') {
    const classes = clsx(
      styles.link,
      disabled && styles.disabled,
      className,
    )
    return (
      <a
        href={href}
        className={classes}
        onClick={(e) => { if (disabled) { e.preventDefault() } }}
        aria-disabled={disabled}
      >
        {children}
      </a>
    )
  }
  return null
}

ActionLink.propTypes = {
  children: PropTypes.node.isRequired,
  className: PropTypes.string,
  disabled: PropTypes.bool,
  element: PropTypes.oneOf(['button', 'a']),
  href: PropTypes.string,
  onClick: PropTypes.func,
}
