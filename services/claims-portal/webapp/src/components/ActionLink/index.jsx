import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './ActionLink.module.scss'
import { noop } from '../Button/index.jsx'

/**
 * @typedef ActionLinkProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the button is disabled
 * @property {'button' | 'a'} [element='button'] Element type to render ('button' or 'a')
 * @property {string} [href] URL to link to if element is 'a'
 * @property {string} text Text content
 * @property {import('react').MouseEventHandler<HTMLButtonElement>} [onClick] Click event handler
 */

/**
 * @param {ActionLinkProps} props
 */
export default function ActionLink({
  className,
  disabled = false,
  // element = 'button',
  // href,
  text,
  onClick,
}) {
  const classes = clsx(
    styles.container,
    className,
  )
  return (
    <div className={classes}>
      <button
        type="button"
        className={clsx(styles.button, disabled && styles.disabled)}
        onClick={disabled ? noop : onClick}
        aria-disabled={disabled}
      >
        {text}
      </button>
    </div>
  )
}

ActionLink.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  // element: PropTypes.oneOf(['button', 'a']),
  // href: PropTypes.string,
  text: PropTypes.string.isRequired,
  onClick: PropTypes.func,
}
