import PropTypes from 'prop-types'
import Stepper from './Stepper/index.jsx'

const stepNames = [
  'Verification',
  'Contact Details',
  'Loans and Offer',
  'Payment Confirmation',
]

/**
 * @param {{ className?: string, step: number }} props
 */
export default function ClaimsStepper({ className, step }) {
  const steps = stepNames.map((name, index) => ({
    label: name,
    active: index === step,
    completed: index < step,
  }))
  return <Stepper className={className} steps={steps} />
}

ClaimsStepper.propTypes = {
  className: PropTypes.string,
  step: PropTypes.number.isRequired,
}
