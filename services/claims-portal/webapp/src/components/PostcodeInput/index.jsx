import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './PostcodeInput.module.scss'
import TextInput from '../TextInput/index.jsx'

const cleanPostcodeRegex = /[^a-zA-Z0-9 ]|[ ]{2,}/g

/**
 * @typedef PostcodeInputProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the input is disabled
 * @property {string} [errorText=''] Error message to display
 * @property {string} [helpText=''] Help text to display
 * @property {string} id HTML ID for the input element
 * @property {string} label Label text for the input
 * @property {string} [placeholder=''] Placeholder text for the input
 * @property {boolean} [readonly=false] Whether the input is read-only
 * @property {boolean} [required=false] Whether the input is required
 * @property {string} [tooltip=''] Tooltip text for the label
 * @property {string} [value] Input value
 * @property {(value: string) => void} [onChange] Change event handler
 */

/**
 * @param {PostcodeInputProps} props
 */
export default function PostcodeInput({
  className,
  disabled = false,
  errorText = '',
  helpText = '',
  id,
  label,
  placeholder = '',
  readonly = false,
  required = false,
  tooltip = '',
  value,
  onChange,
}) {
  return (
    <TextInput
      className={clsx(styles.postcodeInput, className)}
      disabled={disabled}
      errorText={errorText}
      helpText={helpText}
      id={id}
      label={label}
      maxLength={8}
      placeholder={placeholder}
      readonly={readonly}
      removeCharRegex={cleanPostcodeRegex}
      required={required}
      tooltip={tooltip}
      value={value}
      onChange={onChange}
    />
  )
}

PostcodeInput.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  errorText: PropTypes.string,
  helpText: PropTypes.string,
  id: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  readonly: PropTypes.bool,
  required: PropTypes.bool,
  tooltip: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
}
