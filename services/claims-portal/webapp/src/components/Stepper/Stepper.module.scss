@use '../../public/css/colours.scss' as colours;
@use '../../public/css/fonts.scss' as fonts;

.container {
  min-height: 64px;
  width: 100%;
}

.stepper {
  align-items: flex-start;
  display: flex;
  flex-direction: row;
  margin: 0;
  padding: 0;
}

.step {
  align-items: center;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 12px;
  justify-content: center;
  min-height: 64px;

  .stepTrack {
    align-items: center;
    display: flex;
    flex-direction: row;
    gap: 4px;
    height: 24px;
    justify-content: center;
    padding: 0;
    width: 100%;
  }

  .track {
    background-color: colours.$border-alternative;
    border-radius: 50px 0 0 50px;
    flex-grow: 1;
    height: 2px;
  }

  .dot {
    align-items: center;
    background-color: colours.$surface-flat;
    border: 2px solid colours.$border-alternative;
    border-radius: 50%;
    display: flex;
    flex-direction: row;
    height: 32px;
    justify-content: center;
    padding: 4px;
    width: 32px;
  }

  .index {
    align-items: center;
    color: colours.$text-default;
    display: flex;
    flex-direction: row;
    font-family: fonts.$microtext-bold;
    font-size: 16px;
    height: 24px;
    justify-content: center;
    line-height: 24px;
    text-align: center;
    width: 24px;
  }

  .label {
    align-self: stretch;
    color: colours.$text-default;
    font-family: fonts.$microtext-rg;
    font-size: 16px;
    height: 24px;
    line-height: 24px;
    text-align: center;
  }

  &.active {
    .dot {
      background-color: colours.$action-main-selected;
      border: none;
    }

    .index {
      color: colours.$action-on-main-selected;
    }

    .label {
      font-family: fonts.$microtext-bold;
    }
  }

  &.completed {
    .track {
      background-color: colours.$red-65;
    }

    & + .step:not(.completed) {
      .track:first-child {
        background-color: colours.$red-65;
      }
    }

    .dot {
      background-color: colours.$neutral-00;
      border: 2px solid colours.$red-65;
      height: 24px;
      padding: 0;
      width: 24px;
    }
  }

  &:first-child {
    .track:first-child {
      background-color: transparent;
    }
  }

  &:last-child {
    .track:last-child {
      background-color: transparent;
    }
  }
}
