import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './Stepper.module.scss'

function CompletedIcon() {
  return (
    <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M6.2788 12.1326C6.65053 11.762 7.25323 11.762 7.62496 12.1326L10.3173 14.8163L16.375 8.77791C16.7468 8.40736 17.3495 8.40736 17.7212 8.77791C18.0929 9.14845 18.0929 9.74922 17.7212 10.1198L10.3173 17.5L6.2788 13.4744C5.90707 13.1039 5.90707 12.5031 6.2788 12.1326Z" fill="#990000" />
    </svg>
  )
}

/**
 * @typedef Step
 * @property {string} label Label for the step
 * @property {boolean} [active=false] Whether the step is currently active
 * @property {boolean} [completed=false] Whether the step has been completed
 */

/**
 * @typedef StepperProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {Step[]} steps Steps to display with their state
 */

/**
 * @param {StepperProps} props
 */
export default function Stepper({
  className,
  steps,
}) {
  const classes = clsx(
    styles.container,
    className,
  )

  return (
    <div className={classes}>
      <ul className={styles.stepper}>
        {steps.map((step, index) => {
          const stepClasses = clsx(
            styles.step,
            step.active && styles.active,
            step.completed && styles.completed,
          )
          let ariaLabel
          if (step.completed) {
            ariaLabel = 'completed'
          } else if (step.active) {
            ariaLabel = 'active'
          }
          return (
            <li
              key={step.label}
              className={stepClasses}
              aria-label={ariaLabel}
            >
              <div className={styles.stepTrack}>
                <div className={styles.track} />
                <div className={styles.dot}>
                  {step.completed ? (
                    <CompletedIcon />
                  ) : (
                    <span className={styles.index}>{index + 1}</span>
                  )}
                </div>
                <div className={styles.track} />
              </div>
              <span className={styles.label}>{step.label}</span>
            </li>
          )
        })}
      </ul>
    </div>
  )
}

Stepper.propTypes = {
  className: PropTypes.string,
  steps: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string.isRequired,
    active: PropTypes.bool,
    completed: PropTypes.bool,
  })).isRequired,
}
