import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './ButtonGroup.module.scss'

/**
 * @typedef ButtonGroupProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {'inline' | 'stacked'} [type='inline'] Button group display type
 * @property {React.ReactNode} children Content to display inside the button group
 */

/**
 * @param {ButtonGroupProps} props
 */
export default function ButtonGroup({ className, type = 'inline', children }) {
  const classes = clsx(
    type === 'inline' && styles.inline,
    type === 'stacked' && styles.stacked,
    className,
  )
  return (
    <div className={classes}>
      {children}
    </div>
  )
}

ButtonGroup.propTypes = {
  className: PropTypes.string,
  type: PropTypes.oneOf(['inline', 'stacked']),
  children: PropTypes.node.isRequired,
}
