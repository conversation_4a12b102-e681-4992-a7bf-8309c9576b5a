import PropTypes from 'prop-types'
import TextInput from '../TextInput/index.jsx'

const dateInvalidCharRegex = /[^\d/]/g
const dateFormatRegex = /^\d{1,2}\/\d{4}$/
const dateNumericFormatRegex = /^\d{6}$/

/**
 * @param {string} value
 */
function autoFormatValue(value) {
  if (dateFormatRegex.test(value)) {
    const [month, year] = value.split('/')
    return `${month.padStart(2, '0')}/${year}`
  }
  if (dateNumericFormatRegex.test(value)) {
    const month = value.slice(0, 2)
    const year = value.slice(2, 6)
    return `${month}/${year}`
  }
  return value
}

/**
 * @typedef MonthYearInputProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the input is disabled
 * @property {string} [errorText=''] Error message to display
 * @property {string} [helpText=''] Help text to display
 * @property {string} id HTML ID for the input element
 * @property {string} label Label text for the input
 * @property {string} [placeholder='MM/YYYY'] Placeholder text for the input
 * @property {boolean} [readonly=false] Whether the input is read-only
 * @property {boolean} [required=false] Whether the input is required
 * @property {string} [tooltip=''] Tooltip text for the label
 * @property {string} [value=''] Input value
 * @property {(value: string) => void} [onChange] Change event handler
 */

/**
 * @param {MonthYearInputProps} props
 */
export default function MonthYearInput({
  className,
  disabled = false,
  errorText = '',
  helpText = '',
  id,
  label,
  placeholder = 'MM/YYYY',
  readonly = false,
  required = false,
  tooltip = '',
  value = '',
  onChange,
}) {
  const handleBlur = () => {
    const formatted = autoFormatValue(value)
    if (formatted !== value) {
      onChange(formatted)
    }
  }

  return (
    <TextInput
      className={className}
      disabled={disabled}
      errorText={errorText}
      helpText={helpText}
      id={id}
      label={label}
      maxLength={7}
      placeholder={placeholder}
      readonly={readonly}
      removeCharRegex={dateInvalidCharRegex}
      required={required}
      tooltip={tooltip}
      value={value}
      onBlur={handleBlur}
      onChange={onChange}
    />
  )
}

MonthYearInput.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  errorText: PropTypes.string,
  helpText: PropTypes.string,
  id: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  readonly: PropTypes.bool,
  required: PropTypes.bool,
  tooltip: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
}
