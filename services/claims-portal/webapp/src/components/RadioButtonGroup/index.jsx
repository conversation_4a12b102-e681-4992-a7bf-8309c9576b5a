import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './RadioButtonGroup.module.scss'
import Tooltip from '../Tooltip/index.jsx'
import InfoIcon from '../../icons/Info.jsx'
import ErrorIcon from '../../icons/Error.jsx'

/**
 * @typedef Option
 * @property {string} label The label to display for the option
 * @property {string} value The value of the option
 */

/**
 * @typedef RadioButtonGroupProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the input is disabled
 * @property {string} [errorText=''] Error message to display
 * @property {string} [helpText=''] Help text to display
 * @property {string} label Label text for the input
 * @property {string} name Radio Button Group name
 * @property {Option[]} options List of options for the radio buttons
 * @property {boolean} [readonly=false] Whether the input is read-only
 * @property {boolean} [required=false] Whether the input is required
 * @property {string} [tooltip=''] Tooltip text for the label
 * @property {string} [value] Input value
 * @property {boolean} [wide=false] Increase width for longer labels
 * @property {(value: string) => void} [onChange] Change event handler
 */

/**
 * @param {RadioButtonGroupProps} props
 */
export default function RadioButtonGroup({
  className,
  disabled = false,
  errorText = '',
  helpText = '',
  label,
  name,
  options,
  readonly = false,
  required = false,
  tooltip = '',
  value,
  wide = false,
  onChange,
}) {
  const showTooltip = typeof tooltip === 'string' && tooltip.length > 0
  const showError = typeof errorText === 'string' && errorText.length > 0
  const showHelpText = !showError && typeof helpText === 'string' && helpText.length > 0

  const layoutClasses = clsx(
    styles.layout,
    disabled && styles.disabled,
    wide && styles.wide,
    className,
  )
  const inputClasses = clsx(
    styles.radio,
    readonly && styles.readonly,
    showError && styles.error,
  )

  /** @type {React.ChangeEventHandler<HTMLInputElement>} */
  const handleChange = (event) => {
    if (typeof onChange === 'function' && !readonly && !disabled) {
      onChange(event.target.value)
    }
  }

  return (
    <div className={layoutClasses}>
      <div className={styles.labelContainer}>
        <label className={styles.label} htmlFor={name}>
          {label}
        </label>
        {showTooltip ? (
          <Tooltip className={styles.infoIcon} text={tooltip}>
            <InfoIcon />
          </Tooltip>
        ) : null}
      </div>
      <div className={styles.inputContainer} role="radiogroup" id={name} aria-readonly={readonly} aria-required={required}>
        {options.map(option => (
          <div key={option.value} className={styles.radioContainer}>
            <input
              checked={value === option.value}
              className={inputClasses}
              id={option.value}
              name={name}
              tabIndex={readonly ? -1 : undefined}
              type="radio"
              value={option.value}
              onChange={handleChange}
              aria-checked={value === option.value}
              aria-disabled={disabled}
            />
            <label className={styles.radioLabel} htmlFor={option.value}>{option.label}</label>
          </div>
        ))}
      </div>
      <div className={styles.helperTextContainer}>
        {showError ? (
          <div className={styles.errorTextContainer}>
            <div className={styles.errorIcon}>
              <ErrorIcon />
            </div>
            <span className={styles.errorText}>{errorText}</span>
          </div>
        ) : null}
        {showHelpText ? (
          <span className={styles.helpText}>{helpText}</span>
        ) : null}
      </div>
    </div>
  )
}

RadioButtonGroup.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  errorText: PropTypes.string,
  helpText: PropTypes.string,
  label: PropTypes.string.isRequired,
  name: PropTypes.string.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired,
    }),
  ).isRequired,
  readonly: PropTypes.bool,
  required: PropTypes.bool,
  tooltip: PropTypes.string,
  value: PropTypes.string,
  wide: PropTypes.bool,
  onChange: PropTypes.func,
}
