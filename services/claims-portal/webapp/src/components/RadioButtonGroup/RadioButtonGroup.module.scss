@use '../../public/css/colours.scss' as colours;
@use '../../public/css/fonts.scss' as fonts;
@use '../../public/css/shadows.scss' as shadows;

.layout {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 4px;
  // min-height: 100px;
  padding: 0;
  width: 100%;

  @media screen and (min-width: 768px) {
    width: 288px;

    &.wide {
      width: 400px;
    }
  }
}

.labelContainer {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-grow: 0;
  gap: 4px;
  min-height: 24px;
  padding: 0px;
  width: 100%;
}

.label {
  color: colours.$text-default;
  flex-grow: 1;
  font-family: fonts.$microtext-rg;
  font-size: 16px;
  line-height: 24px;
}

.infoIcon {
  flex: none;
  flex-grow: 0;
  height: 24px;
  width: 24px;
}

.inputContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 12px 0;
  position: relative;
  width: 100%;
}

.radioContainer {
  align-items: start;
  display: flex;
  flex-direction: row;
  min-height: 24px;
  width: 100%;
}

.radio {
  appearance: none;
  background-color: colours.$surface-flat;
  border: 1px solid colours.$border-default;
  border-radius: 50%;
  box-sizing: border-box;
  color: colours.$text-default;
  height: 24px;
  margin: auto 0;
  padding: 0;
  position: relative;
  width: 24px;

  &:hover {
    border-color: colours.$border-hover;
  }

  &:checked {
    border: 2px solid colours.$action-alternative-selected;

    &::before {
      background-color: colours.$action-alternative-selected;
      border-radius: 50%;
      content: '';
      height: 12px;
      left: 4px;
      position: absolute;
      top: 4px;
      width: 12px;
    }

    &.readonly::before {
      background-color: colours.$neutral-55;
    }

    &:focus-visible {
      &::after {
        left: -6px;
        top: -6px;
      }
    }
  }

  &.readonly {
    background-color: colours.$surface-disabled;
    border-color: colours.$border-disabled;
    pointer-events: none;
  }

  &.error {
    border-color: colours.$border-error;

    &:checked {
      border-width: 2px;

      &::before {
        background-color: colours.$border-error;
      }
    }
  }

  &:focus-visible {
    outline: none;

    &::after {
      background: transparent;
      border: 2px solid colours.$effect-focus-outer;
      border-radius: 50%;
      content: '';
      height: 32px;
      left: -5px;
      position: absolute;
      top: -5px;
      width: 32px;
    }
  }

  &.readonly {
    background-color: colours.$surface-disabled;
    color: colours.$text-default;
  }
}

.radioLabel {
  color: colours.$text-default;
  font-family: fonts.$microtext-rg;
  font-size: 16px;
  line-height: 24px;
  padding-left: 8px;
  text-align: left;
  width: calc(100% - 24px);
}

.helperTextContainer {
  align-self: stretch;
  flex: none;
  flex-grow: 0;
  min-height: 20px;
  width: 100%;
}

.helpText {
  color: colours.$text-soft;
  font-family: fonts.$microtext-rg;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  width: 100%;
}

.errorTextContainer {
  align-items: flex-start;
  display: flex;
  flex-direction: row;
  gap: 4px;
  padding: 0px;
  width: 100%;
}

.errorIcon {
  flex: none;
  flex-grow: 0;
  height: 20px;
  width: 20px;
}

.errorText {
  align-self: stretch;
  color: colours.$text-error;
  flex: none;
  flex-grow: 1;
  font-family: fonts.$microtext-rg;
  font-size: 14px;
  min-height: 20px;
  line-height: 20px;
  width: calc(100% - 24px);
}

.layout.disabled {
  .label {
    color: colours.$text-disabled;
  }

  .infoIcon {
    path {
      fill: colours.$text-disabled;
    }
  }

  .radio {
    background-color: colours.$surface-disabled;
    border: none;
    color: colours.$text-disabled;
    cursor: not-allowed;
  }

  .helpText {
    color: colours.$text-disabled;
  }
}
