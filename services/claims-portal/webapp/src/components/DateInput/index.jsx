import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './DateInput.module.scss'
import IconButton from '../IconButton/index.jsx'
import Tooltip from '../Tooltip/index.jsx'
import ClearIcon from '../../icons/Clear.jsx'
import InfoIcon from '../../icons/Info.jsx'
import ErrorIcon from '../../icons/Error.jsx'

const dateInvalidCharRegex = /[^\d/]/g
const dateFormatRegex = /^\d{1,2}\/\d{1,2}\/\d{4}$/
const dateNumericFormatRegex = /^\d{8}$/

/**
 * @param {Date | null} date
 */
function formatDate(date) {
  if (date.toString() === 'Invalid Date') return ''
  return date.toLocaleDateString('en-GB', { dateStyle: 'short' })
}

/**
 * @param {string} value
 */
function parseDateString(value) {
  if (dateFormatRegex.test(value)) {
    const [day, month, year] = value.split('/')
    const formatted = `${day.padStart(2, '0')}/${month.padStart(2, '0')}/${year}`
    const asDate = new Date(+year, (+month) - 1, +day)
    if (formatDate(asDate) === formatted) {
      return asDate
    }
  } else if (dateNumericFormatRegex.test(value)) {
    const day = value.slice(0, 2)
    const month = value.slice(2, 4)
    const year = value.slice(4, 8)
    const formatted = `${day}/${month}/${year}`
    const asDate = new Date(+year, (+month) - 1, +day)
    if (formatDate(asDate) === formatted) {
      return asDate
    }
  }
  return null
}

/**
 * @typedef DateInputProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the input is disabled
 * @property {string} [errorText=''] Error message to display
 * @property {string} [helpText=''] Help text to display
 * @property {string} id HTML ID for the input element
 * @property {string} label Label text for the input
 * @property {string} [placeholder='DD/MM/YYYY'] Placeholder text for the input
 * @property {boolean} [readonly=false] Whether the input is read-only
 * @property {boolean} [required=false] Whether the input is required
 * @property {string} [tooltip=''] Tooltip text for the label
 * @property {string} [value = ''] Input value
 * @property {(text: string, date: Date | null) => void} onChange Change event handler
 */

/**
 * @param {DateInputProps} props
 */
export default function DateInput({
  className,
  disabled = false,
  errorText = '',
  helpText = '',
  id,
  label,
  placeholder = 'DD/MM/YYYY',
  readonly = false,
  required = false,
  tooltip = '',
  value = '',
  onChange,
}) {
  const showTooltip = typeof tooltip === 'string' && tooltip.length > 0
  const showError = typeof errorText === 'string' && errorText.length > 0
  const showHelpText = !showError && typeof helpText === 'string' && helpText.length > 0

  const layoutClasses = clsx(
    styles.layout,
    disabled && styles.disabled,
    className,
  )
  const inputClasses = clsx(
    styles.input,
    readonly && styles.readonly,
    showError && styles.error,
  )

  /** @type {React.ChangeEventHandler<HTMLInputElement>} */
  const handleChange = (event) => {
    const newValue = event.target.value.replaceAll(dateInvalidCharRegex, '')
    onChange(newValue, parseDateString(newValue))
  }

  const handleBlur = () => {
    const asDate = parseDateString(value)
    if (asDate === null) { return }
    const formatted = formatDate(asDate)
    if (formatted !== value) {
      onChange(formatted, asDate)
    }
  }

  const handleClear = () => {
    onChange('', null)
  }

  return (
    <div className={layoutClasses}>
      <div className={styles.labelContainer}>
        <label className={styles.label} htmlFor={id}>
          {label}
        </label>
        {showTooltip ? (
          <Tooltip className={styles.infoIcon} text={tooltip}>
            <InfoIcon />
          </Tooltip>
        ) : null}
      </div>
      <div className={styles.inputContainer}>
        <input
          className={inputClasses}
          disabled={disabled}
          id={id}
          maxLength={10}
          placeholder={placeholder}
          readOnly={readonly}
          tabIndex={readonly ? -1 : undefined}
          type="text"
          value={value}
          onChange={handleChange}
          onBlur={handleBlur}
          aria-disabled={disabled}
          aria-readonly={readonly}
          aria-required={required}
        />
        <IconButton
          className={styles.clearIcon}
          tabIndex={-1}
          onClick={handleClear}
        >
          <ClearIcon />
        </IconButton>
      </div>
      <div className={styles.helperTextContainer}>
        {showError ? (
          <div className={styles.errorTextContainer}>
            <div className={styles.errorIcon}>
              <ErrorIcon />
            </div>
            <span className={styles.errorText}>{errorText}</span>
          </div>
        ) : null}
        {showHelpText ? (
          <span className={styles.helpText}>{helpText}</span>
        ) : null}
      </div>
    </div>
  )
}

DateInput.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  errorText: PropTypes.string,
  helpText: PropTypes.string,
  id: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  readonly: PropTypes.bool,
  required: PropTypes.bool,
  tooltip: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
}
