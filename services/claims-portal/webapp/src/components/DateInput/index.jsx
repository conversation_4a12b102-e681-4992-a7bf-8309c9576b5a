import { useEffect, useState } from 'react'
import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './DateInput.module.scss'
import IconButton from '../IconButton/index.jsx'
import Tooltip from '../Tooltip/index.jsx'
import ClearIcon from '../../icons/Clear.jsx'
import InfoIcon from '../../icons/Info.jsx'
import ErrorIcon from '../../icons/Error.jsx'

const dateInvalidCharRegex = /[^\d/]/g
const dateFormatRegex = /^\d{2}\/\d{2}\/\d{4}$/

/**
 * @param {Date | null} date
 */
function formatDate(date) {
  if (date === null) return ''
  if (date.toString() === 'Invalid Date') return ''
  return date.toLocaleDateString('en-GB', { dateStyle: 'short' })
}

function toDate(dateString) {
  if (!dateFormatRegex.test(dateString)) {
    return null
  }
  const [day, month, year] = dateString.split('/')
  const proposed = new Date(+year, (+month) - 1, +day)
  return formatDate(proposed) === dateString ? proposed : null
}

/**
 * @typedef DateInputProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the input is disabled
 * @property {string} [errorText=''] Error message to display
 * @property {string} [helpText=''] Help text to display
 * @property {string} id HTML ID for the input element
 * @property {string} label Label text for the input
 * @property {string} [placeholder='DD/MM/YYYY'] Placeholder text for the input
 * @property {boolean} [readonly=false] Whether the input is read-only
 * @property {string} [tooltip=''] Tooltip text for the label
 * @property {Date | null} [value = null] Input value
 * @property {(value: Date | null) => void} [onChange] Change event handler
 */

/**
 * @param {DateInputProps} props
 */
export default function DateInput({
  className,
  disabled = false,
  errorText = '',
  helpText = '',
  id,
  label,
  placeholder = 'DD/MM/YYYY',
  readonly = false,
  tooltip = '',
  value = null,
  onChange,
}) {
  const [editValue, setEditValue] = useState('')

  useEffect(() => {
    setEditValue(formatDate(value))
  }, [value])

  const showTooltip = typeof tooltip === 'string' && tooltip.length > 0
  const showError = typeof errorText === 'string' && errorText.length > 0
  const showHelpText = !showError && typeof helpText === 'string' && helpText.length > 0

  const layoutClasses = clsx(
    styles.layout,
    disabled && styles.disabled,
    className,
  )
  const inputClasses = clsx(
    styles.input,
    readonly && styles.readonly,
    showError && styles.error,
  )

  /**
   * @param {Parameters<import('react').ChangeEventHandler<HTMLInputElement>>[0]} event
   */
  const handleChange = (event) => {
    const newValue = event.target.value.replaceAll(dateInvalidCharRegex, '')
    setEditValue(newValue)
    if (dateFormatRegex.test(newValue)) {
      const date = toDate(newValue)
      if (date !== null && typeof onChange === 'function') {
        onChange(date)
      }
    }
  }

  const handleBlur = () => {
    const date = toDate(editValue)
    if (date === null) {
      setEditValue('')
    }
    if (typeof onChange === 'function') {
      if (date !== null || value !== null) {
        onChange(date)
      }
    }
  }

  const handleClear = () => {
    setEditValue('')
    if (typeof onChange === 'function' && value !== null) {
      onChange(null)
    }
  }

  return (
    <div className={layoutClasses}>
      <div className={styles.labelContainer}>
        <label className={styles.label} htmlFor={id}>
          {label}
        </label>
        {showTooltip ? (
          <Tooltip className={styles.infoIcon} text={tooltip}>
            <InfoIcon />
          </Tooltip>
        ) : null}
      </div>
      <div className={styles.inputContainer}>
        <input
          className={inputClasses}
          disabled={disabled || readonly}
          id={id}
          maxLength={10}
          placeholder={placeholder}
          type="text"
          value={editValue}
          onChange={handleChange}
          onBlur={handleBlur}
        />
        <IconButton
          className={styles.clearIcon}
          tabIndex={-1}
          onClick={handleClear}
        >
          <ClearIcon />
        </IconButton>
      </div>
      <div className={styles.helperTextContainer}>
        {showError ? (
          <div className={styles.errorTextContainer}>
            <div className={styles.errorIcon}>
              <ErrorIcon />
            </div>
            <span className={styles.errorText}>{errorText}</span>
          </div>
        ) : null}
        {showHelpText ? (
          <span className={styles.helpText}>{helpText}</span>
        ) : null}
      </div>
    </div>
  )
}

DateInput.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  errorText: PropTypes.string,
  helpText: PropTypes.string,
  id: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string,
  readonly: PropTypes.bool,
  tooltip: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
}
