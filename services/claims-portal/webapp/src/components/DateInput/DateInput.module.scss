@use '../../public/css/colours.scss' as colours;
@use '../../public/css/fonts.scss' as fonts;

$width: 288px;

.layout {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-height: 100px;
  padding: 0;
  width: $width;
}

.labelContainer {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: none;
  flex-direction: row;
  flex-grow: 0;
  gap: 4px;
  min-height: 24px;
  padding: 0px;
  width: $width;
}

.label {
  color: colours.$text-default;
  flex-grow: 1;
  font-family: fonts.$microtext-rg;
  font-size: 16px;
  line-height: 24px;
}

.infoIcon {
  flex: none;
  flex-grow: 0;
  height: 24px;
  width: 24px;
}

.inputContainer {
  height: 48px;
  position: relative;
  width: $width;
}

.input {
  border: 1px solid colours.$border-default;
  border-radius: 8px;
  box-sizing: border-box;
  color: colours.$text-default;
  font-family: fonts.$microtext-rg;
  font-size: 16px;
  height: 48px;
  line-height: 24px;
  padding: 12px;
  padding-right: 44px;
  width: $width;

  &::placeholder {
    color: colours.$text-soft;
    flex: none;
    flex-grow: 1;
    text-align: left;
  }

  &:hover {
    border-color: colours.$border-hover;
  }

  &:focus-within {
    border: 2px solid colours.$action-alternative-default;
    outline: none;

    &::placeholder {
      color: colours.$text-default;
    }
  }

  &.error {
    border: 1px solid colours.$border-error;
  }

  &.readonly {
    background-color: colours.$surface-disabled;
    border: none;
    color: colours.$text-default;

    &::placeholder {
      color: colours.$text-default;
    }
  }
}

.clearIcon {
  display: none;
}

.input:focus-within + .clearIcon {
  display: flex;
  height: 24px;
  position: absolute;
  width: 24px;
  top: 12px;
  right: 12px;
}

.helperTextContainer {
  align-self: stretch;
  flex: none;
  flex-grow: 0;
  min-height: 20px;
  width: $width;
}

.helpText {
  color: colours.$text-soft;
  font-family: fonts.$microtext-rg;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
  width: $width;
}

.errorTextContainer {
  align-items: flex-start;
  display: flex;
  flex-direction: row;
  gap: 4px;
  padding: 0px;
  width: $width;
}

.errorIcon {
  flex: none;
  flex-grow: 0;
  height: 20px;
  width: 20px;
}

.errorText {
  align-self: stretch;
  color: colours.$text-error;
  flex: none;
  flex-grow: 1;
  font-family: fonts.$microtext-rg;
  font-size: 14px;
  min-height: 20px;
  line-height: 20px;
  width: 264px;
}

.layout.disabled {
  .label {
    color: colours.$text-disabled;
  }

  .infoIcon {
    path {
      fill: colours.$text-disabled;
    }
  }

  .input {
    background-color: colours.$surface-disabled;
    border: none;
    cursor: not-allowed;

    &::placeholder {
      color: colours.$text-disabled;
    }
  }

  .helpText {
    color: colours.$text-disabled;
  }
}
