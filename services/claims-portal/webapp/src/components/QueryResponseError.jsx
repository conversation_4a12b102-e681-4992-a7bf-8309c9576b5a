import PropTypes from 'prop-types'
import { Navigate } from 'react-router'
import { QueryError, StepError } from '../query.js'
import { getStepRoute } from '../stepRoutes.js'

/**
 * @param {{ error: Error }} props
 */
export default function QueryResponseError({ error }) {
  if (error instanceof StepError) {
    return <Navigate to={getStepRoute(error.step)} replace />
  }
  if (error instanceof QueryError) {
    if (error.status === 401) {
      return <Navigate to="/session-expired" replace />
    }
  }
  return (
    <div className="viewport">
      <h2>An error occurred</h2>
    </div>
  )
}

QueryResponseError.propTypes = { error: PropTypes.instanceOf(Error).isRequired }
