import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './TextInput.module.scss'
import IconButton from '../IconButton/index.jsx'
import Tooltip from '../Tooltip/index.jsx'
import ClearIcon from '../../icons/Clear.jsx'
import InfoIcon from '../../icons/Info.jsx'
import ErrorIcon from '../../icons/Error.jsx'

/**
 * @typedef TextInputProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {boolean} [disabled=false] Whether the input is disabled
 * @property {string} [errorText=''] Error message to display
 * @property {string} [helpText=''] Help text to display
 * @property {string} id HTML ID for the input element
 * @property {string} label Label text for the input
 * @property {number} [maxLength] Maximum length of the input value
 * @property {string} [placeholder=''] Placeholder text for the input
 * @property {boolean} [readonly=false] Whether the input is read-only
 * @property {string} [tooltip=''] Tooltip text for the label
 * @property {string} [value] Input value
 * @property {(value: string) => void} [onChange] Change event handler
 */

/**
 * @param {TextInputProps} props
 */
export default function TextInput({
  className,
  disabled = false,
  errorText = '',
  helpText = '',
  id,
  label,
  maxLength,
  placeholder = '',
  readonly = false,
  tooltip = '',
  value,
  onChange,
}) {
  const showTooltip = typeof tooltip === 'string' && tooltip.length > 0
  const showError = typeof errorText === 'string' && errorText.length > 0
  const showHelpText = !showError && typeof helpText === 'string' && helpText.length > 0

  const layoutClasses = clsx(
    styles.layout,
    disabled && styles.disabled,
    className,
  )
  const inputClasses = clsx(
    styles.input,
    readonly && styles.readonly,
    showError && styles.error,
  )

  /**
   * @param {Parameters<import('react').ChangeEventHandler<HTMLInputElement>>[0]} event
   */
  const handleChange = (event) => {
    if (typeof onChange === 'function') {
      onChange(event.target.value)
    }
  }

  const handleClear = () => {
    if (typeof onChange === 'function') {
      onChange('')
    }
  }

  return (
    <div className={layoutClasses}>
      <div className={styles.labelContainer}>
        <label className={styles.label} htmlFor={id}>
          {label}
        </label>
        {showTooltip ? (
          <Tooltip className={styles.infoIcon} text={tooltip}>
            <InfoIcon />
          </Tooltip>
        ) : null}
      </div>
      <div className={styles.inputContainer}>
        <input
          className={inputClasses}
          disabled={disabled || readonly}
          id={id}
          maxLength={maxLength}
          placeholder={placeholder}
          type="text"
          value={value}
          onChange={handleChange}
        />
        <IconButton
          className={styles.clearIcon}
          tabIndex={-1}
          onClick={handleClear}
        >
          <ClearIcon />
        </IconButton>
      </div>
      <div className={styles.helperTextContainer}>
        {showError ? (
          <div className={styles.errorTextContainer}>
            <div className={styles.errorIcon}>
              <ErrorIcon />
            </div>
            <span className={styles.errorText}>{errorText}</span>
          </div>
        ) : null}
        {showHelpText ? (
          <span className={styles.helpText}>{helpText}</span>
        ) : null}
      </div>
    </div>
  )
}

TextInput.propTypes = {
  className: PropTypes.string,
  disabled: PropTypes.bool,
  errorText: PropTypes.string,
  helpText: PropTypes.string,
  id: PropTypes.string.isRequired,
  label: PropTypes.string.isRequired,
  maxLength: PropTypes.number,
  placeholder: PropTypes.string,
  readonly: PropTypes.bool,
  tooltip: PropTypes.string,
  value: PropTypes.string,
  onChange: PropTypes.func,
}
