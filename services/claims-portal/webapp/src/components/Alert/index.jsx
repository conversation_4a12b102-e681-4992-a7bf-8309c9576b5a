import clsx from 'clsx'
import PropTypes from 'prop-types'
import styles from './Alert.module.scss'
import IconButton from '../IconButton/index.jsx'
import ErrorIcon from '../../icons/Error.jsx'
import InfoIcon from '../../icons/Info.jsx'

/**
 * @typedef AlertProps
 * @property {string} [className] Additional CSS classes to apply
 * @property {string} text Alert message text
 * @property {'info' | 'error'} type Button display type
 * @property {import('react').MouseEventHandler<HTMLButtonElement>} [onClose] Click event handler
 */

/**
 * @param {AlertProps} props
 */
export default function Alert({
  className,
  text,
  type,
  onClose,
}) {
  const classes = clsx(
    styles.container,
    type === 'info' && styles.info,
    type === 'error' && styles.error,
    className,
  )

  return (
    <div className={classes}>
      <div className={styles.icon}>
        {type === 'info' ? (
          <InfoIcon />
        ) : null}
        {type === 'error' ? (
          <ErrorIcon />
        ) : null}
      </div>
      <div className={styles.content}>
        <span className={styles.text}>{text}</span>
      </div>
      {typeof onClose === 'function' ? (
        <IconButton
          className={styles.closeButton}
          onClick={() => { onClose() }}
          aria-label="Close alert"
        >
          TODO
        </IconButton>
      ) : null}
    </div>
  )
}

Alert.propTypes = {
  className: PropTypes.string,
  text: PropTypes.string.isRequired,
  type: PropTypes.oneOf(['info', 'error']).isRequired,
  onClose: PropTypes.func,
}
