@use '../../public/css/colours.scss' as colours;
@use '../../public/css/fonts.scss' as fonts;

.container {
  align-items: flex-start;
  border-radius: 8px;
  display: flex;
  flex-direction: row;
  gap: 8px;
  min-height: 72px;
  padding: 24px;
  width: 448px;
}

.info {
  background-color: colours.$background-info;
}

.error {
  background-color: colours.$background-error;
}

.icon {
  flex: none;
  flex-grow: 0;
  height: 24px;
  width: 24px;
}

.content {
  align-items: flex-start;
  display: flex;
  flex: none;
  flex-direction: column;
  flex-grow: 1;
  min-height: 22px;
  padding: 2px 0px 0px;
  width: 336px;
}

.text {
  align-self: stretch;
  color: colours.$text-default;
  flex: none;
  flex-grow: 0;
  font-family: fonts.$microtext-rg;
  font-size: 14px;
  line-height: 20px;
}

.closeIcon {
  flex: none;
  flex-grow: 0;
  height: 24px;
  width: 24px;
}
