const alphanumericRegex = /^[A-Z0-9]+$/i
const alphanumericSpaceRegex = /^[A-Z0-9 ]+$/i
const emailRegex = /^[^\s@]+@[^\s@]+$/
const invalidCharRegex = /[!@£$%^&*()_+=[\]{}:;/\\?|~<>]/g
const mobilePhoneRegex = /^07\d{9}$/
const monthYearRegex = /^\d{2}\/\d{4}$/
const postcodeRegex = /^(([A-Z]{1,2}[0-9]{1,2})|([A-Z]{1,2}[0-9][A-Z]))[ ]?[0-9][A-Z]{2}$/i

/**
 * @typedef ValidateOptions
 * @property {boolean} [alphanumeric=false] Only allow alphanumeric characters
 * @property {boolean} [alphanumericSpace=false] Only allow alphanumeric and space characters
 * @property {number} [minLength=0] Minimum length of the input
 * @property {boolean} [required=false] Whether the field is required
 * @property {boolean} [restrictSpecialChars=false] Do not allow many special characters
 */

/**
 * @param {[string, Date | null]} value
 * @param {ValidateOptions} options
 */
export function validateDate(value, options) {
  if (value === undefined || value[0] === '') {
    return options.required === true ? 'This field is required' : ''
  }
  if (value[1] === null) {
    return 'Invalid format'
  }
  return ''
}

/**
 * @param {string} value
 * @param {ValidateOptions} options
 */
export function validateEmail(value, options) {
  if (value === undefined || value === '') {
    return options.required === true ? 'This field is required' : ''
  }
  if (!emailRegex.test(value)) {
    return 'Invalid format'
  }
  return ''
}

/**
 * @param {string} value
 * @param {ValidateOptions} options
 */
export function validateMobilePhone(value, options) {
  if (value === undefined || value === '') {
    return options.required === true ? 'This field is required' : ''
  }
  if (!mobilePhoneRegex.test(value)) {
    return 'Invalid format'
  }
  return ''
}

/**
 * @param {string} value
 * @param {ValidateOptions} options
 */
export function validateMoney(value, options) {
  if (value === undefined || value === '') {
    return options.required === true ? 'This field is required' : ''
  }
  if (+value >= 1_000_000) {
    return 'Invalid format'
  }
  return ''
}

/**
 * @param {string} value
 * @param {ValidateOptions} options
 */
export function validateMonthYear(value, options) {
  if (value === undefined || value === '') {
    return options.required === true ? 'This field is required' : ''
  }
  if (!monthYearRegex.test(value)) {
    return 'Invalid format'
  }
  const month = +value.split('/')[0]
  if (month < 1 || month > 12) {
    return 'Invalid format'
  }
  return ''
}

/**
 * @param {number} value
 * @param {ValidateOptions} options
 */
export function validateNumber(value, options) {
  if (value === undefined || value === '') {
    return options.required === true ? 'This field is required' : ''
  }
  return ''
}

/**
 * @param {string} value
 * @param {ValidateOptions} options
 */
export function validatePostcode(value, options) {
  if (value === undefined || value === '') {
    return options.required === true ? 'This field is required' : ''
  }
  if (!postcodeRegex.test(value)) {
    return 'Invalid format'
  }
  return ''
}

/**
 * @param {string} value
 * @param {ValidateOptions} options
 */
export function validateText(value, options) {
  if (value === undefined || value === '') {
    return options.required === true ? 'This field is required' : ''
  }
  if (options.minLength && value.length < options.minLength) {
    return 'Invalid format'
  }
  if (options.alphanumeric && !alphanumericRegex.test(value)) {
    return 'Invalid format'
  }
  if (options.alphanumericSpace && !alphanumericSpaceRegex.test(value)) {
    return 'Invalid format'
  }
  if (options.restrictSpecialChars === true && invalidCharRegex.test(value)) {
    return 'Invalid format'
  }
  return ''
}
