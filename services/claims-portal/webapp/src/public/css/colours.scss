// Core
$blue-50: #3366FF;
$neutral-00: #FFFFFF;
$neutral-05: #F6F6F6;
$neutral-10: #F0F0F0;
$neutral-25: #CCCCCC;
$neutral-45: #8F8F8F;
$neutral-55: #727272;
$neutral-95: #222222;
$red-05: #FEE5E5;
$red-40: #EC0000;
$red-50: #CC0000;
$red-65: #990000;
$sky-00: #F5F9FB;
$sky-10: #DEEDF2;
$sky-20: #CEDEE7;
$turquoise-75: #127277;
$turquoise-85: #0D5155;
$opacity-06-neutral-95: rgba(34, 34, 34, 0.06);
$opacity-23-neutral-95: rgba(34, 34, 34, 0.23);
$opacity-10-neutral-95: rgba(34, 34, 34, 0.1);

// Semantic Tokens
$action-alternative-default: $turquoise-75;
$action-alternative-hover: $turquoise-85;
$action-alternative-selected: $turquoise-75;
$action-disabled: $opacity-06-neutral-95;
$action-main-default: $red-40;
$action-main-hover: $red-50;
$action-main-selected: $red-40;
$action-on-disabled: $opacity-23-neutral-95;
$action-on-main-default: $neutral-00;
$action-on-main-hover: $neutral-00;
$action-on-main-selected: $neutral-00;
$background-brand: $red-40;
$background-default: $neutral-00;
$background-error: $red-05;
$background-info: $sky-00;
$border-alternative: $sky-20;
$border-default: $neutral-45;
$border-disabled: $neutral-25;
$border-error: $red-65;
$border-hover: $neutral-95;
$border-soft: $neutral-25;
$button-secondary-default: $neutral-00;
$button-secondary-hover: $neutral-05;
$button-on-secondary-default: $red-40;
$button-on-secondary-hover: $red-50;
$effect-focus-inner: $neutral-00;
$effect-focus-outer: $blue-50;
$surface-disabled: $opacity-06-neutral-95;
$surface-flat: $neutral-00;
$surface-floating: $neutral-00;
$surface-state-pressed: $opacity-10-neutral-95;
$text-brand: $red-40;
$text-default: $neutral-95;
$text-disabled: $opacity-23-neutral-95;
$text-error: $red-65;
$text-inverted: $neutral-00;
$text-soft: $neutral-55;
