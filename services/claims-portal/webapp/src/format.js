const poundFormat = new Intl.NumberFormat('en-GB', {
  style: 'currency',
  currency: 'GBP',
})

const numberFormat = new Intl.NumberFormat('en-GB', {
  style: 'decimal',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
})

/**
 * @param {number} value
 */
export function asGBP(value) {
  return poundFormat.format(value)
}

/**
 * @param {number | string} value
 */
export function asNumber(value) {
  return numberFormat.format(typeof value === 'string' ? +value : value)
}

/**
 * @param {number} value
 */
export function asPercentage(value) {
  return `${value.toFixed(2)}%`
}
