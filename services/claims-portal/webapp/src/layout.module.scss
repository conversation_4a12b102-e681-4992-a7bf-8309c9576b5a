@use './public/css/colours.scss' as colours;
@use './public/css/fonts.scss' as fonts;

.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;

  header {
    width: 100%;
    height: 64px;
    background-color: colours.$background-brand;
    color: colours.$text-inverted;

    img {
      height: 64px;

      @media screen and (min-width: 450px) {
        margin-left: -24px;
      }
    }
  }

  .routeContainer {
    flex: 1;
    padding: 20px;
    width: 100%;
  }

  footer {
    width: 100%;
    min-height: 214px;
    padding: 38px 0;
    background-color: colours.$background-brand;
    color: colours.$text-inverted;
  }
}

.row {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.copy {
  font-family: fonts.$microtext-rg;
  font-size: 16px;
}

.disclaimer {
  padding: 20px;

  span {
    font-family: fonts.$microtext-rg;
    font-weight: 400;
    line-height: 18px;
    font-size: 12px;
    text-align: center;
  }
}
