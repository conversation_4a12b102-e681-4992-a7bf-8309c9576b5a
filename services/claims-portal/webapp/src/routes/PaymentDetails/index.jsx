import { useQuery } from '@tanstack/react-query'
import { request } from '../../query.js'
import QueryResponseError from '../../components/QueryResponseError.jsx'
import PaymentDetailsForm from './PaymentDetailsForm.jsx'
import ClaimsStepper from '../../components/ClaimsStepper.jsx'

async function getPaymentDetailsFn() {
  return request({
    url: '/api/payment-details',
    method: 'GET',
    errorMessage: 'Unable to get payment details',
  })
}

export default function PaymentDetails() {
  const paymentDetailsQuery = useQuery({
    queryKey: ['payment-details'],
    queryFn: getPaymentDetailsFn,
  })

  if (paymentDetailsQuery.isLoading) {
    return (
      <div className="viewport">
        <h2>Loading...</h2>
      </div>
    )
  }

  if (paymentDetailsQuery.isError) {
    return (<QueryResponseError error={paymentDetailsQuery.error} />)
  }

  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <ClaimsStepper step={3} />
      <br />
      <h3>Confirm Your Payment Details</h3>
      {paymentDetailsQuery.data.accountNumber === null ? null : (
        <>
          <p className="italic">
            We hold the following bank details on record for you, from your previous payments on this agreement:
            <br />
            <strong>Account number ending in {paymentDetailsQuery.data.accountNumber}</strong>
          </p>
          <p>
            Please confirm whether you would like us to use these details for your claim payment, or if you would prefer to
            provide alternative payment information. Please note, the bank details provided must be a UK registered bank
            account in your name.
          </p>
        </>
      )}
      <PaymentDetailsForm
        hasExisting={paymentDetailsQuery.data.accountNumber !== null}
        paymentDetails={paymentDetailsQuery.data.paymentDetails}
      />
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
        aliqua.
      </p>
    </div>
  )
}
