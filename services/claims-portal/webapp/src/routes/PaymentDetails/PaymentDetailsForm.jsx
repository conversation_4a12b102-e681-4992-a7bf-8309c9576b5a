import { useState } from 'react'
import PropTypes from 'prop-types'
import { useNavigate } from 'react-router'
import { useMutation } from '@tanstack/react-query'
import { v4 as uuid } from 'uuid'
import { request, handleMutationError } from '../../query.js'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import ButtonGroup from '../../components/ButtonGroup/index.jsx'
import NumericInput from '../../components/NumericInput/index.jsx'
import RadioButtonGroup from '../../components/RadioButtonGroup/index.jsx'
import SortCodeInput from '../../components/SortCodeInput/index.jsx'
import TextInput from '../../components/TextInput/index.jsx'
import { validateText } from '../../schema.js'

const decisionOptions = [
  { label: 'Yes, use these bank details for my payment', value: 'existing' },
  { label: 'No, I would like to enter new bank details', value: 'new' },
]

/**
 * @typedef PaymentDetails
 * @property {boolean} useExisting
 * @property {string} [payee]
 * @property {string} [accountNumber]
 * @property {string} [sortCode]
 */

/**
 * @param {{ data: PaymentDetails; idempotencyKey: string }} input
 */
async function submitPaymentDetailsFn({ data, idempotencyKey }) {
  return request({
    url: '/api/payment-details',
    method: 'POST',
    body: data,
    headers: { 'x-idempotency-key': idempotencyKey },
    errorMessage: 'Unable to submit payment details',
  })
}

async function confirmPaymentDetailsFn({ idempotencyKey }) {
  return request({
    url: '/api/confirm-payment-details',
    method: 'POST',
    headers: { 'x-idempotency-key': idempotencyKey },
    errorMessage: 'Unable to confirm payment details',
  })
}

/**
 * @param {{ hasExisting: boolean; paymentDetails: PaymentDetails }} props
 */
export default function PaymentDetailsForm({ hasExisting, paymentDetails = null }) {
  const [showErrors, setShowErrors] = useState(false)
  const [canEdit, setCanEdit] = useState(paymentDetails === null)
  const [decision, setDecision] = useState(() => {
    if (!hasExisting) return 'new'
    if (paymentDetails === null) return null
    return paymentDetails.useExisting ? 'existing' : 'new'
  })
  const [payee, setPayee] = useState(paymentDetails?.payee || '')
  const [accountNumber, setAccountNumber] = useState(paymentDetails?.accountNumber || '')
  const [sortCode, setSortCode] = useState(paymentDetails?.sortCode || '')

  const navigate = useNavigate()

  const decisionError = decision === null ? 'You will need to select a payment option to continue' : ''
  const payeeError = validateText(payee, { required: true, alphanumericSpace: true })
  const accountNumberError = validateText(accountNumber, { required: true, minLength: 8 })
  const sortCodeError = validateText(sortCode, { required: true, minLength: 6 })

  const submitMutation = useMutation({
    mutationFn: submitPaymentDetailsFn,
    onSuccess(response, { data: requestData }) {
      if (requestData.useExisting) {
        navigate('/complete', { replace: true })
      } else if (response.valid) {
        setCanEdit(false)
      }
    },
    onError(error) {
      handleMutationError(error, navigate)
    },
  })

  const confirmMutation = useMutation({
    mutationFn: confirmPaymentDetailsFn,
    onSuccess() {
      navigate('/complete', { replace: true })
    },
    onError(error) {
      handleMutationError(error, navigate)
    },
  })

  const handleSubmit = (event) => {
    event.preventDefault()
    if (submitMutation.isPending || decision === null || (
      decision === 'new' && (payeeError || accountNumberError || sortCodeError)
    )) {
      setShowErrors(true)
      return
    }
    submitMutation.mutate({
      data: decision === 'existing' ? { useExisting: true } : {
        useExisting: false,
        payee,
        accountNumber,
        sortCode,
      },
      idempotencyKey: uuid(),
    })
  }

  const handleConfirm = () => {
    if (confirmMutation.isPending) return
    confirmMutation.mutate({ idempotencyKey: uuid() })
  }

  return (
    <form onSubmit={handleSubmit}>
      {hasExisting ? (
        <RadioButtonGroup
          errorText={showErrors ? decisionError : ''}
          name="payment_use_existing"
          options={decisionOptions}
          readonly={!canEdit || submitMutation.isPending}
          required
          value={decision}
          onChange={setDecision}
        />
      ) : null}
      {decision === 'new' ? (
        <>
          <p>
            Please provide your updated payment information. We&apos;ll complete a payee check before using these details.
            Please note, the bank details provided must be a UK registered bank account in your name.
          </p>
          <TextInput
            errorText={showErrors ? payeeError : ''}
            id="payee"
            label="Account name"
            maxLength={70}
            readonly={!canEdit || submitMutation.isPending}
            required
            value={payee}
            onChange={setPayee}
          />
          <SortCodeInput
            errorText={showErrors ? sortCodeError : ''}
            id="sort_code"
            label="Sort code"
            readonly={!canEdit || submitMutation.isPending}
            required
            value={sortCode}
            onChange={setSortCode}
          />
          <NumericInput
            errorText={showErrors ? accountNumberError : ''}
            id="account_number"
            label="Account number"
            maxLength={8}
            readonly={!canEdit || submitMutation.isPending}
            required
            value={accountNumber}
            onChange={setAccountNumber}
          />
        </>
      ) : null}
      {canEdit ? (
        <Button submit disabled={submitMutation.isPending}>Submit</Button>
      ) : (
        <>
          <p>Please review the details you have entered to ensure they are correct.</p>
          <ButtonGroup>
            <Button type="secondary" disabled={confirmMutation.isPending} onClick={() => { setShowErrors(false); setCanEdit(true) }}>
              Edit Details
            </Button>
            <Button disabled={confirmMutation.isPending} onClick={handleConfirm}>
              Confirm Details
            </Button>
          </ButtonGroup>
        </>
      )}
      {submitMutation.isSuccess && submitMutation.data.valid === false ? (
        <Alert
          text="The bank account details you provided are invalid. Please ensure the sort code and account number are correct."
          type="error"
        />
      ) : null}
      {submitMutation.isError || confirmMutation.isError ? (
        <Alert
          text="Something went wrong, please try again"
          type="error"
        />
      ) : null}
    </form>
  )
}

PaymentDetailsForm.propTypes = {
  hasExisting: PropTypes.bool.isRequired,
  paymentDetails: PropTypes.shape({
    useExisting: PropTypes.bool.isRequired,
    payee: PropTypes.string,
    accountNumber: PropTypes.string,
    sortCode: PropTypes.string,
  }),
}
