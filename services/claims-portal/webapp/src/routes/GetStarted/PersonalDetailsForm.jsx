import { useState } from 'react'
import { useNavigate } from 'react-router'
import { useMutation } from '@tanstack/react-query'
import QueryError from '../../query.js'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import DateInput from '../../components/DateInput/index.jsx'
import TextInput from '../../components/TextInput/index.jsx'
import { validateForename, validateSurname, validateDob } from './personalDetailsValidation.js'

/**
 * @param {Date} dob
 */
function convertDobForApi(dob) {
  return `${dob.getFullYear()}-${dob.getMonth() + 1}-${dob.getDate()}`
}

async function submitPersonalDetailsFn(data) {
  const result = await fetch('/api/personal-details', {
    method: 'POST',
    body: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json' },
    signal: AbortSignal.timeout(10000),
  })
  if (!result.ok) {
    throw new QueryError('Unable to submit Personal Details', result.status)
  }
}

export default function PersonalDetailsForm() {
  const [showErrors, setShowErrors] = useState(false)
  const [forename, setForename] = useState('')
  const [surname, setSurname] = useState('')
  const [dob, setDob] = useState(null)
  const navigate = useNavigate()

  const mutation = useMutation({
    mutationFn: submitPersonalDetailsFn,
    onSuccess() {
      navigate('/additional-information')
    },
  })

  const handleForenameChange = (value) => {
    setForename(value)
  }

  const handleSurnameChange = (value) => {
    setSurname(value)
  }

  const handleDobChange = (value) => {
    setDob(value)
  }

  const forenameError = validateForename(forename)
  const surnameError = validateSurname(surname)
  const dobError = validateDob(dob)

  const handleSubmit = (event) => {
    event.preventDefault()
    if (forenameError || surnameError || dobError || mutation.isPending) {
      setShowErrors(true)
      return
    }
    mutation.mutate({ forename, surname, dob: convertDobForApi(dob) })
  }

  return (
    <form onSubmit={handleSubmit}>
      <TextInput
        id="forename"
        label="First Name*"
        placeholder="First Name"
        maxLength={50}
        value={forename}
        errorText={showErrors && forenameError}
        onChange={handleForenameChange}
      />
      <TextInput
        id="surname"
        label="Last Name*"
        placeholder="Last Name"
        maxLength={50}
        value={surname}
        errorText={showErrors && surnameError}
        onChange={handleSurnameChange}
      />
      <DateInput
        id="dob"
        label="Date of Birth*"
        value={dob}
        errorText={showErrors && dobError}
        onChange={handleDobChange}
      />
      <Button submit disabled={mutation.isPending}>Make a Claim</Button>
      {mutation.isError ? (
        <Alert
          text="We couldn't find a match based on the details you provided. Please check your information or try using the details you provided at the time of your loan agreement"
          type="error"
        />
      ) : null}
    </form>
  )
}
