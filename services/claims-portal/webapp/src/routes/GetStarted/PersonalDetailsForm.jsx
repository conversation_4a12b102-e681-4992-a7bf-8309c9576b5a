import { useState } from 'react'
import { useNavigate } from 'react-router'
import { useMutation } from '@tanstack/react-query'
import { request, preSessionRetry, queryClient } from '../../query.js'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import DateInput from '../../components/DateInput/index.jsx'
import TextInput from '../../components/TextInput/index.jsx'
import { validateDate, validateText } from '../../schema.js'

/**
 * @param {Date} dob
 */
function convertDobForApi(dob) {
  return `${dob.getFullYear()}-${dob.getMonth() + 1}-${dob.getDate()}`
}

async function submitPersonalDetailsFn(data) {
  return request({
    url: '/api/personal-details',
    method: 'POST',
    body: data,
    errorMessage: 'Unable to submit Personal Details',
  })
}

export default function PersonalDetailsForm() {
  const [showErrors, setShowErrors] = useState(false)
  const [forename, setForename] = useState('')
  const [surname, setSurname] = useState('')
  const [dob, setDob] = useState(['', null])
  const navigate = useNavigate()

  const mutation = useMutation({
    mutationFn: submitPersonalDetailsFn,
    retry: preSessionRetry,
    onSuccess() {
      queryClient.resetQueries()
      navigate('/additional-information')
    },
  })

  const handleDobChange = (value, date) => {
    setDob([value, date])
  }

  const forenameError = validateText(forename, { required: true, restrictSpecialChars: true })
  const surnameError = validateText(surname, { required: true, restrictSpecialChars: true })
  const dobError = validateDate(dob, { required: true })

  const handleSubmit = (event) => {
    event.preventDefault()
    if (forenameError || surnameError || dobError || mutation.isPending) {
      setShowErrors(true)
      return
    }
    mutation.mutate({ forename, surname, dob: convertDobForApi(dob[1]) })
  }

  return (
    <form onSubmit={handleSubmit}>
      <TextInput
        errorText={showErrors ? forenameError : ''}
        id="forename"
        label="First Name"
        maxLength={50}
        readonly={mutation.isPending}
        required
        value={forename}
        onChange={setForename}
      />
      <TextInput
        errorText={showErrors ? surnameError : ''}
        id="surname"
        label="Last Name"
        maxLength={50}
        readonly={mutation.isPending}
        required
        value={surname}
        onChange={setSurname}
      />
      <DateInput
        errorText={showErrors ? dobError : ''}
        id="dob"
        label="Date of Birth"
        readonly={mutation.isPending}
        required
        value={dob[0]}
        onChange={handleDobChange}
      />
      <Button submit disabled={mutation.isPending}>Make a Claim</Button>
      {mutation.isError ? (
        <Alert
          text="We couldn't find a match based on the details you provided. Please check your information or try using the details you provided at the time of your loan agreement"
          type="error"
        />
      ) : null}
    </form>
  )
}
