import { useState } from 'react'
import { useNavigate } from 'react-router'
import { useMutation } from '@tanstack/react-query'
import { request, preSessionRetry, queryClient } from '../../query.js'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import TextInput from '../../components/TextInput/index.jsx'
import { validateText } from '../../schema.js'

async function submitClaimIdFn(data) {
  return request({
    url: '/api/claim-id',
    method: 'POST',
    body: data,
    errorMessage: 'Unable to submit Claim ID',
  })
}

export default function ClaimIdForm() {
  const [showErrors, setShowErrors] = useState(false)
  const [claimId, setClaimId] = useState('')
  const navigate = useNavigate()

  const mutation = useMutation({
    mutationFn: submitClaimIdFn,
    retry: preSessionRetry,
    onSuccess() {
      queryClient.resetQueries()
      navigate('/verification')
    },
  })

  const handleClaimIdChange = (value) => {
    setClaimId(value)
  }

  const claimIdError = validateText(claimId, { required: true, alphanumeric: true })

  const handleSubmit = (event) => {
    event.preventDefault()
    if (mutation.isPending || claimIdError) {
      setShowErrors(true)
      return
    }
    mutation.mutate({ claimId })
  }

  return (
    <form onSubmit={handleSubmit}>
      <TextInput
        errorText={showErrors ? claimIdError : ''}
        id="claim-id"
        label="Claim ID"
        maxLength={10}
        readonly={mutation.isPending}
        required
        tooltip="This will be on your letter/email, if you have received one from us"
        value={claimId}
        onChange={handleClaimIdChange}
      />
      <Button submit disabled={mutation.isPending}>Continue</Button>
      {mutation.isError ? (
        <Alert
          text="We couldn't find a claim with the ID you entered. please check the number and try again"
          type="error"
        />
      ) : null}
    </form>
  )
}
