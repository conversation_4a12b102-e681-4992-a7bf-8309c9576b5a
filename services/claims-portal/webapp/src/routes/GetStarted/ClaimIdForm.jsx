import { useState } from 'react'
import { useNavigate } from 'react-router'
import { useMutation } from '@tanstack/react-query'
import QueryError from '../../query.js'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import TextInput from '../../components/TextInput/index.jsx'

async function submitClaimIdFn(data) {
  const result = await fetch('/api/claim-id', {
    method: 'POST',
    body: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json' },
    signal: AbortSignal.timeout(10000),
  })
  if (!result.ok) {
    throw new QueryError('Unable to submit Claim ID', result.status)
  }
}

export default function ClaimIdForm() {
  const [showErrors, setShowErrors] = useState(false)
  const [claimId, setClaimId] = useState('')
  const navigate = useNavigate()

  const mutation = useMutation({
    mutationFn: submitClaimIdFn,
    onSuccess() {
      navigate('/verification')
    },
  })

  const handleClaimIdChange = (value) => {
    setClaimId(value)
  }

  const handleSubmit = (event) => {
    event.preventDefault()
    if (claimId.length === 0 || mutation.isPending) {
      setShowErrors(true)
      return
    }
    mutation.mutate({ claimId })
  }

  return (
    <form onSubmit={handleSubmit}>
      <TextInput
        id="claim-id"
        label="Claim ID"
        tooltip="This will be on your letter/email, if you have received one from us"
        placeholder="Claim ID"
        maxLength={10}
        value={claimId}
        errorText={showErrors && claimId.length === 0 ? 'This field is required' : ''}
        onChange={handleClaimIdChange}
      />
      <Button submit disabled={mutation.isPending}>Continue</Button>
      {mutation.isError ? (
        <Alert
          text="We couldn't find a claim with the ID you entered. please check the number and try again"
          type="error"
        />
      ) : null}
    </form>
  )
}
