import { useState } from 'react'
import PropTypes from 'prop-types'
import { useMutation } from '@tanstack/react-query'
import { v4 as uuid } from 'uuid'
import { request } from '../../query.js'
import Button from '../../components/Button/index.jsx'

async function submitFileFn({ file, idempotencyKey }) {
  return request({
    url: '/api/test-data',
    method: 'POST',
    file,
    headers: { 'x-idempotency-key': idempotencyKey, 'content-type': 'text/csv' },
    errorMessage: 'Unable to submit test data',
  })
}

function ErrorMessage({ error }) {
  const lines = error.split('\n')

  return (
    <p>
      {lines.map((line, index) => (
        // eslint-disable-next-line react/no-array-index-key
        <span key={index}>
          {index > 0 && index % 2 === 0 ? <br /> : null}
          {line}
        </span>
      ))}
    </p>
  )
}

ErrorMessage.propTypes = { error: PropTypes.string.isRequired }

export default function TestData() {
  const [ready, setReady] = useState(false)

  const mutation = useMutation({
    mutationFn: submitFileFn,
    onSuccess: (data) => {
      setReady(false)
      document.getElementById('test-data-file-input').value = ''
      return data
    },
  })

  /** @type {React.ChangeEventHandler<HTMLInputElement>} */
  const handleFileChange = (event) => {
    setReady(event.target.files.length === 1)
  }

  const handleSubmit = () => {
    if (mutation.isPending || !ready) return
    mutation.mutate({
      file: document.getElementById('test-data-file-input').files[0],
      idempotencyKey: uuid(),
    })
  }

  return (
    <div className="viewport">
      <h2>Test Data Upload</h2>
      <br />
      <form>
        <input id="test-data-file-input" type="file" accept=".csv" onChange={handleFileChange} />
        <Button disabled={mutation.isPending || !ready} onClick={handleSubmit}>Upload</Button>
      </form>
      <p>
        {mutation.isIdle ? null : (<>Status:&nbsp;</>)}
        {mutation.isPending && 'Uploading...'}
        {mutation.isSuccess && mutation.data.error === null ? 'Upload successful!' : null}
        {mutation.isSuccess && mutation.data.error ? (<ErrorMessage error={mutation.data.error} />) : null }
        {mutation.isError && mutation.error.message}
      </p>
    </div>
  )
}
