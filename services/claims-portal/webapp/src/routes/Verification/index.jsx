import { useState } from 'react'
import { Navigate } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import { request } from '../../query.js'
import QueryResponseError from '../../components/QueryResponseError.jsx'
import Form from './VerificationForm.jsx'
import { getQuestionById } from './questionConfig.js'
import ClaimsStepper from '../../components/ClaimsStepper.jsx'

async function getVerificationQuestionsFn() {
  const data = await request({
    url: '/api/verification-questions',
    method: 'GET',
    errorMessage: 'Unable to get verification questions',
  })
  if (Array.isArray(data.questions)) {
    const questions = data.questions.reduce((acc, questionId) => {
      acc.push(getQuestionById(questionId))
      return acc
    }, [])
    return { customerName: data.customerName, questions }
  }
  return { questions: [] }
}

export default function Verification() {
  const [showAttemptError, setShowAttemptError] = useState(false)
  const verificationQuestionsQuery = useQuery({
    queryKey: ['verification-questions'],
    queryFn: getVerificationQuestionsFn,
  })

  if (!verificationQuestionsQuery.isFetchedAfterMount) {
    return (
      <div className="viewport">
        <h2>Loading...</h2>
      </div>
    )
  }

  if (verificationQuestionsQuery.isError) {
    return (<QueryResponseError error={verificationQuestionsQuery.error} />)
  }

  if (verificationQuestionsQuery.data.questions.length !== 2) {
    return <Navigate to="/verification-failed" replace />
  }

  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <ClaimsStepper step={0} />
      <br />
      <div className="">
        {showAttemptError ? (
          <h3>We couldn&apos;t verify your identity, please try again</h3>
        ) : (
          <h3>Hello {verificationQuestionsQuery.data.customerName}, we&apos;ve successfully located your details</h3>
        )}
        <p>
          To continue with your claim, you will need to verify your identity by answering 2 security questions based on your
          most recent loan. You have three attempts to get both answers correct. If we can&apos;t confirm your identity, you
          will not be able to proceed with your claim.
        </p>
        <br />
      </div>
      <Form
        questions={verificationQuestionsQuery.data.questions}
        onAttemptError={() => { setShowAttemptError(true) }}
      />
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
        aliqua.
      </p>
    </div>
  )
}
