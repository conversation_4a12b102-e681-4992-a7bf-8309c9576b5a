import { useState } from 'react'
import { Navigate } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import QueryError from '../../query.js'
import Form from './VerificationForm.jsx'
import { getQuestionById } from './questionConfig.js'

async function getVerificationQuestionsFn() {
  const result = await fetch('/api/verification-questions', {
    method: 'GET',
    signal: AbortSignal.timeout(10000),
  })
  if (!result.ok) {
    throw new QueryError('Unable to get match status', result.status)
  }
  const data = await result.json()
  if (Array.isArray(data.questions)) {
    const questions = data.questions.reduce((acc, questionId) => {
      acc.push(getQuestionById(questionId))
      return acc
    }, [])
    return { questions }
  }
  return { questions: [] }
}

export default function Verification() {
  const [showAttemptError, setShowAttemptError] = useState(false)
  const verificationQuestionsQuery = useQuery({
    queryKey: ['verification-questions'],
    queryFn: getVerificationQuestionsFn,
    refetchOnMount: true,
  })

  if (!verificationQuestionsQuery.isFetchedAfterMount) {
    return (
      <div className="viewport">
        <h2>Loading...</h2>
      </div>
    )
  }

  if (verificationQuestionsQuery.isError) {
    if (verificationQuestionsQuery.error instanceof QueryError) {
      if (verificationQuestionsQuery.error.status === 404 || verificationQuestionsQuery.error.status === 409) {
        return <Navigate to="/get-started" replace />
      }
    }
    return (
      <div className="viewport">
        <h2>An error occurred</h2>
      </div>
    )
  }

  if (verificationQuestionsQuery.data.questions.length !== 2) {
    return <Navigate to="/verification-failed" replace />
  }

  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <div className="">
        {showAttemptError ? (
          <h3>We couldn&apos;t verify your identity, please try again</h3>
        ) : (
          <h3>We&apos;ve successfully located your details</h3>
        )}
        <p>
          To continue with your claim, you will need to verify your identity by answering 2 security questions based on your
          most recent loan. You have three attempts to get both answers correct. If we can&apos;t confirm your identity, you
          will not be able to proceed with your claim.
        </p>
        <br />
      </div>
      <Form
        questions={verificationQuestionsQuery.data.questions}
        onAttemptError={() => { setShowAttemptError(true) }}
      />
    </div>
  )
}
