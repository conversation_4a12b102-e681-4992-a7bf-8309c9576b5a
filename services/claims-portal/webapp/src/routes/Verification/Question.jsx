import PropTypes from 'prop-types'
import DateInput from '../../components/DateInput/index.jsx'
import TextInput from '../../components/TextInput/index.jsx'

/**
 * @typedef QuestionProps
 * @property {import('./types.ts').QuestionType} type
 * @property {string} label
 * @property {string} placeholder
 * @property {string} [tooltip]
 * @property {Date | number | string} [value]
 * @property {string} [errorText]
 * @property {(value: any) => void} onChange
 */

/**
 * @param {QuestionProps} props
 */
export default function Question({ id, type, label, placeholder, tooltip, value, errorText, onChange }) {
  switch (type) {
    case 'date':
      return (
        <DateInput
          id={id}
          label={label}
          placeholder={placeholder}
          tooltip={tooltip}
          value={value}
          errorText={errorText}
          onChange={onChange}
        />
      )
    case 'email':
    case 'number':
    case 'phone':
    case 'text':
      return (
        <TextInput
          id={id}
          label={label}
          placeholder={placeholder}
          tooltip={tooltip}
          value={value ?? ''}
          errorText={errorText}
          onChange={onChange}
        />
      )
    default:
      return null
  }
}

Question.propTypes = {
  id: PropTypes.string.isRequired,
  type: PropTypes.oneOf(['text', 'date', 'phone', 'email', 'number']).isRequired,
  label: PropTypes.string.isRequired,
  placeholder: PropTypes.string.isRequired,
  tooltip: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]),
  errorText: PropTypes.string,
  onChange: PropTypes.func.isRequired,
}
