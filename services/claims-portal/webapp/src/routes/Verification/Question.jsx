import PropTypes from 'prop-types'
import DateInput from '../../components/DateInput/index.jsx'
import MoneyInput from '../../components/MoneyInput/index.jsx'
import MonthYearInput from '../../components/MonthYearInput/index.jsx'
import NumericInput from '../../components/NumericInput/index.jsx'
import SortCodeInput from '../../components/SortCodeInput/index.jsx'
import TextInput from '../../components/TextInput/index.jsx'

/**
 * @typedef QuestionProps
 * @property {import('./types.ts').QuestionType} type
 * @property {string} label
 * @property {number} [maxLength]
 * @property {string} [placeholder]
 * @property {boolean} [readonly]
 * @property {string} [tooltip]
 * @property {number | string} [value]
 * @property {string} [errorText]
 * @property {(value: any) => void} onChange
 */

/**
 * @param {QuestionProps} props
 */
export default function Question({
  errorText,
  id,
  label,
  maxLength,
  placeholder,
  readonly,
  tooltip,
  type,
  value,
  onChange,
}) {
  switch (type) {
    case 'date':
      return (
        <DateInput
          errorText={errorText}
          id={id}
          label={label}
          placeholder={placeholder}
          readonly={readonly}
          required
          tooltip={tooltip}
          value={value}
          onChange={(text, date) => { onChange([text, date]) }}
        />
      )
    case 'money':
      return (
        <MoneyInput
          errorText={errorText}
          id={id}
          label={label}
          maxLength={maxLength}
          placeholder={placeholder}
          readonly={readonly}
          required
          tooltip={tooltip}
          value={value}
          onChange={onChange}
        />
      )
    case 'monthyear':
      return (
        <MonthYearInput
          errorText={errorText}
          id={id}
          label={label}
          placeholder={placeholder}
          readonly={readonly}
          required
          tooltip={tooltip}
          value={value}
          onChange={onChange}
        />
      )
    case 'numeric':
      return (
        <NumericInput
          errorText={errorText}
          id={id}
          label={label}
          maxLength={maxLength}
          placeholder={placeholder}
          readonly={readonly}
          required
          tooltip={tooltip}
          value={value ?? ''}
          onChange={onChange}
        />
      )
    case 'sortcode':
      return (
        <SortCodeInput
          errorText={errorText}
          id={id}
          label={label}
          placeholder={placeholder}
          readonly={readonly}
          required
          tooltip={tooltip}
          value={value ?? ''}
          onChange={onChange}
        />
      )
    case 'text':
    case 'email':
      return (
        <TextInput
          errorText={errorText}
          id={id}
          label={label}
          maxLength={maxLength}
          placeholder={placeholder}
          readonly={readonly}
          required
          tooltip={tooltip}
          value={value ?? ''}
          onChange={onChange}
        />
      )
    default:
      return null
  }
}

Question.propTypes = {
  id: PropTypes.string.isRequired,
  type: PropTypes.oneOf(['date', 'email', 'money', 'monthyear', 'numeric', 'sortcode', 'text']).isRequired,
  label: PropTypes.string.isRequired,
  maxLength: PropTypes.number,
  placeholder: PropTypes.string.isRequired,
  readonly: PropTypes.bool,
  tooltip: PropTypes.string,
  value: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.number, PropTypes.string]),
  errorText: PropTypes.string,
  onChange: PropTypes.func.isRequired,
}
