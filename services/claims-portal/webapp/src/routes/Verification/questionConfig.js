import { validateDate, validateEmail, validateMoney, validate<PERSON><PERSON><PERSON><PERSON>ear, validateText } from '../../schema.js'

/** @type {import('./types.ts').QuestionConfig} */
const questionConfig = {
  driving_licence_number: {
    id: 'driving_licence_number',
    type: 'text',
    label: 'What was your Driving Licence Number?',
    maxLength: 18,
  },
  passport_number: {
    id: 'passport_number',
    type: 'numeric',
    label: 'What was your Passport Number linked to your loan agreement?',
    maxLength: 10,
  },
  agreement_number: {
    id: 'agreement_number',
    type: 'text',
    label: 'What was your Loan Agreement Number?',
    maxLength: 14,
  },
  date_of_birth: {
    id: 'date_of_birth',
    type: 'date',
    label: 'What is your Date of Birth?',
  },
  account_number: {
    id: 'account_number',
    type: 'numeric',
    label: 'What are the last four digits of the bank account number that you used to make your monthly payments?',
    maxLength: 4,
    minLength: 4,
  },
  monthly_payment: {
    id: 'monthly_payment',
    type: 'money',
    label: 'How much was your monthly payment?',
    maxLength: 10,
    placeholder: '00.00',
  },
  mobile_phone_number: {
    id: 'mobile_phone_number',
    type: 'numeric',
    label: 'What are the last four digits of your mobile phone number?',
    maxLength: 4,
    minLength: 4,
  },
  sort_code: {
    id: 'sort_code',
    type: 'sortcode',
    label: 'What is the Sort Code of the bank account number that you used to make your monthly payments?',
  },
  email_address: {
    id: 'email_address',
    type: 'email',
    label: 'What was the email you registered for your account?',
  },
  personal_phone_number: {
    id: 'personal_phone_number',
    type: 'numeric',
    label: 'What are the last four digits of your personal phone number?',
    maxLength: 4,
    minLength: 4,
  },
  vehicle_registration_number: {
    id: 'vehicle_registration_number',
    type: 'text',
    label: 'What was the Registration Number of the vehicle linked to your loan agreement?',
    maxLength: 7,
  },
  loan_inception: {
    id: 'loan_inception',
    type: 'monthyear',
    label: 'What month and year did your loan start?',
  },
}

const safeConfig = Object.assign(Object.create(null), questionConfig)

/**
 * @param {string} id
 */
export function getQuestionById(id) {
  return safeConfig[id]
}

/**
 * @param {import('./types.ts').Question} question
 * @param {number | string | [string, Date | null]} value
 */
export function validateInput(question, value) {
  switch (question.type) {
    case 'date': {
      return validateDate(value, { required: true })
    }
    case 'email': {
      return validateEmail(value, { required: true })
    }
    case 'money': {
      return validateMoney(value, { required: true })
    }
    case 'monthyear': {
      return validateMonthYear(value, { required: true })
    }
    case 'numeric': {
      return validateText(value, { required: true, minLength: question.minLength })
    }
    case 'sortcode': {
      return validateText(value, { required: true, minLength: 6 })
    }
    case 'text': {
      return validateText(value, { required: true, alphanumeric: true, minLength: question.minLength })
    }
    default:
      return ''
  }
}
