/** @type {import('./types.ts').QuestionConfig} */
const questionConfig = {
  driving_licence_number: {
    id: 'driving_licence_number',
    type: 'text',
    label: 'What was your Driving Licence Number linked to your loan agreement?',
    placeholder: 'TODO',
  },
  passport_number: {
    id: 'passport_number',
    type: 'text',
    label: 'What was your Passport Number linked to your loan agreement?',
    placeholder: 'TODO',
  },
  agreement_number: {
    id: 'agreement_number',
    type: 'text',
    label: 'What was your Loan Agreement Number?',
    placeholder: 'TODO',
  },
  date_of_birth: {
    id: 'date_of_birth',
    type: 'date',
    label: 'What is your Date of Birth?',
    placeholder: undefined,
  },
  account_number: {
    id: 'account_number',
    type: 'text',
    label: 'What are the last four digits of the bank account number that you used to make your monthly payments?',
    placeholder: 'TODO',
  },
  monthly_amount: {
    id: 'monthly_amount',
    type: 'number',
    label: 'How much was your monthly payment?',
    placeholder: 'TODO',
  },
  mobile_phone_number: {
    id: 'mobile_phone_number',
    type: 'phone',
    label: 'What was the Mobile Phone Number you registered for your account?',
    placeholder: 'TODO',
  },
  sort_code: {
    id: 'sort_code',
    type: 'text',
    label: 'What is the Sort Code of the bank account number that you used to make your monthly payments?',
    placeholder: 'TODO',
  },
  email_address: {
    id: 'email_address',
    type: 'email',
    label: 'What was the email you registered for your account?',
    placeholder: 'TODO',
  },
  personal_phone_number: {
    id: 'personal_phone_number',
    type: 'phone',
    label: 'What was the Personal Phone Number you registered for your account?',
    placeholder: 'TODO',
  },
  vehicle_registration_number: {
    id: 'vehicle_registration_number',
    type: 'text',
    label: 'What was the Registration Number of the vehicle linked to your loan agreement?',
    placeholder: 'TODO',
  },
}

const safeConfig = Object.assign(Object.create(null), questionConfig)

/**
 * @param {string} id
 */
export function getQuestionById(id) {
  return safeConfig[id]
}
