import { useState } from 'react'
import PropTypes from 'prop-types'
import { useNavigate } from 'react-router'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { v4 as uuid } from 'uuid'
import { request, handleMutationError } from '../../query.js'
import ActionLink from '../../components/ActionLink/index.jsx'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import { validateInput } from './questionConfig.js'
import Question from './Question.jsx'

/**
 * @param {Date} date
 */
function convertDateForApi(date) {
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
}

async function submitFn({ data, idempotencyKey }) {
  return request({
    url: '/api/verification-questions',
    method: 'POST',
    body: data,
    headers: { 'x-idempotency-key': idempotencyKey },
    errorMessage: 'Unable to submit additional info',
  })
}

/**
 * @typedef Question
 * @property {string} id
 * @property {import('./types.ts').QuestionType} type
 * @property {string} label
 * @property {string} placeholder
 * @property {string} [tooltip]
 */

/**
 * @param {{ questions: Question[], onAttemptError: () => void }} props
 */
export default function VerificationForm({ questions, onAttemptError }) {
  const [showErrors, setShowErrors] = useState(false)
  const [formState, setFormState] = useState({})
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  const handleInputChange = (id, value) => {
    setFormState(prev => ({ ...prev, [id]: value }))
  }

  let errorCount = 0
  const errors = questions.reduce((acc, question) => {
    const error = validateInput(question, formState[question.id])
    if (error !== '') {
      errorCount += 1
    }
    acc[question.id] = error
    return acc
  }, {})

  const mutation = useMutation({
    mutationFn: submitFn,
    onSuccess(data) {
      if (data.success === true) {
        navigate('/contact-details', { replace: true })
      } else if (data.fail === true) {
        navigate('/verification-failed', { replace: true })
      } else {
        // Refresh questions
        queryClient.invalidateQueries({ queryKey: ['verification-questions'] })
        onAttemptError()
      }
    },
    onError(error) {
      handleMutationError(error, navigate)
    },
  })

  const handleSubmit = (event) => {
    event.preventDefault()
    if (errorCount !== 0 || mutation.isPending) {
      setShowErrors(true)
      return
    }
    const answers = questions.reduce((acc, question) => {
      if (question.type === 'date') {
        acc[question.id] = convertDateForApi(formState[question.id][1])
      } else {
        acc[question.id] = formState[question.id]
      }
      return acc
    }, {})
    mutation.mutate({ data: { answers }, idempotencyKey: uuid() })
  }

  const handleSkip = () => {
    setShowErrors(false)
    mutation.mutate({ data: { skip: true }, idempotencyKey: uuid() })
  }

  return (
    <form onSubmit={handleSubmit}>
      {questions.map(question => (
        <Question
          key={question.id}
          id={question.id}
          type={question.type}
          label={question.label}
          maxLength={question.maxLength}
          placeholder={question.placeholder}
          readonly={mutation.isPending}
          tooltip={question.tooltip}
          value={question.type === 'date' ? formState[question.id]?.[0] : formState[question.id]}
          errorText={showErrors ? errors[question.id] : ''}
          onChange={(value) => { handleInputChange(question.id, value) }}
        />
      ))}
      <p>Don&apos;t have these details?</p>
      <ActionLink disabled={mutation.isPending} onClick={handleSkip}>Skip these questions</ActionLink>
      <Button submit disabled={mutation.isPending}>Submit</Button>
      {mutation.isError ? (
        <Alert
          text="Something went wrong, please try again"
          type="error"
        />
      ) : null}
    </form>
  )
}

VerificationForm.propTypes = {
  questions: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  onAttemptError: PropTypes.func.isRequired,
}
