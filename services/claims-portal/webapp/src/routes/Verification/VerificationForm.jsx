import { useState } from 'react'
import PropTypes from 'prop-types'
import { useNavigate } from 'react-router'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import QueryError from '../../query.js'
import ActionLink from '../../components/ActionLink/index.jsx'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import Question from './Question.jsx'

/**
 * @param {Date} date
 */
function convertDateForApi(date) {
  return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`
}

/**
 * @param {import('./types.ts').QuestionType} type
 * @param {number | string} value
 */
function validateValue(type, value) {
  if (type === 'text') {
    if (value === '') {
      return 'This field is required'
    }
    return ''
  }
  return ''
}

async function submitFn(data) {
  const result = await fetch('/api/verification-questions', {
    method: 'POST',
    body: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json' },
    signal: AbortSignal.timeout(10000),
  })
  if (!result.ok) {
    throw new QueryError('Unable to submit additional info', result.status)
  }
  return result.json()
}

/**
 * @typedef Question
 * @property {string} id
 * @property {import('./types.ts').QuestionType} type
 * @property {string} label
 * @property {string} placeholder
 * @property {string} [tooltip]
 */

/**
 * @param {{ questions: Question[], onAttemptError: () => void }} props
 */
export default function VerificationForm({ questions, onAttemptError }) {
  const [showErrors, setShowErrors] = useState(false)
  const [formState, setFormState] = useState({})
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  const handleInputChange = (id, value) => {
    setFormState(prev => ({ ...prev, [id]: value }))
  }

  let errorCount = 0
  const errors = questions.reduce((acc, question) => {
    const error = validateValue(question.type, formState[question.id] ?? '')
    if (error !== '') {
      errorCount += 1
    }
    acc[question.id] = error
    return acc
  }, {})

  const mutation = useMutation({
    mutationFn: submitFn,
    onSuccess(data) {
      if (data.success === true) {
        navigate('/claim-complete', { replace: true })
      } else if (data.fail === true) {
        navigate('/verification-failed', { replace: true })
      } else {
        // Refresh questions
        queryClient.invalidateQueries({ queryKey: ['verification-questions'] })
        onAttemptError()
      }
    },
    onError(error) {
      if (error instanceof QueryError && error.status === 409) {
        navigate('/', { replace: true })
      }
    },
  })

  const handleSubmit = (event) => {
    event.preventDefault()
    if (errorCount !== 0 || mutation.isPending) {
      setShowErrors(true)
      return
    }
    const answers = questions.reduce((acc, question) => {
      if (question.type === 'date') {
        acc[question.id] = convertDateForApi(formState[question.id])
      } else {
        acc[question.id] = formState[question.id]
      }
      return acc
    }, {})
    mutation.mutate({ answers })
  }

  const handleSkip = () => {
    mutation.mutate({ skip: true })
  }

  return (
    <form onSubmit={handleSubmit}>
      {questions.map(question => (
        <Question
          key={question.id}
          id={question.id}
          type={question.type}
          label={question.label}
          placeholder={question.placeholder}
          tooltip={question.tooltip}
          value={formState[question.id]}
          errorText={showErrors ? errors[question.id] : ''}
          onChange={(value) => { handleInputChange(question.id, value) }}
        />
      ))}
      <p>Don&apos;t have these details?</p>
      <ActionLink text="Skip these questions" disabled={mutation.isPending} onClick={handleSkip} />
      <Button submit disabled={mutation.isPending}>Submit</Button>
      {mutation.isError ? (
        <Alert
          text="Something went wrong, please try again"
          type="error"
        />
      ) : null}
    </form>
  )
}

VerificationForm.propTypes = {
  questions: PropTypes.arrayOf(PropTypes.shape({})).isRequired,
  onAttemptError: PropTypes.func.isRequired,
}
