import { useQuery } from '@tanstack/react-query'
import { request } from '../../query.js'
import QueryResponseError from '../../components/QueryResponseError.jsx'

async function getClaimResultFn() {
  return request({
    url: '/api/result',
    method: 'GET',
    errorMessage: 'Unable to get claim result',
  })
}

function Accepted() {
  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <p>Thank you for providing your payment details.</p>
      <p>
        As part of our commitment to putting things right, your payment is now
        being processed.
      </p>
      <p>
        This process typically takes up to 15 working days to complete. During
        this time, our team will be working carefully to ensure everything is
        handled smoothly and securely.
      </p>
      <p>
        Thank you again for your understanding and for giving us the opportunity
        to make things right.
      </p>
    </div>
  )
}

function Mix() {
  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <p>Your redress decisions have been recorded.</p>
      <p>Thank you for providing your payment details.</p>
      <p>
        As part of our commitment to putting things right, your payment is now processed. This process typically takes up to
        15 working days to complete. During this time, our team will be working carefully to ensure everything is handled
        smoothly and securely.
      </p>
      <p>
        You have declined one or more redress offers.
        In line with FCA guidance <strong>[PLACEHOLDER for REGULATORY WORDING]</strong>
      </p>
    </div>
  )
}

function Declined() {
  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <p>We are sorry that you are not satisied with the decision.</p>
      <p>
        <strong>[Placeholder for regulatory wording].</strong>
      </p>
      <p>
        <strong>[Placeholder for FAQ redirection].</strong>
      </p>
    </div>
  )
}

function Ineligible() {
  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <p>Thank you for taking the time to review your details.</p>
      <p>
        Having checked our records, we can confirm that we charged you the
        correct amount for your motor finance agreement.
      </p>
      <p>
        <strong>[Placeholder for regulatory wording].</strong>
      </p>
      <p>
        <strong>[Placeholder for FAQ redirection].</strong>
      </p>
    </div>
  )
}

function Retry() {
  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <h3>Claim Previously Completed</h3>
      <p>Thank you for the information you have provided.</p>
      <p>Our records indicate that you claim is already complete. You do not need to take any further action.</p>
      <p>If you have not previously made a claim, please contact us on +44........</p>
    </div>
  )
}

export default function ClaimComplete() {
  const claimResultQuery = useQuery({
    queryKey: ['claim-result'],
    queryFn: getClaimResultFn,
    refetchOnMount: false,
  })

  if (claimResultQuery.isLoading) {
    return (
      <div className="viewport">
        <p>Loading...</p>
      </div>
    )
  }

  if (claimResultQuery.isError) {
    return (<QueryResponseError error={claimResultQuery.error} />)
  }

  switch (claimResultQuery.data) {
    case 'accepted':
      return <Accepted />
    case 'mix':
      return <Mix />
    case 'declined':
      return <Declined />
    case 'ineligible':
      return <Ineligible />
    case 'retry':
      return <Retry />
    default:
      return (
        <div className="viewport">
          <p>Something went wrong</p>
        </div>
      )
  }
}
