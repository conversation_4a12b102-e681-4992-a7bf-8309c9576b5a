import { useQuery } from '@tanstack/react-query'
import { request } from '../../query.js'
import QueryResponseError from '../../components/QueryResponseError.jsx'
import ContactDetailsForm from './ContactDetailsForm.jsx'
import ClaimsStepper from '../../components/ClaimsStepper.jsx'

async function getContactDetailsFn() {
  return request({
    url: '/api/contact-details',
    method: 'GET',
    errorMessage: 'Unable to get contact details',
  })
}

export default function ContactDetails() {
  const contactDetailsQuery = useQuery({
    queryKey: ['contact-details'],
    queryFn: getContactDetailsFn,
  })

  if (contactDetailsQuery.isLoading) {
    return (
      <div className="viewport">
        <h2>Loading...</h2>
      </div>
    )
  }

  if (contactDetailsQuery.isError) {
    return (<QueryResponseError error={contactDetailsQuery.error} />)
  }

  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <ClaimsStepper step={1} />
      <br />
      <h3>Are These Your Current Contact Details?</h3>
      <p className="italic">
        Please take a moment to confirm that your name, email address, mobile number and address are correct.
        If anything has changed, update your details before continuing.
        We&apos;ll use these details for any future communications.
      </p>
      <br />
      <h3>Personal Information</h3>
      <ContactDetailsForm submitted={contactDetailsQuery.data.submitted} details={contactDetailsQuery.data.details} />
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
        aliqua.
      </p>
    </div>
  )
}
