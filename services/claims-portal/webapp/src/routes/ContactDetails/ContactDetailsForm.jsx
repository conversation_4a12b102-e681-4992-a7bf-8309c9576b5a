import { useState } from 'react'
import PropTypes from 'prop-types'
import { useNavigate } from 'react-router'
import { useMutation } from '@tanstack/react-query'
import { v4 as uuid } from 'uuid'
import { request, handleMutationError } from '../../query.js'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import ButtonGroup from '../../components/ButtonGroup/index.jsx'
import NumericInput from '../../components/NumericInput/index.jsx'
import PostcodeInput from '../../components/PostcodeInput/index.jsx'
import TextInput from '../../components/TextInput/index.jsx'
import { validateEmail, validateMobilePhone, validatePostcode, validateText } from '../../schema.js'

/**
 * @typedef ContactDetails
 * @property {string} [forename]
 * @property {string} [surname]
 * @property {string} [emailAddress]
 * @property {string} [mobilePhoneNumber]
 * @property {string} [addressLineOne]
 * @property {string} [addressLineTwo]
 * @property {string} [addressLineThree]
 * @property {string} [addressPostcode]
 */

/**
 * @param {{ data: ContactDetails; idempotencyKey: string }} input
 */
async function submitContactDetailsFn({ data, idempotencyKey }) {
  return request({
    url: '/api/contact-details',
    method: 'POST',
    body: data,
    headers: { 'x-idempotency-key': idempotencyKey },
    errorMessage: 'Unable to submit contact details',
  })
}

async function confirmContactDetailsFn({ idempotencyKey }) {
  return request({
    url: '/api/confirm-contact-details',
    method: 'POST',
    headers: { 'x-idempotency-key': idempotencyKey },
    errorMessage: 'Unable to confirm contact details',
  })
}

/**
 * @param {{ submitted: boolean; details: ContactDetails }} props
 */
export default function ContactDetailsForm({ submitted, details }) {
  const [showErrors, setShowErrors] = useState(false)
  const [canEdit, setCanEdit] = useState(submitted !== true)
  const [forename, setForename] = useState(details.forename || '')
  const [surname, setSurname] = useState(details.surname || '')
  const [emailAddress, setEmailAddress] = useState(details.emailAddress || '')
  const [mobilePhoneNumber, setMobilePhoneNumber] = useState(details.mobilePhoneNumber || '')
  const [addressLineOne, setAddressLineOne] = useState(details.addressLineOne || '')
  const [addressLineTwo, setAddressLineTwo] = useState(details.addressLineTwo || '')
  const [addressLineThree, setAddressLineThree] = useState(details.addressLineThree || '')
  const [addressPostcode, setAddressPostcode] = useState(details.addressPostcode || '')

  const navigate = useNavigate()

  const forenameError = validateText(forename, { required: true, restrictSpecialChars: true })
  const surnameError = validateText(surname, { required: true, restrictSpecialChars: true })
  const emailAddressError = validateEmail(emailAddress, { required: true })
  const mobilePhoneNumberError = validateMobilePhone(mobilePhoneNumber, { required: true })
  const addressLineOneError = validateText(addressLineOne, { required: true, restrictSpecialChars: true })
  const addressLineTwoError = validateText(addressLineTwo, { required: false, restrictSpecialChars: true })
  const addressLineThreeError = validateText(addressLineThree, { required: false, restrictSpecialChars: true })
  const addressPostcodeError = validatePostcode(addressPostcode, { required: true })

  const submitMutation = useMutation({
    mutationFn: submitContactDetailsFn,
    onSuccess() {
      setCanEdit(false)
    },
    onError(error) {
      handleMutationError(error, navigate)
    },
  })

  const confirmMutation = useMutation({
    mutationFn: confirmContactDetailsFn,
    onSuccess() {
      navigate('/loan-details', { replace: true })
    },
    onError(error) {
      handleMutationError(error, navigate)
    },
  })

  const handleSubmit = (event) => {
    event.preventDefault()
    if (submitMutation.isPending || forenameError || surnameError || emailAddressError || mobilePhoneNumberError
    || addressLineOneError || addressLineTwoError || addressLineThreeError || addressPostcodeError) {
      setShowErrors(true)
      return
    }
    submitMutation.mutate({
      data: {
        forename,
        surname,
        emailAddress,
        mobilePhoneNumber,
        addressLineOne,
        addressLineTwo,
        addressLineThree,
        addressPostcode,
      },
      idempotencyKey: uuid(),
    })
  }

  const handleConfirm = () => {
    if (confirmMutation.isPending) return
    confirmMutation.mutate({ idempotencyKey: uuid() })
  }

  return (
    <form onSubmit={handleSubmit}>
      <TextInput
        errorText={showErrors ? forenameError : ''}
        id="forename"
        label="First Name"
        maxLength={50}
        readonly={!canEdit || submitMutation.isPending}
        required
        value={forename}
        onChange={setForename}
      />
      <TextInput
        errorText={showErrors ? surnameError : ''}
        id="surname"
        label="Last Name"
        maxLength={50}
        readonly={!canEdit || submitMutation.isPending}
        required
        value={surname}
        onChange={setSurname}
      />
      <TextInput
        errorText={showErrors ? emailAddressError : ''}
        id="email_address"
        label="Email Address"
        maxLength={254}
        readonly={!canEdit || submitMutation.isPending}
        required
        value={emailAddress}
        onChange={setEmailAddress}
      />
      <NumericInput
        errorText={showErrors ? mobilePhoneNumberError : ''}
        helpText="Please start with 07. Do not include +44."
        id="mobile_phone_number"
        label="Mobile Phone Number"
        maxLength={11}
        readonly={!canEdit || submitMutation.isPending}
        required
        value={mobilePhoneNumber}
        onChange={setMobilePhoneNumber}
      />
      <p>Address on File</p>
      <TextInput
        errorText={showErrors ? addressLineOneError : ''}
        id="address_line_one"
        label="Line 1"
        maxLength={100}
        readonly={!canEdit || submitMutation.isPending}
        required
        value={addressLineOne}
        onChange={setAddressLineOne}
      />
      <TextInput
        errorText={showErrors ? addressLineTwoError : ''}
        id="address_line_two"
        label="Line 2 (optional)"
        maxLength={100}
        readonly={!canEdit || submitMutation.isPending}
        value={addressLineTwo}
        onChange={setAddressLineTwo}
      />
      <TextInput
        errorText={showErrors ? addressLineThreeError : ''}
        id="address_line_three"
        label="Line 3 (optional)"
        maxLength={100}
        readonly={!canEdit || submitMutation.isPending}
        value={addressLineThree}
        onChange={setAddressLineThree}
      />
      <PostcodeInput
        errorText={showErrors ? addressPostcodeError : ''}
        id="address_postcode"
        label="Postcode"
        readonly={!canEdit || submitMutation.isPending}
        required
        value={addressPostcode}
        onChange={setAddressPostcode}
      />
      {canEdit ? (
        <Button submit disabled={submitMutation.isPending}>Submit</Button>
      ) : (
        <>
          <p>Please review the details you have entered to ensure they are correct.</p>
          <ButtonGroup>
            <Button type="secondary" disabled={confirmMutation.isPending} onClick={() => { setShowErrors(false); setCanEdit(true) }}>
              Edit Details
            </Button>
            <Button disabled={confirmMutation.isPending} onClick={handleConfirm}>
              Confirm Details
            </Button>
          </ButtonGroup>
        </>
      )}
      {submitMutation.isError || confirmMutation.isError ? (
        <Alert
          text="Something went wrong, please try again"
          type="error"
        />
      ) : null}
    </form>
  )
}

ContactDetailsForm.propTypes = {
  submitted: PropTypes.bool.isRequired,
  details: PropTypes.shape({
    forename: PropTypes.string,
    surname: PropTypes.string,
    emailAddress: PropTypes.string,
    mobilePhoneNumber: PropTypes.string,
    addressLineOne: PropTypes.string,
    addressLineTwo: PropTypes.string,
    addressLineThree: PropTypes.string,
    addressPostcode: PropTypes.string,
  }).isRequired,
}
