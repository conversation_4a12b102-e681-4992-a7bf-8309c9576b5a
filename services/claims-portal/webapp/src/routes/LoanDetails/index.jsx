import { useNavigate } from 'react-router'
import { useQuery, useMutation } from '@tanstack/react-query'
import { v4 as uuid } from 'uuid'
import { request, handleMutationError, queryClient } from '../../query.js'
import QueryResponseError from '../../components/QueryResponseError.jsx'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import ClaimsStepper from '../../components/ClaimsStepper.jsx'
import Table from '../../components/Table/index.jsx'
import { columns } from './table.jsx'

async function getLoansSummaryFn() {
  const data = await request({
    url: '/api/loans-summary',
    method: 'GET',
    errorMessage: 'Unable to get loans summary',
  })
  return Array.isArray(data) ? data : []
}

async function confirmOffersFn({ idempotencyKey }) {
  return request({
    url: '/api/confirm-loan-offers',
    method: 'POST',
    headers: { 'x-idempotency-key': idempotencyKey },
    errorMessage: 'Unable to confirm loan offers',
  })
}

export default function LoanDetails() {
  const navigate = useNavigate()

  const loansSummaryQuery = useQuery({
    queryKey: ['loans-summary'],
    queryFn: getLoansSummaryFn,
    staleTime: 60_000,
  })

  const mutation = useMutation({
    mutationFn: confirmOffersFn,
    onSuccess(data) {
      queryClient.setQueryData(['claim-result'], data)
      if (data === 'accepted' || data === 'mix') {
        navigate('/payment-details', { replace: true })
      } else {
        navigate('/complete', { replace: true })
      }
    },
    onError(error) {
      handleMutationError(error, navigate)
    },
  })

  if (loansSummaryQuery.isFetching) {
    return (
      <div className="viewport">
        <h2>Loading...</h2>
      </div>
    )
  }

  if (loansSummaryQuery.isError) {
    return (<QueryResponseError error={loansSummaryQuery.error} />)
  }

  const continueDisabled = mutation.isPending || loansSummaryQuery.data.some(loan => loan.status === 'ready')

  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <ClaimsStepper step={2} />
      <br />
      <h3>Loan Agreement Details</h3>
      <br />
      <p>
        Offers have been generated for the eligible agreements below.
        <br />
        Please review each one and select how you&apos;d like to proceed.
      </p>
      <br />
      <Table
        columns={columns}
        data={loansSummaryQuery.data}
      />
      <br />
      <Button
        disabled={continueDisabled}
        onClick={() => { mutation.mutate({ idempotencyKey: uuid() }) }}
      >
        Continue
      </Button>
      {mutation.isError ? (
        <Alert
          text="Something went wrong, please try again"
          type="error"
        />
      ) : null}
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
        aliqua.
      </p>
    </div>
  )
}
