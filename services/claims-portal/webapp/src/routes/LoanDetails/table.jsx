import { createColumnHelper } from '@tanstack/react-table'
import ViewOfferLink from './ViewOfferLink.jsx'

const columnHelper = createColumnHelper()

export const columns = [
  columnHelper.display({
    id: 'description',
    header: 'Description of Loan',
    cell: ({ row }) => (
      <>
        <u><strong>Agreement Number - {row.original.agreementNumber}</strong></u>
        <br />
        Vehicle Model - {row.original.vehicleModel}
        <br />
        Vehicle Registration Number - {row.original.vehicleRegistrationNumber}
      </>
    ),
  }),
  columnHelper.accessor('eligible', {
    header: 'Eligibility for Redress',
    cell: ({ getValue }) => {
      const eligible = getValue()
      return eligible ? 'Eligible' : 'Ineligible'
    },
  }),
  columnHelper.display({
    id: 'offerDetails',
    header: 'Offer Details',
    cell: ({ row }) => {
      if (row.original.eligible) {
        return <ViewOfferLink agreementNumber={row.original.agreementNumber} />
      }
      return 'N/A'
    },
  }),
  columnHelper.accessor('status', {
    header: 'Status',
    cell: ({ getValue }) => {
      const status = getValue()
      if (status === null) {
        return <strong>N/A</strong>
      }
      if (status === 'ready') {
        return <strong>Ready for Review</strong>
      }
      if (status === 'accepted') {
        return <strong>Accepted</strong>
      }
      if (status === 'declined') {
        return <strong>Declined</strong>
      }
      return null
    },
  }),
]
