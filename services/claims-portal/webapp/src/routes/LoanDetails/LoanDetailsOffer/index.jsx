import { useEffect, useState } from 'react'
import { useNavigate, useParams, Navigate } from 'react-router'
import { useQuery, useMutation } from '@tanstack/react-query'
import { v4 as uuid } from 'uuid'
import { QueryError, request, handleMutationError, queryClient } from '../../../query.js'
import QueryResponseError from '../../../components/QueryResponseError.jsx'
import { asGBP, asPercentage } from '../../../format.js'
import ActionLink from '../../../components/ActionLink/index.jsx'
import Alert from '../../../components/Alert/index.jsx'
import ButtonGroup from '../../../components/ButtonGroup/index.jsx'
import Button from '../../../components/Button/index.jsx'
import RadioButtonGroup from '../../../components/RadioButtonGroup/index.jsx'
import TextInput from '../../../components/TextInput/index.jsx'

const decisionOptions = [
  {
    label: 'Accept - By accepting, I agree with the reimbursement amount as compensation for the overcharged amount.',
    value: 'accept',
  },
  {
    label: 'Decline - By declining, I disagree with the reimbursement amount as compensation.',
    value: 'decline',
  },
]

async function getLoanOfferFn({ queryKey }) {
  return request({
    url: `/api/loan-offer?agreementNumber=${queryKey[1]}`,
    method: 'GET',
    errorMessage: 'Unable to get loan offer',
  })
}

async function submitDecisionFn({ data, idempotencyKey }) {
  return request({
    url: '/api/loan-offer',
    method: 'POST',
    body: data,
    headers: { 'x-idempotency-key': idempotencyKey },
    errorMessage: 'Unable to submit loan decision',
  })
}

function preventDefault(e) {
  e.preventDefault()
}

export default function LoanDetailsOffer() {
  const [showErrors, setShowErrors] = useState(false)
  const [decision, setDecision] = useState('')
  const [declineReason, setDeclineReason] = useState('')
  const navigate = useNavigate()
  const params = useParams()

  const loanOfferQuery = useQuery({
    queryKey: ['loan-offer', params.agreementNumber],
    queryFn: getLoanOfferFn,
    staleTime: 60_000,
  })

  useEffect(() => {
    if (loanOfferQuery.data && loanOfferQuery.data.decision !== undefined) {
      setDecision(prev => (prev === '' ? loanOfferQuery.data.decision.value : prev))
      setDeclineReason(prev => (prev === '' ? loanOfferQuery.data.decision.reason : prev))
    }
  }, [loanOfferQuery.data])

  const mutation = useMutation({
    mutationFn: submitDecisionFn,
    onSuccess() {
      queryClient.invalidateQueries({ queryKey: ['loans-summary'] })
      queryClient.invalidateQueries({ queryKey: ['loan-offer', params.agreementNumber] })
      navigate(-1)
    },
    onError(error) {
      handleMutationError(error, navigate)
    },
  })

  if (loanOfferQuery.isFetching) {
    return (
      <div className="viewport">
        <h2>Loading...</h2>
      </div>
    )
  }

  if (loanOfferQuery.isError) {
    if (loanOfferQuery.error instanceof QueryError && loanOfferQuery.error.status === 404) {
      return (<Navigate to={-1} />)
    }
    return (<QueryResponseError error={loanOfferQuery.error} />)
  }

  const decisionError = decision === '' ? 'You will need to Accept or Decline the offer to continue' : ''
  const declineReasonError = decision === 'decline' && declineReason === '' ? 'You will need to select a reason to continue' : ''

  const handleSubmit = (e) => {
    e.preventDefault()
    if (decisionError || declineReasonError || mutation.isPending) {
      setShowErrors(true)
      return
    }

    mutation.mutate({
      data: {
        agreementNumber: params.agreementNumber,
        decision: {
          value: decision,
          reason: decision === 'decline' ? declineReason : undefined,
        },
      },
      idempotencyKey: uuid(),
    })
  }

  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <br />
      <h4>You are due compensation from us on your motor finance agreement.</h4>
      <br />
      <p>
        Having checked our records, we can confirm that you were overcharged on your motor finance agreement with us.
        This means that you are due compensation.
      </p>
      <p>
        We&apos;re sorry that we haven&apos;t got this right and we would like to reimburse you the outstanding amount
        of {asGBP(loanOfferQuery.data.total)}. This includes the amount you were overcharged, and the the statutory
        interest on the compensation. This value has been calculated in line with the FCA&apos;s regulatory guidance for this
        redress scheme, for further information on how this is calculated <ActionLink element="a" href="/">click here</ActionLink>.
        <br />
        The details of this calculation are presented below:
      </p>
      <form onSubmit={preventDefault}>
        <TextInput
          id="paid-actual"
          label="What you paid"
          readonly
          value={asGBP(loanOfferQuery.data.paidActual + 0.1)}
        />
        <TextInput
          id="apr-actual"
          label="Your APR"
          readonly
          value={asPercentage(loanOfferQuery.data.aprActual)}
        />
        <TextInput
          id="apr-expected"
          label="What your APR should have been"
          readonly
          value={asPercentage(loanOfferQuery.data.aprExpected)}
        />
        <TextInput
          id="paid-expected"
          label="What you should have paid"
          readonly
          value={asGBP(loanOfferQuery.data.paidExpected)}
        />
        <TextInput
          id="refund"
          label="What refund you are owed"
          readonly
          value={asGBP(loanOfferQuery.data.refund)}
        />
        <TextInput
          id="interest"
          label="8% Statutory Interest on your refund *(minus tax deducted)"
          readonly
          value={asGBP(loanOfferQuery.data.interest)}
        />
        <TextInput
          id="total"
          label="Total Refund"
          readonly
          value={asGBP(loanOfferQuery.data.total)}
        />
      </form>
      <p>
        This offer has been made in line with the terms of the scheme. Please review this offer and either accept or reject
        below.
      </p>
      <form onSubmit={handleSubmit}>
        <RadioButtonGroup
          errorText={showErrors ? decisionError : ''}
          name="accept-decline"
          options={decisionOptions}
          readonly={mutation.isPending}
          required
          value={decision}
          wide
          onChange={setDecision}
        />
        {decision === 'decline' ? (
          <RadioButtonGroup
            errorText={showErrors ? declineReasonError : ''}
            label="Please could we ask you to select your reason for declining the offer from one of the following options:"
            name="decline-reason"
            options={[
              { label: 'I do not wish to receive compensation on this agreement', value: 'no_compensation' },
              { label: 'The details of the claim are incorrect', value: 'details_incorrect' },
              { label: 'I do not understand how the payment has been calculated', value: 'calculation_unclear' },
              { label: 'I wish to talk to someone about my claim', value: 'talk_to_someone' },
              { label: 'Other', value: 'other' },
            ]}
            readonly={mutation.isPending}
            required
            value={declineReason}
            wide
            onChange={setDeclineReason}
          />
        ) : null}
        <ButtonGroup>
          <Button
            disabled={mutation.isPending}
            type="secondary"
            onClick={() => { navigate(-1) }}
          >
            Back to Loans
          </Button>
          <Button
            disabled={mutation.isPending}
            submit
            type="primary"
          >
            Submit
          </Button>
        </ButtonGroup>
        {mutation.isError ? (
          <Alert
            text="Something went wrong, please try again"
            type="error"
          />
        ) : null}
      </form>
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
        aliqua.
      </p>
    </div>
  )
}
