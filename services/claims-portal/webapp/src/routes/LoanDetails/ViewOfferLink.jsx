import PropTypes from 'prop-types'
import { useNavigate } from 'react-router'
import ActionLink from '../../components/ActionLink/index.jsx'

/**
 * @param {{ agreementNumber: string }} props
 */
export default function ViewOfferLink({ agreementNumber }) {
  const navigate = useNavigate()

  return (
    <ActionLink
      onClick={() => navigate(`/loan-details/${agreementNumber}`)}
    >
      View Offer
    </ActionLink>
  )
}

ViewOfferLink.propTypes = { agreementNumber: PropTypes.string.isRequired }
