import { useNavigate } from 'react-router'
import styles from './LandingPage.module.scss'
import Button from '../../components/Button/index.jsx'

export default function LandingPage() {
  const navigate = useNavigate()
  return (
    <div className="viewport">
      <div className={styles.center}>
        <h2>Customer Claim Portal</h2>
        <p className="italic">Helping you check if you&apos;re eligible for a refund on your motor finance agreement.</p>
      </div>
      <p>
        If you used motor finance to purchase a vehicle, you may have been overcharged due to the way commission was
        arranged on your loan.
      </p>
      <p>We&apos;re here to help you check whether that applies to you and guide you through making a claim.</p>
      <p>Click here to find out more about the Motor Finance Commission redress scheme.</p>
      <p className="italic">Before you start, make sure you have the following ready:</p>
      <ul>
        <li>
          Claim ID (this will be on your letter/email, if you have received one from us. While this ID is not
          mandatory, it will help us to review your claim promptly)
        </li>
        <li>Loan documentation</li>
        <li>Driver&apos;s licence</li>
      </ul>

      <h2>Let&apos;s Get Started</h2>
      <br />
      <p style={{ textAlign: 'center' }}>
        This portal will take you through a few quick steps to check if your loan is eligible. Once verified, you&apos;ll
        be able to make a claim and securely confirm where your payment should be sent, if due.
      </p>
      <br />
      <div className={styles.buttonContainer}>
        <Button onClick={() => navigate('/get-started')}>Get Started</Button>
      </div>
    </div>
  )
}
