import { useNavigate } from 'react-router'
import styles from './LandingPage.module.scss'
import ActionLink from '../../components/ActionLink/index.jsx'
import Button from '../../components/Button/index.jsx'

export default function LandingPage() {
  const navigate = useNavigate()
  return (
    <div className="viewport">
      <div className={styles.center}>
        <h2>Customer Claim Portal</h2>
        <p className="italic">Helping you check if you&apos;re eligible for a financial redress on your motor finance agreement.</p>
      </div>
      <p>
        If you used motor finance to purchase a vehicle, you may have been overcharged due to the way commission was
        arranged on your loan.
      </p>
      <p>We&apos;re here to help you check whether that applies to you and guide you through making a claim.</p>
      <p>To find out more about the Motor Finance Commission redress scheme <ActionLink element="a" href="/">click here</ActionLink>.</p>
      <p><strong>Before you start</strong></p>
      <p className="italic">
        It&apos;s helpful to have any of the following information ready, if available, to make your journey smoother:
      </p>
      <ul>
        <li>
          Claim ID (This will be on your letter/email, if you have received one from us. While this ID is not
          mandatory, it will help us to review your claim promptly.)
        </li>
        <li>Loan information and any associated documentation</li>
        <li>Vehicle information and any associated documentation</li>
        <li>Personal records such as your driving licence</li>
      </ul>
      <br />
      <h2>Let&apos;s Get Started</h2>
      <p style={{ textAlign: 'center' }}>
        This portal will take you through a few quick steps to check if your loan is eligible. Once verified, you&apos;ll
        be able to make a claim and securely confirm where your payment should be sent, if due.
      </p>
      <br />
      <div className={styles.buttonContainer}>
        <Button onClick={() => navigate('/get-started')}>Get Started</Button>
      </div>
    </div>
  )
}
