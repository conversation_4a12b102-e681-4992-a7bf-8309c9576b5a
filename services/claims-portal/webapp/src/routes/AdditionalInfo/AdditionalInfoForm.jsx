import { useState } from 'react'
import { useNavigate } from 'react-router'
import { useMutation } from '@tanstack/react-query'
import { v4 as uuid } from 'uuid'
import { request, handleMutationError } from '../../query.js'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import PostcodeInput from '../../components/PostcodeInput/index.jsx'
import { validatePostcode } from '../../schema.js'

async function submitFn({ data, idempotencyKey }) {
  return request({
    url: '/api/additional-info',
    method: 'POST',
    body: data,
    headers: { 'x-idempotency-key': idempotencyKey },
    errorMessage: 'Unable to submit additional info',
  })
}

export default function AdditionalInfoForm() {
  const [showErrors, setShowErrors] = useState(false)
  const [postcode, setPostcode] = useState('')
  const navigate = useNavigate()

  const mutation = useMutation({
    mutationFn: submitFn,
    onSuccess() {
      navigate('/verification', { replace: true })
    },
    onError(error) {
      handleMutationError(error, navigate)
    },
  })

  const postcodeError = validatePostcode(postcode, { required: true })

  const handleSubmit = (event) => {
    event.preventDefault()
    if (postcodeError || mutation.isPending) {
      setShowErrors(true)
      return
    }
    mutation.mutate({ data: { postcode }, idempotencyKey: uuid() })
  }

  return (
    <form onSubmit={handleSubmit}>
      <PostcodeInput
        errorText={showErrors ? postcodeError : ''}
        id="postcode"
        label="Postcode linked to your loan agreement*"
        readonly={mutation.isPending}
        required
        tooltip="TBD"
        value={postcode}
        onChange={setPostcode}
      />
      <Button submit disabled={mutation.isPending}>Submit</Button>
      {mutation.isError ? (
        <Alert
          text="We couldn't find a match based on the details you provided. Please check your information or try using the details you provided at the time of your loan agreement"
          type="error"
        />
      ) : null}
    </form>
  )
}
