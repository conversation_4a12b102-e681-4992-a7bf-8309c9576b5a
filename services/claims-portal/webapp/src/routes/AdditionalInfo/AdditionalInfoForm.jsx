import { useState } from 'react'
import { useNavigate } from 'react-router'
import { useMutation } from '@tanstack/react-query'
import QueryError from '../../query.js'
import Alert from '../../components/Alert/index.jsx'
import Button from '../../components/Button/index.jsx'
import TextInput from '../../components/TextInput/index.jsx'
import styles from './AdditionalInfo.module.scss'

const postcodeRegex = /^[A-Z0-9\s]+$/i

function validatePostcode(postcode) {
  if (postcode.length === 0) {
    return 'This field is required'
  }
  if (!postcodeRegex.test(postcode)) {
    return 'Invalid data format'
  }
  return ''
}

async function submitFn(data) {
  const result = await fetch('/api/additional-info', {
    method: 'POST',
    body: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json' },
    signal: AbortSignal.timeout(10000),
  })
  if (!result.ok) {
    throw new QueryError('Unable to submit additional info', result.status)
  }
}

export default function AdditionalInfoForm() {
  const [showErrors, setShowErrors] = useState(false)
  const [postcode, setPostcode] = useState('')
  const navigate = useNavigate()

  const mutation = useMutation({
    mutationFn: submitFn,
    onSuccess() {
      navigate('/verification', { replace: true })
    },
    onError(error) {
      if (error instanceof QueryError && error.status === 409) {
        navigate('/', { replace: true })
      }
    },
  })

  const postcodeError = validatePostcode(postcode)

  const handleSubmit = (event) => {
    event.preventDefault()
    if (postcodeError || mutation.isPending) {
      setShowErrors(true)
      return
    }
    mutation.mutate({ postcode })
  }

  return (
    <form onSubmit={handleSubmit}>
      <TextInput
        id="postcode"
        className={styles.postcodeInput}
        label="Postcode linked to your loan agreement*"
        placeholder="Postcode"
        maxLength={10}
        tooltip="TBD"
        value={postcode}
        errorText={showErrors ? postcodeError : ''}
        onChange={setPostcode}
      />
      <Button submit disabled={mutation.isPending}>Submit</Button>
      {mutation.isError ? (
        <Alert
          text="We couldn't find a match based on the details you provided. Please check your information or try using the details you provided at the time of your loan agreement"
          type="error"
        />
      ) : null}
    </form>
  )
}
