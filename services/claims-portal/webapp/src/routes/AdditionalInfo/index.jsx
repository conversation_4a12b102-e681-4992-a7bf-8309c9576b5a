import { Navigate } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import QueryError from '../../query.js'
import Form from './AdditionalInfoForm.jsx'

const getAdditionalInfoStatusFn = async () => {
  const result = await fetch('/api/additional-info-status', {
    method: 'GET',
    signal: AbortSignal.timeout(10000),
  })
  if (!result.ok) {
    throw new QueryError('Unable to get match status', result.status)
  }
  return result.json()
}

export default function AdditionalInfo() {
  const additionalInfoStatusQuery = useQuery({
    queryKey: ['additional-info-status'],
    queryFn: getAdditionalInfoStatusFn,
  })

  if (additionalInfoStatusQuery.isLoading) {
    return <h2>Loading...</h2>
  }

  if (additionalInfoStatusQuery.isError) {
    if (additionalInfoStatusQuery.error instanceof QueryError) {
      if (additionalInfoStatusQuery.error.status === 404 || additionalInfoStatusQuery.error.status === 409) {
        return <Navigate to="/get-started" replace />
      }
    }
    return <h2>An error occurred</h2>
  }

  if (additionalInfoStatusQuery.data.status === 'verify') {
    return (
      <Navigate to="/verification" replace />
    )
  }

  return (
    <div className="viewport">
      <h2>Customer Claim Portal</h2>
      <div className="">
        <h3>We need a bit more information</h3>
        <p className="italic">
          We need a few more details to be sure we&apos;ve got the right record.
          <br />
          Please complete the fields below so we can continue processing your claim securely.
          <br />
          Fields marked with * are mandatory.
        </p>
        <p>If you need assistance, please reach out to our Customer Claims team at (0)20 564827120</p>
        <h3>Personal information</h3>
        <br />
      </div>
      <Form />
    </div>
  )
}
