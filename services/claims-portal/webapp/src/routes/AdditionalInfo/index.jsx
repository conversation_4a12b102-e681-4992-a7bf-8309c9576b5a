import { useQuery } from '@tanstack/react-query'
import { request } from '../../query.js'
import QueryResponseError from '../../components/QueryResponseError.jsx'
import Form from './AdditionalInfoForm.jsx'

async function getAdditionalInfoStatusFn() {
  return request({
    url: '/api/additional-info-status',
    method: 'GET',
    errorMessage: 'Unable to get additional info status',
  })
}

export default function AdditionalInfo() {
  const additionalInfoStatusQuery = useQuery({
    queryKey: ['additional-info-status'],
    queryFn: getAdditionalInfoStatusFn,
  })

  if (additionalInfoStatusQuery.isLoading) {
    return <h2>Loading...</h2>
  }

  if (additionalInfoStatusQuery.isError) {
    return (<QueryResponseError error={additionalInfoStatusQuery.error} />)
  }

  return (
    <div className="viewport">
      <h2>Customer <PERSON>laim <PERSON></h2>
      <div className="">
        <h3>We need a bit more information</h3>
        <p className="italic">
          We need a few more details to be sure we&apos;ve got the right record.
          <br />
          Please complete the fields below so we can continue processing your claim securely.
          <br />
          Fields marked with * are mandatory.
        </p>
        <h3>Personal information</h3>
        <br />
      </div>
      <Form />
      <p>
        Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
        aliqua.
      </p>
    </div>
  )
}
