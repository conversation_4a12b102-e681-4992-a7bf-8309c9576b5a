import path from 'path'
import url from 'url'
import CompressionPlugin from 'compression-webpack-plugin'
import HtmlWebpackPlugin from 'html-webpack-plugin'
import MiniCssExtractPlugin from 'mini-css-extract-plugin'
import CssMinimizerPlugin from 'css-minimizer-webpack-plugin'
import TerserPlugin from 'terser-webpack-plugin'
import webpack from 'webpack'
import { merge } from 'webpack-merge'
import webpackConfig from './webpack.common.js'

const dir = url.fileURLToPath(new URL('.', import.meta.url))

const SRC_DIR = path.join(dir, 'src')
const DIST_DIR = path.join(dir, 'dist')

export default merge(webpackConfig, {
  mode: 'production',
  entry: {
    polyfill: 'core-js/stable',
    src: path.resolve(SRC_DIR, 'main.js'),
  },
  optimization: {
    minimizer: [
      new CssMinimizerPlugin(),
      new TerserPlugin({
        extractComments: false,
        terserOptions: { output: { comments: false } },
      }),
    ],
    splitChunks: {
      cacheGroups: {
        vendor: {
          test: /node_modules/,
          chunks: 'initial',
          name: 'vendor',
          enforce: true,
        },
      },
    },
  },
  output: {
    path: DIST_DIR,
    filename: '[name]/[chunkhash].min.js',
    assetModuleFilename: 'assets/[contenthash][ext][query]',
  },
  plugins: [
    new HtmlWebpackPlugin({
      filename: 'index.html',
      inject: 'body',
      template: path.resolve(SRC_DIR, 'index.html'),
      chunks: ['polyfill', 'src'],
    }),
    new MiniCssExtractPlugin({ filename: '[name]/[contenthash].css' }),
    new webpack.optimize.AggressiveMergingPlugin(),
    new CompressionPlugin({
      filename: '[path][base].br',
      algorithm: 'brotliCompress',
      test: /\.(js|css|html|svg)$/,
      compressionOptions: { level: 11 },
      threshold: 10240,
    }),
    new CompressionPlugin({
      filename: '[path][base].gz',
      algorithm: 'gzip',
      test: /\.(js|css|html|svg)$/,
      threshold: 10240,
    }),
  ],
})
