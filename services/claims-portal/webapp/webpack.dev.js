import path from 'path'
import url from 'url'
import HtmlWebpackPlugin from 'html-webpack-plugin'
import MiniCssExtractPlugin from 'mini-css-extract-plugin'
import ReactRefreshPlugin from '@pmmmwh/react-refresh-webpack-plugin'
import { merge } from 'webpack-merge'
import webpackConfig from './webpack.common.js'

const SRC_DIR = url.fileURLToPath(new URL('./src', import.meta.url))

export default merge(webpackConfig, {
  mode: 'development',
  devServer: {
    compress: true,
    historyApiFallback: true,
    hot: true,
    port: 9000,
    proxy: [
      {
        context: ['/plausible.js'],
        target: 'http://127.0.0.1:4000',
        secure: false,
      },
      {
        context: ['/api/**'],
        target: 'http://127.0.0.1:4000',
        secure: false,
      },
    ],
  },
  devtool: 'inline-source-map',
  entry: path.resolve(SRC_DIR, 'main.js'),
  optimization: { runtimeChunk: true },
  output: {
    assetModuleFilename: 'assets/[name].[contenthash][ext][query]',
    pathinfo: false,
  },
  plugins: [
    new ReactRefreshPlugin(),
    new HtmlWebpackPlugin({
      filename: 'index.html',
      inject: 'body',
      template: path.resolve(SRC_DIR, 'index.html'),
    }),
    new MiniCssExtractPlugin(),
  ],
})
