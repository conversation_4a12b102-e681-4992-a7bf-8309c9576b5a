import path from 'path'
import url from 'url'
import MiniCssExtractPlugin from 'mini-css-extract-plugin'

const dir = url.fileURLToPath(new URL('.', import.meta.url))

const SRC_DIR = path.join(dir, 'src')
const MODULES_DIR = path.join(dir, 'node_modules')

export default {
  module: {
    rules: [
      {
        test: /\.jsx?$/,
        include: [
          SRC_DIR,
          path.resolve(MODULES_DIR, 'ansi-regex'),
          path.resolve(MODULES_DIR, 'strip-ansi'),
        ],
        loader: 'babel-loader',
      },
      {
        include: [path.resolve(SRC_DIR, 'public/favicon.ico')],
        type: 'asset/resource',
        generator: { filename: 'favicon.ico' },
      },
      {
        test: /\.(sa|sc|c)ss$/,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              modules: {
                auto: true,
                namedExport: false,
              },
            },
          },
          'sass-loader',
        ],
      },
      {
        test: /\.(png|svg|jp(e)?g|gif|woff(2)?|eot|ttf|otf)(\?v=[0-9]\.[0-9]\.[0-9])?$/i,
        type: 'asset/resource',
      },
      {
        test: /\.md$/,
        type: 'asset/source',
      },
    ],
  },
  output: { publicPath: '/' },
  resolve: {
    extensions: ['.js', '.jsx'],
    modules: ['node_modules', MODULES_DIR],
    symlinks: false,
  },
  target: ['web', 'es5'],
}
