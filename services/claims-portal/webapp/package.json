{"name": "santander", "version": "1.0.0", "private": true, "type": "module", "engines": {"node": "22"}, "scripts": {"lint": "eslint --ext .jsx --ext .js src", "fix-lint": "eslint --ext .jsx --ext .js --fix src", "test": "echo \"No tests\"", "start": "webpack serve --config webpack.dev.js", "review-bundle": "NODE_ENV=production webpack --config webpack.prod.js --profile --json > bundle-stats.json && webpack-bundle-analyzer bundle-stats.json ./dist", "build": "rm -rf dist && NODE_ENV=production webpack --config webpack.prod.js"}, "dependencies": {"@babel/preset-env": "7.26.9", "@babel/preset-react": "7.26.3", "@base-ui-components/react": "1.0.0-beta.0", "@tanstack/react-query": "5.80.10", "@tanstack/react-table": "8.21.3", "babel-loader": "10.0.0", "clsx": "1.1.1", "compression-webpack-plugin": "11.1.0", "core-js": "3.41.0", "css-loader": "7.1.2", "css-minimizer-webpack-plugin": "7.0.2", "html-webpack-plugin": "5.6.3", "mini-css-extract-plugin": "2.9.2", "prop-types": "15.8.1", "react": "19.1.0", "react-dom": "19.1.0", "react-router": "7.6.2", "react-widgets": "5.8.6", "rxjs": "6.6.3", "sass": "1.89.2", "sass-loader": "16.0.5", "terser-webpack-plugin": "5.3.14", "uuid": "11.1.0", "webpack": "5.99.9", "webpack-cli": "6.0.1", "webpack-merge": "6.0.1"}, "devDependencies": {"@kpmg-uk/eslint-config-cdd-react-js": "1.0.2", "@pmmmwh/react-refresh-webpack-plugin": "0.6.0", "react-refresh": "0.17.0", "webpack-bundle-analyzer": "4.10.2", "webpack-dev-server": "5.2.2", "webpack-hot-middleware": "2.26.1"}}