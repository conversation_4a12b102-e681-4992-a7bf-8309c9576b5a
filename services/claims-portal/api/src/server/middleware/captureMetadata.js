import crypto from 'crypto'
import { APP_NAME } from '../../constants.js'
import logger from '../../logger.js'
import { als } from '../../utils/asyncLocalStorage.js'

/** @type {import('express').RequestHandler} */
export default (req, res, next) => {
  const correlationId = crypto.randomUUID()
  const requestId = crypto.randomUUID()

  const store = als.getStore()
  store.app.set('apiHeaders', {
    'x-correlation-id': correlationId,
    'x-api-consumer': APP_NAME,
  })

  logger.setRootArg('correlationId', correlationId)
  logger.setRootArg('requestId', requestId)

  next()
}
