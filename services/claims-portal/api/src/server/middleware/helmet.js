import helmet from 'helmet'
import { fromEnv } from '../../constants.js'

const contentSecurityPolicy = {
  directives: {
    defaultSrc: ["'self'"],
    imgSrc: ["'self'", 'data:'],
    scriptSrc: [
      "'self'",
      "'sha256-lbsuZcRaS7BAuua30Ml1RaJECrA96pscF9NpbcRTkQA='", // anti-framing html script
    ],
    styleSrc: [
      "'self'",
      "'unsafe-inline'",
    ],
    frameAncestors: ["'none'"], // prevent clickjacking
  },
}

try {
  const plausibleOrigin = new URL(fromEnv('PLAUSIBLE_SCRIPT_URL')).origin
  contentSecurityPolicy.directives.scriptSrc.push(plausibleOrigin)
  contentSecurityPolicy.directives.connectSrc = ["'self'", plausibleOrigin]
} catch {
  // Script URL not provided, ignore
}

const referrerPolicy = { policy: ['no-referrer', 'strict-origin-when-cross-origin'] }

export const htmlSecurity = helmet({
  contentSecurityPolicy,
  referrerPolicy,
})

export const staticSecurity = helmet({
  contentSecurityPolicy: false,
  frameguard: false,
  referrerPolicy,
  xssFilter: false,
})

export const xhrSecurity = helmet({
  contentSecurityPolicy: false,
  frameguard: false,
  referrerPolicy,
  xssFilter: false,
})
