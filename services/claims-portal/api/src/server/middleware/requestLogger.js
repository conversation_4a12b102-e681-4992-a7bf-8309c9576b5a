import morgan from 'morgan'
import logger from '../../logger.js'

const skip = (req, res) => (req.path === '/liveness' || req.path === '/readiness') && res.statusCode === 200

const write = (data) => {
  const request = JSON.parse(data)

  const message = request.status === '-'
    ? 'Request cancelled'
    : 'Request processed'

  logger.log('info', message, { request })
}

const fallback = '-'

const format = (tokens, req, res) => JSON.stringify({
  remote_addr: tokens['remote-addr'](req) ?? fallback,
  body_bytes_received: tokens.req(req, res, 'content-length') ?? '0',
  body_bytes_sent: tokens.res(req, res, 'content-length') ?? '0',
  response_time: tokens['response-time'](req, res) ?? fallback,
  status: tokens.status(req, res) ?? fallback,
  request_method: tokens.method(req) ?? fallback,
  request_url: tokens.url(req) ?? fallback,
  http_version: tokens['http-version'](req) ?? fallback,
  http_referrer: tokens.referrer(req) ?? fallback,
  http_user_agent: tokens['user-agent'](req) ?? fallback,
})

/** @type {import('express').RequestHandler} */
export default (req, res, next) => {
  const middleware = morgan(format, { skip, stream: { write: logger.bindArgs(write) } })
  middleware(req, res, next)
}
