import session from 'express-session'
import { RedisStore } from 'connect-redis'
import { Phunk } from 'phunky'
import { fromEnv, SESSION_IDLE_TIMEOUT, SESSION_MAX_TIMEOUT, SESSION_MIDDLEWARE_TTL } from '../../constants.js'
import logger from '../../logger.js'
import { getRedisClient } from '../../remoteServices/connectors/redis.js'
import { getCookieSecrets } from '../../remoteServices/cookieSecrets.js'

export const redisSessionKeyPrefix = 'claims-portal:session:'

const sessionStore = new RedisStore({
  client: getRedisClient(),
  prefix: redisSessionKeyPrefix,
  ttl: () => SESSION_IDLE_TIMEOUT / 1000,
})

const cookieName = 'sid'
const cookiePath = '/'

const sessionMiddlewarePhunk = new Phunk(async () => session({
  name: cookieName,
  store: sessionStore,
  secret: await getCookieSecrets(),
  resave: false,
  saveUninitialized: false,
  proxy: true,
  cookie: {
    path: cookiePath,
    httpOnly: true,
    secure: fromEnv('LOCALHOST_MODE') !== 'true',
    sameSite: 'strict',
    maxAge: SESSION_MAX_TIMEOUT,
  },
}), { ttl: SESSION_MIDDLEWARE_TTL })

/** @type {import('express').RequestHandler} */
const useSession = async (req, res, next) => {
  const middleware = await sessionMiddlewarePhunk.current()
  middleware(req, res, next)
}

/** @type {import('express').RequestHandler} */
export const assertSession = (req, res, next) => {
  if (!req.session) {
    logger.warn('No session active, possible path misconfiguration')
    res.sendStatus(500)
    return
  }
  next()
}

export default [useSession, assertSession]

/**
 * @param {import('express').Response} res
 */
export const clearCookie = (res) => {
  res.clearCookie(cookieName, { path: cookiePath, httpOnly: true })
}
