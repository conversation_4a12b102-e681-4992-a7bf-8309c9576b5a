import { fromEnv } from '../../constants.js'

/** @type {import('express').RequestHandler} */
export default (req, res) => {
  res.set('content-type', 'application/javascript')
  res.set('cache-control', 'max-age=86400, immutable')
  if (fromEnv('LOCALHOST_MODE') === 'true') {
    res.send('// noop') // Do nothing on localhost script
    return
  }

  const script = `var plausibleScript = document.createElement('script');
plausibleScript.defer = true;
plausibleScript.setAttribute('data-domain', '${fromEnv('PUBLIC_HOST')}');
plausibleScript.src = '${fromEnv('PLAUSIBLE_SCRIPT_URL')}';
document.body.appendChild(plausibleScript);
`

  res.send(script)
}
