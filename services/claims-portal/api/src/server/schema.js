import * as z from 'zod'

const alphanumericRegex = /^[A-Z0-9]+$/i
const alphanumericSpaceRegex = /^[A-Z0-9 ]+$/i
const dateRegex = /^\d{4}-\d{2}-\d{2}$/
const emailRegex = /^[^\s@]+@[^\s@]+$/
const invalidCharRegex = /[!@£$%^&*()_+=[\]{}:;/\\?|~<>]/g
const mobilePhoneRegex = /^07\d{9}$/
const moneyRegex = /^\d+\.\d{2}$/
const monthYearRegex = /^\d{2}\/\d{4}$/
const numericRegex = /^\d+$/
const postcodeRegex = /^(([A-Z]{1,2}[0-9]{1,2})|([A-Z]{1,2}[0-9][A-Z]))[ ]?[0-9][A-Z]{2}$/

export function createAlphanumericSchema({
  minLength = 1,
  maxLength = 100,
} = {}) {
  return z.string()
    .min(minLength)
    .max(maxLength)
    .regex(alphanumericRegex)
}

export function createAlphanumericSpaceSchema({
  trim = false,
  minLength = 1,
  maxLength = 100,
} = {}) {
  return (trim ? z.string().trim() : z.string())
    .min(minLength)
    .max(maxLength)
    .regex(alphanumericSpaceRegex)
}

export function createNumericSchema({
  length,
}) {
  return (length === undefined ? z.string() : z.string().length(length))
    .regex(numericRegex)
}

export function createSimpleTextSchema({
  trim = false,
  minLength = 1,
  maxLength = 100,
} = {}) {
  return (trim ? z.string().trim() : z.string())
    .min(minLength)
    .max(maxLength)
    .refine(val => !invalidCharRegex.test(val), { error: 'String contains invalid special characters' })
}

export const dateSchema = z.string().length(10).regex(dateRegex)

export const emailSchema = z.string().max(254).trim().regex(emailRegex)

export const mobilePhoneSchema = z.string().length(11).regex(mobilePhoneRegex)

export const moneySchema = z.string().max(10).regex(moneyRegex)

export const monthYearSchema = z.string().length(7).regex(monthYearRegex).refine((val) => {
  const month = +val.split('/')[0]
  return month >= 1 && month <= 12
})

export const postcodeSchema = z.string()
  .max(8)
  .overwrite(val => val.replaceAll(' ', '').toUpperCase())
  .max(7)
  .min(5)
  .regex(postcodeRegex)
