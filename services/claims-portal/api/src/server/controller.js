import { HttpCallError } from '@restatedev/restate-sdk-clients'
import logger from '../logger.js'
import { destroySession } from './session.js'

/**
 * @typedef ControllerOptions
 * @property {string} [idempotencyKey]
 * @property {AbortSignal} signal
 */

/**
 * @typedef ControllerResult
 * @property {number} [status]
 * @property {*} [body]
 * @property {Record<string,string|number>} [headers]
 * @property {import('stream').Readable} [stream]
 */

/**
 * @callback Controller
 * @param {import('express').Request} req
 * @param {ControllerOptions} options
 * @returns {ControllerResult | Promise<ControllerResult>}
 */

/**
 * @param {Controller} controller
 * @returns {import('express').RequestHandler}
 */
export const asCon = controller => async (req, res) => {
  const abortController = new AbortController()

  res.on('close', () => {
    abortController.abort()
  })

  let result
  try {
    result = await controller(req, { idempotencyKey: getIdempotencyKey(req), signal: abortController.signal })
    if (abortController.signal.aborted) { return }
  } catch (error) {
    if (error instanceof HttpCallError) {
      if (error.status === 401) {
        await destroySession(req)
      }
      res.sendStatus(error.status)
      return
    }
    if (error instanceof Error) {
      if (error.name === 'AbortError') { return }
      logger.error(error, 'Uncaught error in controller')
    }
    res.sendStatus(500)
    return
  }

  if (result === null || typeof result !== 'object') {
    res.end()
    return
  }

  if (result.headers !== null && typeof result.headers === 'object') {
    if (Object.keys(result.headers).length > 0) {
      res.setHeaders(result.headers)
    }
  }

  if (result.status !== undefined) {
    res.status(result.status)
  }

  if (result.body !== undefined) {
    res.send(result.body)
  } else if (result.stream !== undefined) {
    result.stream.pipe(res)
  } else {
    res.end()
  }
}

/**
 * @param {import('express').Request} req
 */
export function getIdempotencyKey(req) {
  const idempotencyKeyHeader = req.headers['x-idempotency-key']
  if (typeof idempotencyKeyHeader === 'string' && idempotencyKeyHeader.length > 0) {
    return idempotencyKeyHeader
  }
  return undefined
}
