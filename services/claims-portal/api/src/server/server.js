import express from 'express'
import { fromEnv } from '../constants.js'
import logger from '../logger.js'
import asyncLocalStorage from './middleware/asyncLocalStorage.js'
import captureMetadata from './middleware/captureMetadata.js'
import plausible from './middleware/plausible.js'
import requestLogger from './middleware/requestLogger.js'
import uncaughtError from './middleware/uncaughtError.js'
import api from './routes/api.js'
import liveness from './routes/liveness.js'
import swaggerUi from 'swagger-ui-express'
import { swaggerSpec } from '../swagger/swagger-spec.js'
import notFound from './routes/notFound.js'
import readiness from './routes/readiness.js'
import { webappContent, webappIndex } from './routes/webapp.js'

const port = fromEnv('PORT') ?? 4000

const updatedSwaggerSpec = {
  ...swaggerSpec,
  servers: [
    {
      url: `http://localhost:${port}/api`,
      description: 'Development server',
    },
  ],
}

express()
  .set('etag', false)
  .set('x-powered-by', false)
  .use(asyncLocalStorage)
  .use(requestLogger)
  .get('/plausible.js', plausible)
  .get('/liveness', liveness)
  .get('/readiness', readiness)
  .use(captureMetadata)
  .use('/api', api)
  .use('/api-docs', swaggerUi.serve, swaggerUi.setup(updatedSwaggerSpec))
  .get(
    ['/assets/{*splat}', '/polyfill/{*splat}', '/src/{*splat}', '/vendor/{*splat}', '/favicon.ico'],
    webappContent,
  )
  .get('/{*splat}', webappIndex)
  .use('{*splat}', notFound)
  .use(uncaughtError)
  .listen(port, (error) => {
    if (error) {
      logger.error(error, 'Failed to start express server')
    } else {
      logger.info('Started express server', { port })
    }
  })
