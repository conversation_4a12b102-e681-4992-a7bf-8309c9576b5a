import express from 'express'
import { fromEnv } from '../constants.js'
import logger from '../logger.js'
import asyncLocalStorage from './middleware/asyncLocalStorage.js'
import captureMetadata from './middleware/captureMetadata.js'
import plausible from './middleware/plausible.js'
import requestLogger from './middleware/requestLogger.js'
import uncaughtError from './middleware/uncaughtError.js'
import api from './routes/api.js'
import liveness from './routes/liveness.js'
import swaggerUi from 'swagger-ui-express'
import swaggerJsdoc from 'swagger-jsdoc'
import notFound from './routes/notFound.js'
import readiness from './routes/readiness.js'
import { webappContent, webappIndex } from './routes/webapp.js'
import { fileURLToPath } from 'url'
import { dirname } from 'path'
import path from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const port = fromEnv('PORT') ?? 4000

const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Claims Portal API',
      version: '1.0.0',
      description: 'API documentation for the Claims Portal application. This API handles the claims verification process including claim ID submission, personal details verification, additional information collection, and verification questions.',
      contact: {
        name: 'API Support',
        email: '<EMAIL>'
      }
    },
    servers: [
      {
        url: `http://localhost:${port}/api`,
        description: 'Development server'
      },
    ],
    components: {
      securitySchemes: {
        sessionAuth: {
          type: 'apiKey',
          in: 'cookie',
          name: 'connect.sid',
          description: 'Session-based authentication using Express sessions'
        }
      },
      schemas: {
        Error: {
          type: 'object',
          properties: {
            status: {
              type: 'integer',
              description: 'HTTP status code'
            },
            message: {
              type: 'string',
              description: 'Error message'
            }
          }
        }
      }
    },
    tags: [
      {
        name: 'Claims',
        description: 'Claims processing and verification endpoints'
      },
      {
        name: 'Health',
        description: 'Health check endpoints'
      }
    ]
  },
  apis: [
    path.join(__dirname, 'routes', 'api', '*.js'),
    path.join(__dirname, 'routes', '*.js')
  ], // Path to the API routes files
}

const swaggerSpec = swaggerJsdoc(swaggerOptions);

express()
  .set('etag', false)
  .set('x-powered-by', false)
  .use(asyncLocalStorage)
  .use(requestLogger)
  .get('/plausible.js', plausible)
  .get('/liveness', liveness)
  .get('/readiness', readiness)
  .use(captureMetadata)
  .use('/api', api)
  .use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerSpec))
  .get(
    ['/assets/{*splat}', '/polyfill/{*splat}', '/src/{*splat}', '/vendor/{*splat}', '/favicon.ico'],
    webappContent,
  )
  .get('/{*splat}', webappIndex)
  .use('{*splat}', notFound)
  .use(uncaughtError)
  .listen(port, (error) => {
    if (error) {
      logger.error(error, 'Failed to start express server')
    } else {
      logger.info('Started express server', { port })
    }
  })
