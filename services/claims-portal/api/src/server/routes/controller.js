import logger from '../../logger.js'

/**
 * @typedef ControllerOptions
 * @property {AbortSignal} signal
 */

/**
 * @typedef ControllerResult
 * @property {number} [status]
 * @property {*} [body]
 * @property {Record<string,string|number>} [headers]
 * @property {import('stream').Readable} [stream]
 */

/**
 * @callback Controller
 * @param {import('express').Request} req
 * @param {ControllerOptions} options
 * @returns {ControllerResult | Promise<ControllerResult>}
 */

/**
 * @param {Controller} controller
 * @returns {import('express').RequestHandler}
 */
export const asCon = controller => async (req, res) => {
  const abortController = new AbortController()

  res.on('close', () => {
    abortController.abort()
  })

  let result
  try {
    result = await controller(req, { signal: abortController.signal })
    if (abortController.signal.aborted) { return }
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') { return }
      logger.error(error, 'Uncaught error in controller')
    }
    res.sendStatus(500)
    return
  }

  if (result === null || typeof result !== 'object') {
    res.end()
    return
  }

  if (result.headers !== null && typeof result.headers === 'object') {
    if (Object.keys(result.headers).length > 0) {
      res.setHeaders(result.headers)
    }
  }

  if (result.status !== undefined) {
    res.status(result.status)
  }

  if (result.body !== undefined) {
    res.send(result.body)
  } else if (result.stream !== undefined) {
    result.stream.pipe(res)
  } else {
    res.end()
  }
}
