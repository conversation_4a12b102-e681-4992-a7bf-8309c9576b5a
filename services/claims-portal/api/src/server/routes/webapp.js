import fs from 'fs'
import path from 'path'
import url from 'url'
import expressStaticGzip from 'express-static-gzip'
import { Phunk } from 'phunky'
import { htmlSecurity, staticSecurity } from '../middleware/helmet.js'

const clientDir = path.join(url.fileURLToPath(new URL('.', import.meta.url)), '../../../../webapp/dist')

export const webappContent = [
  staticSecurity,
  expressStaticGzip(clientDir, {
    enableBrotli: true,
    index: false,
    serveStatic: {
      setHeaders: (res, filePath) => {
        if (filePath.endsWith('/favicon.ico')) {
          res.set('Cache-Control', 'max-age=3600, immutable')
        } else {
          res.set('Cache-Control', 'max-age=31536000, immutable')
        }
      },
    },
  }),
]

const indexCache = new Phunk(async () => {
  const encoding = 'utf8'
  const filePath = path.join(clientDir, 'index.html')
  const fileBuffer = await fs.promises.readFile(filePath, encoding)
  const stat = await fs.promises.stat(filePath)
  return {
    content: fileBuffer.toString(encoding),
    lastModified: stat.mtime.toUTCString(),
  }
})

export const webappIndex = [
  htmlSecurity,
  /** @type {import('express').RequestHandler} */
  async (req, res) => {
    const { content, lastModified } = await indexCache.current()
    res.set('Accept-Ranges', 'bytes')
    res.set('Cache-Control', 'max-age=180')
    res.set('Last-Modified', lastModified)
    res.send(content)
  },
]
