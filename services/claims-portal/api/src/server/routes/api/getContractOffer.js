import * as z from 'zod'
import { asCon } from '../../controller.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

const agreementNumberSchema = z.string().check(
  z.minLength(1),
  z.maxLength(50),
  z.regex(/^[A-Z0-9]+$/i, 'Invalid agreement number format'),
)

/** @type {import('express').RequestHandler} */
export const getContractOffer = asCon(async (req, { signal }) => {
  const { agreementNumber } = req.query
  const parsedAgreementNumber = agreementNumberSchema.safeParse(agreementNumber)
  if (!parsedAgreementNumber.success) {
    return { status: 400 }
  }

  const restate = getRestateClient()
  const result = await restate.objectClient(claimSesson, req.sessionID).getContractOffer(
    { contractId: parsedAgreementNumber.data },
    optsWithHeaders({ signal }),
  )
  return { status: 200, body: result }
})
