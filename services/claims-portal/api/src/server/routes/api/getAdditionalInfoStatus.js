import { HttpCallError } from '@restatedev/restate-sdk-clients'
import { asCon } from '../controller.js'
import { destroySession } from './session.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

/**
 * @swagger
 * /api/additional-info-status:
 *   get:
 *     summary: Get additional information status
 *     description: Retrieves the current status of additional information requirements for the claim
 *     tags:
 *       - Claims
 *     security:
 *       - sessionAuth: []
 *     responses:
 *       200:
 *         description: Additional info status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   description: Current status of additional information
 *                   example: "required"
 *                 required_fields:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of required additional information fields
 *                   example: ["postcode"]
 *       401:
 *         description: Unauthorized - session required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 401
 *       404:
 *         description: Session not found or expired
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 404
 *       409:
 *         description: Conflict - invalid session state
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 409
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 500
 */

/** @type {import('express').RequestHandler} */
export const getAdditionalInfoStatus = asCon(async (req, { signal }) => {
  const restate = getRestateClient()
  try {
    const result = await restate.objectClient(claimSesson, req.sessionID).getMatchState(optsWithHeaders({ signal }))
    return { status: 200, body: result }
  } catch (error) {
    if (error instanceof HttpCallError) {
      if (error.status === 404 || error.status === 409) {
        await destroySession(req)
      }
      return { status: error.status }
    }
    throw error
  }
})
