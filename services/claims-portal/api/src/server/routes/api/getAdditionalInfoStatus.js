import { HttpCallError } from '@restatedev/restate-sdk-clients'
import { asCon } from '../controller.js'
import { destroySession } from './session.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

/** @type {import('express').RequestHandler} */
export const getAdditionalInfoStatus = asCon(async (req, { signal }) => {
  const restate = getRestateClient()
  try {
    const result = await restate.objectClient(claimSesson, req.sessionID).getMatchState(optsWithHeaders({ signal }))
    return { status: 200, body: result }
  } catch (error) {
    if (error instanceof HttpCallError) {
      if (error.status === 404 || error.status === 409) {
        await destroySession(req)
      }
      return { status: error.status }
    }
    throw error
  }
})
