import * as z from 'zod'
import { asCon } from '../../controller.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'
import { createAlphanumericSchema } from '../../schema.js'

const inputSchema = z.object({
  agreementNumber: createAlphanumericSchema({ maxLength: 50 }),
  decision: z.discriminatedUnion('value', [
    z.object({ value: z.literal('accept') }),
    z.object({
      value: z.literal('decline'),
      reason: z.literal(['no_compensation', 'details_incorrect', 'calculation_unclear', 'talk_to_someone', 'other']),
    }),
  ]),
})

/** @type {import('express').RequestHandler} */
export const submitContractOfferDecision = asCon(async (req, { idempotencyKey, signal }) => {
  const parsedInput = inputSchema.safeParse(req.body)
  if (!parsedInput.success) {
    return { status: 400 }
  }

  const restate = getRestateClient()
  const result = await restate.objectClient(claimSesson, req.sessionID).submitOfferDecision(
    { contractId: parsedInput.data.agreementNumber, decision: parsedInput.data.decision },
    optsWithHeaders({ idempotencyKey, signal }),
  )
  return { status: 200, body: result }
})
