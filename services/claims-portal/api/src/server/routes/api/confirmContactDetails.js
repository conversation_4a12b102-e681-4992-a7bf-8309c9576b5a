import { asCon } from '../../controller.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

/** @type {import('express').RequestHandler} */
export const confirmContactDetails = asCon(async (req, { idempotencyKey, signal }) => {
  const restate = getRestateClient()
  const result = await restate.objectClient(claimSesson, req.sessionID).confirmContactDetails(
    optsWithHeaders({ idempotencyKey, signal }),
  )
  return { status: 200, body: result }
})
