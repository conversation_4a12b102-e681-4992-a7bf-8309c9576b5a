import { asCon } from '../../controller.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

/** @type {import('express').RequestHandler} */
export const getContractsSummary = asCon(async (req, { signal }) => {
  const restate = getRestateClient()
  const result = await restate.objectClient(claimSesson, req.sessionID).getContractsSummary(optsWithHeaders({ signal }))
  return { status: 200, body: result }
})
