import { HttpCallError } from '@restatedev/restate-sdk-clients'
import { asCon } from '../controller.js'
import { destroySession } from './session.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

/**
 * @swagger
 * /api/verification-questions:
 *   get:
 *     summary: Get verification questions for the claim
 *     description: Retrieves verification questions that need to be answered to complete the claim process
 *     tags:
 *       - Claims
 *     security:
 *       - sessionAuth: []
 *     responses:
 *       200:
 *         description: Verification questions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 questions:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: string
 *                         description: Question identifier
 *                       question:
 *                         type: string
 *                         description: The verification question text
 *                       options:
 *                         type: array
 *                         items:
 *                           type: string
 *                         description: Available answer options
 *       401:
 *         description: Unauthorized - session required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 401
 *       404:
 *         description: Questions not found or session expired
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 404
 *       409:
 *         description: Conflict - invalid session state
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 409
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 500
 */

/** @type {import('express').RequestHandler} */
export const getVerificationQuestions = asCon(async (req, { signal }) => {
  const restate = getRestateClient()
  try {
    const result = await restate.objectClient(claimSesson, req.sessionID).getVerificationQuestions(
      optsWithHeaders({ signal }),
    )
    return { status: 200, body: result }
  } catch (error) {
    if (error instanceof HttpCallError) {
      if (error.status === 404 || error.status === 409) {
        await destroySession(req)
      }
      return { status: error.status }
    }
    throw error
  }
})
