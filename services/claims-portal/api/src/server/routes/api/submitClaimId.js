import { HttpCallError } from '@restatedev/restate-sdk-clients'
import * as z from 'zod/v4'
import logger from '../../../logger.js'
import { asCon } from '../controller.js'
import { generateSession } from './session.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

const claimIdRegex = /^[a-z0-9]{10}$/

const claimIdSchema = z.object({
  claimId: z.string().min(1).max(10).toLowerCase(),
})

/** @type {import('express').RequestHandler} */
export const submitClaimId = asCon(async (req, { signal }) => {
  const parsedInput = claimIdSchema.safeParse(req.body)
  if (!parsedInput.success) {
    return { status: 400 }
  }

  const { claimId } = parsedInput.data

  if (!claimIdRegex.test(claimId)) {
    logger.info('Claim ID contains invalid characters')
    return { status: 404 }
  }

  try {
    await generateSession(req)
  } catch (error) {
    logger.error(error, 'Failed to generate session')
    return { status: 500 }
  }

  const restate = getRestateClient()
  try {
    await restate.objectClient(claimSesson, req.sessionID).startWithId(
      { outreachId: claimId },
      optsWithHeaders({ signal }),
    )
    return { status: 200 }
  } catch (error) {
    if (error instanceof HttpCallError) {
      return { status: error.status }
    }
    throw error
  }
})
