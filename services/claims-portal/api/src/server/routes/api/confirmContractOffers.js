import { asCon } from '../../controller.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

/** @type {import('express').RequestHandler} */
export const confirmContractOffers = asCon(async (req, { idempotencyKey, signal }) => {
  const restate = getRestateClient()
  const result = await restate.objectClient(claimSesson, req.sessionID).confirmOffers(
    optsWithHeaders({ idempotencyKey, signal }),
  )
  return { status: 200, body: result }
})
