import { asCon } from '../../controller.js'
import { serde, getRestateClient, optsWithHeaders, testData } from '../../../remoteServices/connectors/restate.js'

/** @type {import('express').RequestHandler} */
export const submitTestData = asCon(async (req, { idempotencyKey, signal }) => {
  if (!(req.body instanceof Buffer)) {
    return { status: 400 }
  }
  const restate = getRestateClient()
  const result = await restate.serviceClient(testData).overwriteFromCsv(
    new Uint8Array(req.body),
    optsWithHeaders({ idempotencyKey, signal, input: serde.binary }),
  )
  return { status: 200, body: { result } }
})
