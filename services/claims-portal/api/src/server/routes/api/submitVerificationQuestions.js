import { HttpCallError } from '@restatedev/restate-sdk-clients'
import * as z from 'zod/v4'
import { asCon } from '../controller.js'
import { destroySession } from './session.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

/**
 * @swagger
 * /api/verification-questions:
 *   post:
 *     summary: Submit answers to verification questions
 *     description: Submit answers to verification questions or skip the verification process
 *     tags:
 *       - Claims
 *     security:
 *       - sessionAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             oneOf:
 *               - type: object
 *                 properties:
 *                   skip:
 *                     type: boolean
 *                     enum: [true]
 *                     description: Skip verification questions
 *                 required:
 *                   - skip
 *                 example:
 *                   skip: true
 *               - type: object
 *                 properties:
 *                   answers:
 *                     type: object
 *                     description: Verification question answers (exactly 2 required)
 *                     additionalProperties:
 *                       oneOf:
 *                         - type: string
 *                           minLength: 1
 *                           maxLength: 100
 *                         - type: number
 *                     example:
 *                       question_1: "answer1"
 *                       question_2: 42
 *                 required:
 *                   - answers
 *     responses:
 *       200:
 *         description: Verification answers submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *                 result:
 *                   type: object
 *                   description: Verification result
 *       400:
 *         description: Invalid request body or answers format
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized - session required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 401
 *       404:
 *         description: Session not found or expired
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 404
 *       409:
 *         description: Conflict - invalid session state
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 409
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 500
 */

const answerIdSchema = z.string().min(1).max(50).regex(/^[a-z_]+$/)
const answerValueSchema = z.union([
  z.number(),
  z.string().min(1).max(100),
])

const verificationAnswersSchema = z.union([
  z.object({
    skip: z.literal(true),
  }),
  z.object({
    skip: z.undefined().optional(),
    answers: z.record(answerIdSchema, answerValueSchema).refine(
      record => Object.keys(record).length === 2,
      { error: 'Two verification answers are required' },
    ),
  }),
])

/** @type {import('express').RequestHandler} */
export const submitVerificationQuestions = asCon(async (req, { signal }) => {
  const parsedInput = verificationAnswersSchema.safeParse(req.body)
  if (!parsedInput.success) {
    return { status: 400 }
  }

  const restate = getRestateClient()
  try {
    const result = await restate.objectClient(claimSesson, req.sessionID).submitVerificationAnswers(
      parsedInput.data,
      optsWithHeaders({ signal }),
    )
    return { status: 200, body: result }
  } catch (error) {
    if (error instanceof HttpCallError) {
      if (error.status === 404 || error.status === 409) {
        await destroySession(req)
      }
      return { status: error.status }
    }
    throw error
  }
})
