import { HttpCallError } from '@restatedev/restate-sdk-clients'
import * as z from 'zod/v4'
import { asCon } from '../controller.js'
import { destroySession } from './session.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

const answerIdSchema = z.string().min(1).max(50).regex(/^[a-z_]+$/)
const answerValueSchema = z.union([
  z.number(),
  z.string().min(1).max(100),
])

const verificationAnswersSchema = z.union([
  z.object({
    skip: z.literal(true),
  }),
  z.object({
    skip: z.undefined().optional(),
    answers: z.record(answerIdSchema, answerValueSchema).refine(
      record => Object.keys(record).length === 2,
      { error: 'Two verification answers are required' },
    ),
  }),
])

/** @type {import('express').RequestHandler} */
export const submitVerificationQuestions = asCon(async (req, { signal }) => {
  const parsedInput = verificationAnswersSchema.safeParse(req.body)
  if (!parsedInput.success) {
    return { status: 400 }
  }

  const restate = getRestateClient()
  try {
    const result = await restate.objectClient(claimSesson, req.sessionID).submitVerificationAnswers(
      parsedInput.data,
      optsWithHeaders({ signal }),
    )
    return { status: 200, body: result }
  } catch (error) {
    if (error instanceof HttpCallError) {
      if (error.status === 404 || error.status === 409) {
        await destroySession(req)
      }
      return { status: error.status }
    }
    throw error
  }
})
