import { HttpCallError } from '@restatedev/restate-sdk-clients'
import * as z from 'zod/v4'
import { asCon } from '../controller.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

const additionalInfoSchema = z.object({
  postcode: z.string().min(1).max(10).toUpperCase()
    .overwrite(val => val.replaceAll(' ', ''))
    .regex(/^[A-Z0-9]*$/),
})

/** @type {import('express').RequestHandler} */
export const submitAdditionalInfo = asCon(async (req, { signal }) => {
  const parsedInput = additionalInfoSchema.safeParse(req.body)
  if (!parsedInput.success) {
    return { status: 400 }
  }

  const { postcode } = parsedInput.data

  if (postcode.length < 4) {
    return { status: 404 }
  }

  const restate = getRestateClient()
  try {
    await restate.objectClient(claimSesson, req.sessionID).processMatchAnswer(
      { postcode },
      optsWithHeaders({ signal }),
    )
    return { status: 200 }
  } catch (error) {
    if (error instanceof HttpCallError) {
      return { status: error.status }
    }
    throw error
  }
})
