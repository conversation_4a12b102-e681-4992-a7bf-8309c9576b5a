import { HttpCallError } from '@restatedev/restate-sdk-clients'
import * as z from 'zod/v4'
import { asCon } from '../controller.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

/**
 * @swagger
 * /api/additional-info:
 *   post:
 *     summary: Submit additional information for claim processing
 *     description: Submit additional information (such as postcode) required for claim verification
 *     tags:
 *       - Claims
 *     security:
 *       - sessionAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - postcode
 *             properties:
 *               postcode:
 *                 type: string
 *                 minLength: 4
 *                 maxLength: 10
 *                 pattern: '^[A-Z0-9]*$'
 *                 description: UK postcode (spaces will be removed, converted to uppercase)
 *                 example: "SW1A1AA"
 *     responses:
 *       200:
 *         description: Additional information submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request body or postcode format
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized - session required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 401
 *       404:
 *         description: Postcode too short or session not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 500
 */

const additionalInfoSchema = z.object({
  postcode: z.string().min(1).max(10).toUpperCase()
    .overwrite(val => val.replaceAll(' ', ''))
    .regex(/^[A-Z0-9]*$/),
})

/** @type {import('express').RequestHandler} */
export const submitAdditionalInfo = asCon(async (req, { signal }) => {
  const parsedInput = additionalInfoSchema.safeParse(req.body)
  if (!parsedInput.success) {
    return { status: 400 }
  }

  const { postcode } = parsedInput.data

  if (postcode.length < 4) {
    return { status: 404 }
  }

  const restate = getRestateClient()
  try {
    await restate.objectClient(claimSesson, req.sessionID).processMatchAnswer(
      { postcode },
      optsWithHeaders({ signal }),
    )
    return { status: 200 }
  } catch (error) {
    if (error instanceof HttpCallError) {
      return { status: error.status }
    }
    throw error
  }
})
