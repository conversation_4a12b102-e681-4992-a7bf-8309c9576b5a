import * as z from 'zod'
import { asCon } from '../../controller.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'
import { createAlphanumericSpaceSchema, createNumericSchema } from '../../schema.js'

const paymentDetailsSchema = z.union([
  z.object({
    useExisting: z.literal(true),
  }),
  z.object({
    useExisting: z.literal(false),
    payee: createAlphanumericSpaceSchema({ trim: true, maxLength: 70 }),
    accountNumber: createNumericSchema({ length: 8 }),
    sortCode: createNumericSchema({ length: 6 }),
  }),
])

/** @type {import('express').RequestHandler} */
export const submitPaymentDetails = asCon(async (req, { idempotencyKey, signal }) => {
  const parsedInput = paymentDetailsSchema.safeParse(req.body)
  if (!parsedInput.success) {
    return { status: 400 }
  }

  const restate = getRestateClient()
  const result = await restate.objectClient(claimSesson, req.sessionID).submitPaymentDetails(
    parsedInput.data,
    optsWithHeaders({ idempotencyKey, signal }),
  )
  return { status: 200, body: result }
})
