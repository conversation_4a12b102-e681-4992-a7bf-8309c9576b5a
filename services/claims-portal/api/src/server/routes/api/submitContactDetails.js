import * as z from 'zod'
import { asCon } from '../../controller.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'
import { createSimpleTextSchema, emailSchema, mobilePhoneSchema, postcodeSchema } from '../../schema.js'

const contactDetailsSchema = z.object({
  forename: createSimpleTextSchema({ trim: true, maxLength: 50 }),
  surname: createSimpleTextSchema({ trim: true, maxLength: 50 }),
  emailAddress: emailSchema,
  mobilePhoneNumber: mobilePhoneSchema,
  addressLineOne: createSimpleTextSchema({ trim: true, maxLength: 100 }),
  addressLineTwo: createSimpleTextSchema({ trim: true, minLength: 0, maxLength: 100 }).optional(),
  addressLineThree: createSimpleTextSchema({ trim: true, minLength: 0, maxLength: 100 }).optional(),
  addressPostcode: postcodeSchema,
})

/** @type {import('express').RequestHandler} */
export const submitContactDetails = asCon(async (req, { idempotencyKey, signal }) => {
  const parsedInput = contactDetailsSchema.safeParse(req.body)
  if (!parsedInput.success) {
    return { status: 400 }
  }

  const restate = getRestateClient()
  const result = await restate.objectClient(claimSesson, req.sessionID).submitContactDetails(
    parsedInput.data,
    optsWithHeaders({ idempotencyKey, signal }),
  )
  return { status: 200, body: result }
})
