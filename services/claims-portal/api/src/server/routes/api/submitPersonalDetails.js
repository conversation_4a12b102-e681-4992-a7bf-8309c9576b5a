import { HttpCallError } from '@restatedev/restate-sdk-clients'
import * as z from 'zod/v4'
import logger from '../../../logger.js'
import { asCon } from '../controller.js'
import { generateSession } from './session.js'
import { getRestateClient, optsWithHeaders, claimSesson } from '../../../remoteServices/connectors/restate.js'

/**
 * @swagger
 * /api/personal-details:
 *   post:
 *     summary: Submit personal details for claim verification
 *     description: Validates and submits personal details to verify the claim
 *     tags:
 *       - Claims
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - forename
 *               - surname
 *               - dob
 *             properties:
 *               forename:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *                 description: First name of the claimant
 *                 example: "John"
 *               surname:
 *                 type: string
 *                 minLength: 1
 *                 maxLength: 50
 *                 description: Last name of the claimant
 *                 example: "Smith"
 *               dob:
 *                 type: string
 *                 format: date
 *                 pattern: '^\d{4}-\d{2}-\d{2}$'
 *                 description: Date of birth in YYYY-MM-DD format
 *                 example: "1990-01-01"
 *     responses:
 *       200:
 *         description: Personal details successfully submitted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request body or format
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 400
 *       404:
 *         description: Personal details contain invalid characters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: integer
 *                   example: 500
 */

const nameRegex = /^[^!@£$%^&*()_+=[\]{}:;/\\?|~<>]+$/

const personalDetailsSchema = z.object({
  forename: z.string().max(50).trim().min(1),
  surname: z.string().max(50).trim().min(1),
  dob: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
})

/** @type {import('express').RequestHandler} */
export const submitPersonalDetails = asCon(async (req, { signal }) => {
  const parsedInput = personalDetailsSchema.safeParse(req.body)
  if (!parsedInput.success) {
    return { status: 400 }
  }

  if (!nameRegex.test(parsedInput.data.forename) || !nameRegex.test(parsedInput.data.surname)) {
    logger.info('Personal details contain invalid characters')
    return { status: 404 }
  }

  try {
    await generateSession(req)
  } catch (error) {
    logger.error(error, 'Failed to generate session')
    return { status: 500 }
  }

  const { forename, surname, dob } = parsedInput.data

  const restate = getRestateClient()
  try {
    await restate.objectClient(claimSesson, req.sessionID).startWithDetails(
      { forename, surname, dateOfBirth: dob },
      optsWithHeaders({ signal }),
    )
    return { status: 200 }
  } catch (error) {
    if (error instanceof HttpCallError) {
      return { status: error.status }
    }
    throw error
  }
})
