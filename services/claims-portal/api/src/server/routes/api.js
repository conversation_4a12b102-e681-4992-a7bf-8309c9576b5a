import { Router, json } from 'express'
import { noCache } from '../middleware/cache.js'
import { xhrSecurity } from '../middleware/helmet.js'
import session from '../middleware/session.js'
import notFound from './notFound.js'
import { submitClaimId } from './api/submitClaimId.js'
import { submitPersonalDetails } from './api/submitPersonalDetails.js'
import { getAdditionalInfoStatus } from './api/getAdditionalInfoStatus.js'
import { submitAdditionalInfo } from './api/submitAdditionalInfo.js'
import { getVerificationQuestions } from './api/getVerificationQuestions.js'
import { submitVerificationQuestions } from './api/submitVerificationQuestions.js'

const jsonParser = json()

const router = Router()

router.use(xhrSecurity, noCache, session)

router.post('/claim-id', jsonParser, submitClaimId)
router.post('/personal-details', jsonParser, submitPersonalDetails)
router.use((req, res, next) => {
  if (req.session.active !== true) {
    res.sendStatus(401)
  } else {
    next()
  }
})
router.get('/additional-info-status', getAdditionalInfoStatus)
router.post('/additional-info', jsonParser, submitAdditionalInfo)
router.get('/verification-questions', getVerificationQuestions)
router.post('/verification-questions', jsonParser, submitVerificationQuestions)

export default [
  router,
  notFound,
]
