import { Router, json, raw } from 'express'
import { noCache } from '../middleware/cache.js'
import { xhrSecurity } from '../middleware/helmet.js'
import session from '../middleware/session.js'
import notFound from './notFound.js'
import { submitClaimId } from './api/submitClaimId.js'
import { submitPersonalDetails } from './api/submitPersonalDetails.js'
import { getAdditionalInfoStatus } from './api/getAdditionalInfoStatus.js'
import { submitAdditionalInfo } from './api/submitAdditionalInfo.js'
import { getVerificationQuestions } from './api/getVerificationQuestions.js'
import { submitVerificationQuestions } from './api/submitVerificationQuestions.js'
import { getContactDetails } from './api/getContactDetails.js'
import { submitContactDetails } from './api/submitContactDetails.js'
import { confirmContactDetails } from './api/confirmContactDetails.js'
import { getContractsSummary } from './api/getContractsSummary.js'
import { getContractOffer } from './api/getContractOffer.js'
import { submitContractOfferDecision } from './api/submitContractOfferDecision.js'
import { confirmContractOffers } from './api/confirmContractOffers.js'
import { getPaymentDetails } from './api/getPaymentDetails.js'
import { submitPaymentDetails } from './api/submitPaymentDetails.js'
import { confirmPaymentDetails } from './api/confirmPaymentDetails.js'
import { getResult } from './api/getResult.js'
import { submitTestData } from './api/submitTestData.js'

const jsonParser = json()

const router = Router()

router.use(xhrSecurity, noCache, session)

router.post('/test-data', raw({ type: 'text/csv', limit: '1MB' }), submitTestData)

router.post('/claim-id', jsonParser, submitClaimId)
router.post('/personal-details', jsonParser, submitPersonalDetails)
router.use((req, res, next) => {
  if (req.session.active !== true) {
    res.sendStatus(401)
  } else {
    next()
  }
})
router.get('/additional-info-status', getAdditionalInfoStatus)
router.post('/additional-info', jsonParser, submitAdditionalInfo)
router.get('/verification-questions', getVerificationQuestions)
router.post('/verification-questions', jsonParser, submitVerificationQuestions)
router.get('/contact-details', getContactDetails)
router.post('/contact-details', jsonParser, submitContactDetails)
router.post('/confirm-contact-details', confirmContactDetails)
router.get('/loans-summary', getContractsSummary)
router.get('/loan-offer', getContractOffer)
router.post('/loan-offer', jsonParser, submitContractOfferDecision)
router.post('/confirm-loan-offers', confirmContractOffers)
router.get('/payment-details', jsonParser, getPaymentDetails)
router.post('/payment-details', jsonParser, submitPaymentDetails)
router.post('/confirm-payment-details', confirmPaymentDetails)
router.get('/result', getResult)

export default [
  router,
  notFound,
]
