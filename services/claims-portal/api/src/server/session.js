import logger from '../logger.js'

/**
 * @param {import('express').Request} req
 */
export async function generateSession(req) {
  return new Promise((resolve, reject) => {
    req.session.regenerate((error) => {
      if (error) {
        reject(error)
      } else {
        req.session.active = true
        resolve()
      }
    })
  })
}

/**
 * @param {import('express').Request} req
 */
export async function destroySession(req) {
  return new Promise((resolve) => {
    req.session.destroy((error) => {
      if (error) {
        if (error instanceof Error) {
          logger.error(error, 'Failed to destroy session, but continuing')
        } else {
          logger.warn('Failed to destroy session, but continuing')
        }
      }
      resolve()
    })
  })
}
