import cluster from 'cluster'
import os from 'os'
import { fromEnv } from './constants.js'
import logger from './logger.js'

const run = async () => {
  process.on('uncaughtExceptionMonitor', (error, origin) => {
    logger.error(error, 'Uncaught exception monitored', { origin })
  })
  await import('./server/server.js')
}

if (cluster.isPrimary) {
  const useSingleThread = fromEnv('NODE_SINGLE_THREAD') === 'true'
  if (useSingleThread) {
    await run()
  } else {
    let parallelism = os.availableParallelism()

    const reserveCpus = +fromEnv('RESERVE_CPUS')
    if (Number.isSafeInteger(reserveCpus) && reserveCpus > 0) {
      parallelism = Math.max(1, parallelism - reserveCpus)
    }

    cluster.on('fork', (worker) => {
      worker.once('exit', (exitCode, signal) => {
        if (signal) {
          logger.info('Worker was killed by signal', {
            signal,
            pid: worker.process.pid,
          })
        } else {
          logger.warn('Worker exited unexpectedly, creating new worker', {
            exitCode,
            pid: worker.process.pid,
          })
          cluster.fork()
        }
      })
    })

    logger.info('Creating cluster of process forks', { count: parallelism })
    for (let i = 0; i < parallelism; i += 1) {
      cluster.fork()
    }

    process.on('SIGTERM', () => {
      Object.values(cluster.workers).forEach((worker) => {
        worker.process.kill()
      })
    })
  }
} else {
  await run()
}
