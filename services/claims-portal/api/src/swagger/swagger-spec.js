import { paths } from './paths.js'

export const swaggerSpec = {
  openapi: '3.0.0',
  info: {
    title: 'Claims Portal API',
    version: '1.0.0',
    description: 'API documentation for the Claims Portal application. This API handles the claims verification process including claim ID submission, personal details verification, additional information collection, and verification questions.',
    contact: {
      name: 'API Support',
      email: '<EMAIL>'
    }
  },
  servers: [
    {
      url: '/api',
      description: 'API base path'
    }
  ],
  components: {
    securitySchemes: {
      sessionAuth: {
        type: 'apiKey',
        in: 'cookie',
        name: 'connect.sid',
        description: 'Session-based authentication using Express sessions'
      }
    },
    schemas: {
      Error: {
        type: 'object',
        properties: {
          status: {
            type: 'integer',
            description: 'HTTP status code'
          },
          message: {
            type: 'string',
            description: 'Error message'
          }
        }
      },
      ClaimIdRequest: {
        type: 'object',
        required: ['claimId'],
        properties: {
          claimId: {
            type: 'string',
            minLength: 1,
            maxLength: 10,
            pattern: '^[a-z0-9]{10}$',
            description: '10-character alphanumeric claim identifier (lowercase)',
            example: 'abc1234567'
          }
        }
      },
      PersonalDetailsRequest: {
        type: 'object',
        required: ['forename', 'surname', 'dob'],
        properties: {
          forename: {
            type: 'string',
            minLength: 1,
            maxLength: 50,
            description: 'First name of the claimant',
            example: 'John'
          },
          surname: {
            type: 'string',
            minLength: 1,
            maxLength: 50,
            description: 'Last name of the claimant',
            example: 'Smith'
          },
          dob: {
            type: 'string',
            format: 'date',
            pattern: '^\\d{4}-\\d{2}-\\d{2}$',
            description: 'Date of birth in YYYY-MM-DD format',
            example: '1990-01-01'
          }
        }
      },
      AdditionalInfoRequest: {
        type: 'object',
        required: ['postcode'],
        properties: {
          postcode: {
            type: 'string',
            minLength: 4,
            maxLength: 10,
            pattern: '^[A-Z0-9]*$',
            description: 'UK postcode (spaces will be removed, converted to uppercase)',
            example: 'SW1A1AA'
          }
        }
      },
      VerificationAnswersRequest: {
        oneOf: [
          {
            type: 'object',
            properties: {
              skip: {
                type: 'boolean',
                enum: [true],
                description: 'Skip verification questions'
              }
            },
            required: ['skip'],
            example: { skip: true }
          },
          {
            type: 'object',
            properties: {
              answers: {
                type: 'object',
                description: 'Verification question answers (exactly 2 required)',
                additionalProperties: {
                  oneOf: [
                    { type: 'string', minLength: 1, maxLength: 100 },
                    { type: 'number' }
                  ]
                },
                example: {
                  question_1: 'answer1',
                  question_2: 42
                }
              }
            },
            required: ['answers']
          }
        ]
      },
      SuccessResponse: {
        type: 'object',
        properties: {
          status: {
            type: 'integer',
            example: 200
          }
        }
      },
      VerificationQuestions: {
        type: 'object',
        properties: {
          questions: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                id: {
                  type: 'string',
                  description: 'Question identifier'
                },
                question: {
                  type: 'string',
                  description: 'The verification question text'
                },
                options: {
                  type: 'array',
                  items: { type: 'string' },
                  description: 'Available answer options'
                }
              }
            }
          }
        }
      },
      AdditionalInfoStatus: {
        type: 'object',
        properties: {
          status: {
            type: 'string',
            description: 'Current status of additional information',
            example: 'required'
          },
          required_fields: {
            type: 'array',
            items: { type: 'string' },
            description: 'List of required additional information fields',
            example: ['postcode']
          }
        }
      }
    },
    responses: {
      BadRequest: {
        description: 'Invalid request body or format',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      },
      Unauthorized: {
        description: 'Unauthorized - session required',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      },
      NotFound: {
        description: 'Resource not found',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      },
      Conflict: {
        description: 'Conflict - invalid session state',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      },
      InternalServerError: {
        description: 'Internal server error',
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/Error' }
          }
        }
      }
    }
  },
  tags: [
    {
      name: 'Claims',
      description: 'Claims processing and verification endpoints'
    },
    {
      name: 'Health',
      description: 'Health check endpoints'
    }
  ],
  paths
}
