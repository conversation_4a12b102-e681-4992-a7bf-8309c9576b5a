export const paths = {
  '/claim-id': {
    post: {
      summary: 'Submit claim ID to start the claims process',
      description: 'Validates and submits a claim ID to initiate the claims verification process',
      tags: ['Claims'],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/ClaimIdRequest' }
          }
        }
      },
      responses: {
        200: {
          description: 'Claim ID successfully submitted and session started',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SuccessResponse' }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        404: { $ref: '#/components/responses/NotFound' },
        500: { $ref: '#/components/responses/InternalServerError' }
      }
    }
  },
  '/personal-details': {
    post: {
      summary: 'Submit personal details for claim verification',
      description: 'Validates and submits personal details to verify the claim',
      tags: ['Claims'],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/PersonalDetailsRequest' }
          }
        }
      },
      responses: {
        200: {
          description: 'Personal details successfully submitted',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SuccessResponse' }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        404: { $ref: '#/components/responses/NotFound' },
        500: { $ref: '#/components/responses/InternalServerError' }
      }
    }
  },
  '/additional-info-status': {
    get: {
      summary: 'Get additional information status',
      description: 'Retrieves the current status of additional information requirements for the claim',
      tags: ['Claims'],
      security: [{ sessionAuth: [] }],
      responses: {
        200: {
          description: 'Additional info status retrieved successfully',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/AdditionalInfoStatus' }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' },
        404: { $ref: '#/components/responses/NotFound' },
        409: { $ref: '#/components/responses/Conflict' },
        500: { $ref: '#/components/responses/InternalServerError' }
      }
    }
  },
  '/additional-info': {
    post: {
      summary: 'Submit additional information for claim processing',
      description: 'Submit additional information (such as postcode) required for claim verification',
      tags: ['Claims'],
      security: [{ sessionAuth: [] }],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/AdditionalInfoRequest' }
          }
        }
      },
      responses: {
        200: {
          description: 'Additional information submitted successfully',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/SuccessResponse' }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' },
        404: { $ref: '#/components/responses/NotFound' },
        500: { $ref: '#/components/responses/InternalServerError' }
      }
    }
  },
  '/verification-questions': {
    get: {
      summary: 'Get verification questions for the claim',
      description: 'Retrieves verification questions that need to be answered to complete the claim process',
      tags: ['Claims'],
      security: [{ sessionAuth: [] }],
      responses: {
        200: {
          description: 'Verification questions retrieved successfully',
          content: {
            'application/json': {
              schema: { $ref: '#/components/schemas/VerificationQuestions' }
            }
          }
        },
        401: { $ref: '#/components/responses/Unauthorized' },
        404: { $ref: '#/components/responses/NotFound' },
        409: { $ref: '#/components/responses/Conflict' },
        500: { $ref: '#/components/responses/InternalServerError' }
      }
    },
    post: {
      summary: 'Submit answers to verification questions',
      description: 'Submit answers to verification questions or skip the verification process',
      tags: ['Claims'],
      security: [{ sessionAuth: [] }],
      requestBody: {
        required: true,
        content: {
          'application/json': {
            schema: { $ref: '#/components/schemas/VerificationAnswersRequest' }
          }
        }
      },
      responses: {
        200: {
          description: 'Verification answers submitted successfully',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  status: { type: 'integer', example: 200 },
                  result: {
                    type: 'object',
                    description: 'Verification result'
                  }
                }
              }
            }
          }
        },
        400: { $ref: '#/components/responses/BadRequest' },
        401: { $ref: '#/components/responses/Unauthorized' },
        404: { $ref: '#/components/responses/NotFound' },
        409: { $ref: '#/components/responses/Conflict' },
        500: { $ref: '#/components/responses/InternalServerError' }
      }
    }
  },
  '/liveness': {
    get: {
      summary: 'Liveness check',
      description: 'Health check endpoint to verify the service is running',
      tags: ['Health'],
      responses: {
        200: {
          description: 'Service is alive',
          content: {
            'text/plain': {
              schema: {
                type: 'string',
                example: 'OK'
              }
            }
          }
        }
      }
    }
  },
  '/readiness': {
    get: {
      summary: 'Readiness check',
      description: 'Health check endpoint to verify the service is ready to handle requests',
      tags: ['Health'],
      responses: {
        200: {
          description: 'Service is ready',
          content: {
            'text/plain': {
              schema: {
                type: 'string',
                example: 'OK'
              }
            }
          }
        }
      }
    }
  }
}
