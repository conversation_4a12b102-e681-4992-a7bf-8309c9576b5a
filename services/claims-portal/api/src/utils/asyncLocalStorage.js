import { AsyncLocalStorage } from 'async_hooks'

/**
 * @typedef Store
 * @property {Map<string, any>} app
 */

/** @type {AsyncLocalStorage<Store>} */
export const als = new AsyncLocalStorage()

/**
 * Bind a callback to the current store
 * @param {Function} fn The callback function
 * @returns {Function} The callback curried with bindings to the current store
 */
export const bind = (fn) => {
  const store = als.getStore()
  return (...args) => als.run(store, fn, ...args)
}
