import logger from '../logger.js'

class HealthcheckError {
  constructor(message, error) {
    this.message = message
    this.error = error
  }
}

/**
 * @param {() => Promise<void>} fn
 * @param {String} message
 */
export const wrapHealthcheckError = (fn, message) => async () => {
  try {
    await fn()
  } catch (error) {
    throw new HealthcheckError(message, error)
  }
}

/**
 * @param {...Promise<void>} promises
 */
export const assertHealthchecks = async (...promises) => {
  const results = await Promise.allSettled(promises)
  let hasError = false
  for (let i = 0, n = results.length; i < n; i += 1) {
    if (results[i].status === 'rejected') {
      hasError = true
      const error = results[i].reason
      if (error instanceof HealthcheckError) {
        logger.error(error.error, error.message)
      } else {
        logger.error(error, 'Unwrapped healthcheck error')
      }
    }
  }
  if (hasError) throw new Error('Failed healthcheck(s)')
}
