import Redis from 'ioredis'
import logger from '../../logger.js'
import { fromEnv } from '../../constants.js'
import { wrapHealthcheckError } from '../../utils/healthcheckError.js'

const redisUrl = new URL(fromEnv('REDIS_HOSTNAME'))

/** @type {Redis} */
let client = null

export const getRedisClient = () => {
  if (client === null) {
    client = new Redis(redisUrl.href, { maxRetriesPerRequest: 5 })

    client.on('connect', () => {
      logger.info('Connected to redis', { redisSource: redisUrl.hostname })
    })

    client.on('error', (error) => {
      logger.error(error, 'Redis client error caught', { redisSource: redisUrl.hostname })
    })
  }

  return client
}

/**
 * @template T
 * @param {import('ioredis').RedisOptions} options
 * @param {(client: Redis) => Promise<T>} callback
 */
export const createTempRedisClient = (options, callback) => {
  const tempClient = new Redis(redisUrl.href, options)
  return callback(tempClient).finally(() => {
    tempClient.disconnect()
  })
}

export const healthcheck = wrapHealthcheckError(
  () => createTempRedisClient({ maxRetriesPerRequest: 1 }, pingClient => (
    new Promise((resolve, reject) => {
      pingClient.once('error', reject)
      pingClient.once('connect', () => (
        pingClient.ping(error => (error ? reject(error) : resolve()))
      ))
    })
  )),
  'Redis healthcheck error',
)
