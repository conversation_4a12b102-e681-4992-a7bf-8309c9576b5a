import * as restate from '@restatedev/restate-sdk-clients'
import { fromEnv } from '../../constants.js'
import { als } from '../../utils/asyncLocalStorage.js'

export const { serde } = restate

/** @type {restate.Ingress} */
let client = null

export const getRestateClient = () => {
  if (client === null) {
    client = restate.connect({ url: fromEnv('RESTATE_INGRESS_URL') })
  }

  return client
}

/**
 * @returns {Record<string, string> | undefined}
 */
function getHeaders() {
  const store = als.getStore()
  if (store === undefined) return undefined
  return store.app.get('apiHeaders')
}

/**
 * @template I, O
 * @param {restate.IngressCallOptions<I, O>} [opts]
 */
export function optsWithHeaders(opts) {
  const headers = getHeaders()
  if (headers === undefined) {
    return restate.Opts.from(opts === undefined ? {} : opts)
  }

  return restate.Opts.from({
    ...opts,
    headers: {
      ...opts?.headers,
      ...headers,
    },
  })
}

/**
 * @template I
 * @param {restate.IngressSendOptions<I>} [opts]
 */
export function sendOptsWithHeaders(opts) {
  const headers = getHeaders()
  if (headers === undefined) {
    return restate.SendOpts.from(opts === undefined ? {} : opts)
  }

  return restate.SendOpts.from({
    ...opts,
    headers: {
      ...opts?.headers,
      ...headers,
    },
  })
}

/** @type {import('../../../../../../lambdas/restate-runner/src/restate/claimSession.ts').ClaimSession} */
export const claimSesson = { name: 'claim-session' }

/** @type {import('../../../../../../lambdas/restate-runner/src/testing/service.ts').TestData} */
export const testData = { name: 'test-data' }
