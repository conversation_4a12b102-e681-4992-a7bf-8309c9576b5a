import crypto from "crypto";
import {
  GetSecretValueCommand,
  CreateSecretCommand,
} from "@aws-sdk/client-secrets-manager";
import { Phunk } from "phunky";
import { fromEnv, COOKIE_SECRETS_TTL } from "../constants.js";
import logger from "../logger.js";
import { getClient as getSecretsManager } from "./connectors/secretsManager.js";

const secretsPhunk = new Phunk(
  async () => {
    const secretsManager = getSecretsManager();
    const cookieSecretId = fromEnv("SECRET_ID_COOKIE_SECRET");

    const getCurrentCommand = new GetSecretValueCommand({
      SecretId: cookieSecretId,
    });
    const getPreviousCommand = new GetSecretValueCommand({
      SecretId: cookieSecretId,
      VersionStage: "AWSPREVIOUS",
    });

    logger.debug("Getting cookie secrets from SecretsManager");
    let currentSecret;
    try {
      currentSecret = (await secretsManager.send(getCurrentCommand))
        .SecretString;
    } catch (error) {
      if (fromEnv("LOCALHOST_MODE") === "true") {
        // This should never be enabled in a deployed environment, just for local development due to localstack forgetfulness
        logger.warn("Creating custom secret as no secret found in localstack");
        currentSecret = crypto.randomUUID();
        const createSecretCommand = new CreateSecretCommand({
          Name: cookieSecretId,
          SecretString: currentSecret,
        });
        await secretsManager.send(createSecretCommand);
        return [currentSecret];
      }
      throw error;
    }
    logger.debug("Found current cookie secret value");
    try {
      const { SecretString: previousSecret } = await secretsManager.send(
        getPreviousCommand
      );
      return [currentSecret, previousSecret];
    } catch {
      logger.debug("No previous cookie secret value available");
      return [currentSecret];
    }
  },
  { ttl: COOKIE_SECRETS_TTL, cacheRejections: true }
);

export const getCookieSecrets = async () => secretsPhunk.current();
