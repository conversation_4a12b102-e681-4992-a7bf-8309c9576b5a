/// <reference types="node" />

export const APP_NAME = 'Claims Portal'

/** @type {NodeJS.ProcessEnv} */
const env = Object.assign(Object.create(null), process.env)

/**
 * @param {string} name
 */
export const fromEnv = name => env[name]

export const COOKIE_SECRETS_TTL = 1 * 60 * 60 * 1000 // 1 hour

export const SESSION_IDLE_TIMEOUT = 15 * 60 * 1000 // 15 minutes
export const SESSION_MAX_TIMEOUT = 2 * 60 * 60 * 1000 // 2 hours
export const SESSION_MIDDLEWARE_TTL = 6 * 60 * 60 * 1000 // 6 hours
