{"name": "claims-portal-api", "version": "1.0.0", "main": "main/app.js", "type": "module", "private": true, "engines": {"node": "22"}, "scripts": {"lint": "eslint src", "fix-lint": "eslint --fix src", "test": "echo \"No tests\"", "start": "nodemon --exec \"env-cmd -e debug node src/app.js\""}, "nodemonConfig": {"watch": ["src", ".env-cmdrc.json"], "ignore": ["src/**/*.test.js"]}, "dependencies": {"@aws-sdk/client-secrets-manager": "3.826.0", "@kpmg-uk/logger": "2.2.1", "@restatedev/restate-sdk-clients": "1.6.1", "connect-redis": "8.1.0", "express": "5.1.0", "express-session": "1.18.1", "express-static-gzip": "3.0.0", "helmet": "4.1.1", "ioredis": "5.6.1", "morgan": "1.10.0", "phunky": "2.0.0", "request-ip": "3.3.0", "zod": "3.25.67"}, "devDependencies": {"@kpmg-uk/eslint-config-cdd-js": "2.0.1", "@types/node": "22.15.31", "env-cmd": "10.1.0"}}