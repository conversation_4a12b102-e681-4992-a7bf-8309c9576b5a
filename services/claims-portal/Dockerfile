ARG base_image=node:22.16.0-alpine3.21

FROM ${base_image} AS webapp-build
WORKDIR /home/<USER>/webapp
COPY ./webapp/.npmrc ./webapp/package*.json ./
RUN --mount=type=secret,id=npm-token,uid=1000 \
    echo "//npm.pkg.github.com/:_authToken=$(cat /run/secrets/npm-token)" > ~/.npmrc && \
    DISABLE_OPENCOLLECTIVE=true npm ci --omit=dev && \
    rm ~/.npmrc
COPY ./webapp ./
RUN npm run build

FROM ${base_image} AS api-dependencies
WORKDIR /home/<USER>/api
COPY ./api/.npmrc ./api/package*.json ./
RUN --mount=type=secret,id=npm-token,uid=1000 \
    echo "//npm.pkg.github.com/:_authToken=$(cat /run/secrets/npm-token)" > ~/.npmrc && \
    DISABLE_OPENCOLLECTIVE=true npm ci --omit=dev && \
    rm ~/.npmrc

FROM ${base_image} AS release
STOPSIGNAL SIGTERM
HEALTHCHECK CMD wget -nv -t1 --spider 'http://127.0.0.1/liveness' || exit 1
CMD ["node", "api"]
ENV NODE_ENV="production"
USER node
WORKDIR /home/<USER>/app
COPY --chown=node:node ./api/package.json ./api/
COPY --chown=node:node --from=api-dependencies /home/<USER>/api/node_modules ./api/node_modules
COPY --chown=node:node --from=webapp-build /home/<USER>/webapp/dist ./webapp/dist
COPY --chown=node:node ./api/src ./api/main
