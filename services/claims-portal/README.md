# ODD Outreach Portal

## Environment Variables

| **Name** | **Required** | **Default value** | **Notes** |
| --- | --- | --- | --- |
| NODE_ENV | NO | | One of ["development", "production", "test"] |
| NODE_SINGLE_THREAD | NO | | |
| LOGGING_LEVEL | NO | info | One of ["debug", "verbose", "info", "warn", "error"] |
| AWS_XRAY_LOG_LEVEL | NO | | |
| LOCALHOST_MODE | NO | | |
| OIDC_CLIENT_ID | YES | | |
| OIDC_CLIENT_SECRET | YES | | |
| OIDC_CONFIGURATION_URL | NO | | |
| OIDC_ISSUER | NO | | Required if OIDC_CONFIGURATION_URL is not provided |
| OIDC_AUTHORIZATION_ENDPOINT | NO | | Required if OIDC_CONFIGURATION_URL is not provided |
| OIDC_TOKEN_ENDPOINT | NO | | Required if OIDC_CONFIGURATION_URL is not provided |
| OIDC_USERINFO_ENDPOINT | NO | | Required if OIDC_CONFIGURATION_URL is not provided |
| BRAND_URLS | YES | | Brand name followed by host, brands separated by pipe e.g. "aaa sub.aaa.co.uk \| bbb sub.bbb.co.uk" Do not include "https://" |
| PG_HOST | YES | | |
| PG_PORT | YES | | |
| PG_PASSWORD | YES | | |
| REDIS_HOSTNAME | YES | redis://localhost:6379 | |
| CORE_SERVICES_API_KEY | YES | | Is a string. To validate if the request is coming from a trusted source |
| AUTH_SERVICE_PRIVATE_HOSTNAME | YES | | Internal |
| AUTH_SERVICE_PUBLIC_HOSTNAME | YES | | Internal |
| MOCK_AUTH_SERVICE_PRIVATE_HOSTNAME | NO | | Internal |
| COMMS_HONCHO_HOSTNAME | YES | | Internal |
| DOCUMENT_VAULT_HOSTNAME | YES | | Internal |
| CASE_DATA_VAULT_HOSTNAME | YES | | Internal |
| TOFU_HOSTNAME | YES | | Internal |
| EVALUATION_ENGINE_HOSTNAME | YES | | Internal |
| HOOYU_SERVICE_HOSTNAME | YES | | Internal |
| DOCUSIGN_HOSTNAME | YES | | Internal |
| AWS_ACCESS_KEY_ID | YES | | AWS credential for SNS client |
| AWS_SECRET_ACCESS_KEY | NO | | AWS credential for SNS client | 
| AWS_REGION | NO | | AWS region for SNS client |
| AWS_ENDPOINT | NO | | |
| LOGIN_SNS_TOPIC_ARN | YES | | ARN of SNS topic to publish events to |
| SECRET_ID_COOKIE_SECRET | YES | | |
| AAV_UPDATER_GATEWAY | YES | | Internal |
| AUTH_API_HOSTNAME | YES | | Internal |
| MELISSA_ADDRESS_LOOKUP_HOSTNAME | YES | | Internal |
| PLAUSIBLE_SCRIPT_URL | YES | | |
| TASK_DATA_VAULT_HOSTNAME | YES | | Internal |
