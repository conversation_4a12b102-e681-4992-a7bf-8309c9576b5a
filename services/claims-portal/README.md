# ODD Outreach Portal

## Environment Variables

| **Name** | **Required** | **Default value** | **Notes** |
| --- | --- | --- | --- |
| NODE_ENV | NO | | One of ["development", "production", "test"] |
| NODE_SINGLE_THREAD | NO | | |
| LOGGING_LEVEL | NO | info | One of ["debug", "verbose", "info", "warn", "error"] |
| AWS_XRAY_LOG_LEVEL | NO | | |
| LOCALHOST_MODE | NO | | |
| OIDC_CLIENT_ID | YES | | |
| OIDC_CLIENT_SECRET | YES | | |
| OIDC_CONFIGURATION_URL | NO | | |
| OIDC_ISSUER | NO | | Required if OIDC_CONFIGURATION_URL is not provided |
| OIDC_AUTHORIZATION_ENDPOINT | NO | | Required if OIDC_CONFIGURATION_URL is not provided |
| OIDC_TOKEN_ENDPOINT | NO | | Required if OIDC_CONFIGURATION_URL is not provided |
| OIDC_USERINFO_ENDPOINT | NO | | Required if OIDC_CONFIGURATION_URL is not provided |
| BRAND_URLS | YES | | Brand name followed by host, brands separated by pipe e.g. "aaa sub.aaa.co.uk \| bbb sub.bbb.co.uk" Do not include "https://" |
| PG_HOST | YES | | |
| PG_PORT | YES | | |
| PG_PASSWORD | YES | | |
| REDIS_HOSTNAME | YES | redis://localhost:6379 | |
| CORE_SERVICES_API_KEY | YES | | Is a string. To validate if the request is coming from a trusted source |
| AUTH_SERVICE_PRIVATE_HOSTNAME | YES | | Internal |
| AUTH_SERVICE_PUBLIC_HOSTNAME | YES | | Internal |
| MOCK_AUTH_SERVICE_PRIVATE_HOSTNAME | NO | | Internal |
| COMMS_HONCHO_HOSTNAME | YES | | Internal |
| DOCUMENT_VAULT_HOSTNAME | YES | | Internal |
| CASE_DATA_VAULT_HOSTNAME | YES | | Internal |
| TOFU_HOSTNAME | YES | | Internal |
| EVALUATION_ENGINE_HOSTNAME | YES | | Internal |
| HOOYU_SERVICE_HOSTNAME | YES | | Internal |
| DOCUSIGN_HOSTNAME | YES | | Internal |
| AWS_ACCESS_KEY_ID | YES | | AWS credential for SNS client |
| AWS_SECRET_ACCESS_KEY | NO | | AWS credential for SNS client | 
| AWS_REGION | NO | | AWS region for SNS client |
| AWS_ENDPOINT | NO | | |
| LOGIN_SNS_TOPIC_ARN | YES | | ARN of SNS topic to publish events to |
| SECRET_ID_COOKIE_SECRET | YES | | |
| AAV_UPDATER_GATEWAY | YES | | Internal |
| AUTH_API_HOSTNAME | YES | | Internal |
| MELISSA_ADDRESS_LOOKUP_HOSTNAME | YES | | Internal |
| PLAUSIBLE_SCRIPT_URL | YES | | |
| TASK_DATA_VAULT_HOSTNAME | YES | | Internal |


## Code Setup

### Tooling pre-requisities

**NodeJS**

We use NVM for Node versions, if you're not using any Node version management tool, please install this https://github.com/nvm-sh/nvm

Once NVM is installed and you have updated your `.zshrc` as the instructions show, then install Node 22
```sh
nvm install 22
# in future just switch to this version with:
nvm use 22
```
Or if you're using another versioning tool, make sure you switch to v18 for this work

### NPM access token for GitHub registry

Our NodeJS services make use of @kpmg-uk scoped NPM packages, and an access token is required to install these

1. On GitHub, generate a new Personal Access Token (PAT) (see online guides if necessary) with the `repo` and `read:packages` permissions
2. In your `~/.zshrc` add the following line `export GH_NPM_TOKEN="PASTE_PAT_HERE"` (replacing the value in quotes with the token on your clipboard)
3. On GitHub, make sure you "Configure SSO" for the KPMG-UK org for the new PAT
4. In your `~/.npmrc` add the following lines:
    ```npmrc
    //npm.pkg.github.com/:_authToken=${GH_NPM_TOKEN}
    @kpmg-uk:registry=https://npm.pkg.github.com
    ```
5. Your terminal will not automatically pick up the changes to `~/.zshrc` so run `source ~/.zshrc` to refresh your terminal with the token value (this applies to any open terminal that needs to run SANCDD commands)

Now you are set up to install @kpmg-uk scoped NPM packages

6. Install nodemon if not already installed
```sh
npm i -g nodemon
```
7. Navigate to the .local folder and recognise the ```up.sh``` file as a script
```sh
chmod +x up.sh
```
8. You need to have docker installed on your machine for this step. After you have installed docker, you can run the script to initialise the local environment.

Run the script
```sh
./up.sh
```
This creates a docker container and sets up a local development environment for the project. You can verify this by running the following command:
```sh
docker ps
```
This should show the containers running.

9. Navigate to the services/api directory and install the dependencies
```sh
npm ci
```
10. Run the API
```sh
npm run start
```
11. Navigate to the services/api directory and install the dependencies
```sh
npm ci
```
12. Run the API
```sh
npm run start
```
13. You can now access the API at http://localhost:3000 and UI at http://localhost:4000
14. Navigate back to the root folder and switch to lambda directory
```sh
cd lambda
```
15. Install dependencies and run the lambda
```sh
npm ci
npm run deploy
```
