{"name": "restate-runner", "version": "0.0.0", "private": true, "type": "module", "main": "src/index.js", "engines": {"node": "22"}, "bundle": true, "scripts": {"lint": "tsc --noEmit && eslint", "test": "vitest run", "bundle": "rm -rf bundle && esbuild ./src/main.ts --bundle --sourcemap --sources-content=false --platform=node --target=node22.16.0 --format=esm --main-fields=module,main --external:pg-native --outfile=bundle/main.mjs --banner:js='import { createRequire } from \"module\";const require = createRequire(import.meta.url);' && cp -r ./src/services/bankModulus/*.txt bundle", "deploy": "sh ../../.local/localstack/deploy-restate-lambda.sh restate-runner"}, "dependencies": {"@aws-sdk/client-secrets-manager": "3.821.0", "@aws-sdk/credential-providers": "3.821.0", "@aws-sdk/rds-signer": "3.821.0", "@kpmg-uk/http-client": "0.2.0", "@kpmg-uk/logger": "2.2.1", "@restatedev/restate-sdk": "1.8.0", "csv-parse": "6.1.0", "pg": "8.16.0", "phunky": "2.0.0", "zod": "4.0.14"}, "devDependencies": {"@kpmg-uk/eslint-config-cdd-ts": "1.0.0", "@types/aws-lambda": "8.10.149", "@types/node": "22.15.29", "@types/pg": "8.15.4", "esbuild": "0.25.1", "typescript": "5.8.2", "vitest": "3.2.0"}}