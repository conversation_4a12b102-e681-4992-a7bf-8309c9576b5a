import type { Duration } from '@restatedev/restate-sdk'
import { createEndpointHandler } from '@restatedev/restate-sdk/lambda'
import { fromEnv } from './constants.ts'
import logger, { restateLogger } from './logger.ts'
import { claimSession } from './restate/claimSession.ts'
import { claimSessionAudit } from './restate/claimSessionAudit.ts'
import { claimSessionContracts } from './restate/claimSessionContracts.ts'
import { claimSessionVerification } from './restate/claimSessionVerification.ts'
import { customerLock } from './restate/customerLock.ts'
import { customerLockList } from './restate/customerLockList.ts'
import { secretRotation } from './restate/secretRotation.ts'
import { testData } from './testing/service.ts'

const identityKeys: string[] = (() => {
  const identityPublicKey = fromEnv('RESTATE_IDENTITY_PUBLIC_KEY')
  if (typeof identityPublicKey === 'string') {
    return [identityPublicKey]
  }
  return []
})()

const journalRetention: Duration | undefined = (() => {
  const retention = fromEnv('RESTATE_JOURNAL_RETENTION')
  if (!retention || retention === '0') { return undefined }
  const hours = +retention
  if (Number.isInteger(hours) && hours > 0) {
    return { hours }
  }
  logger.warn('Invalid RESTATE_JOURNAL_RETENTION value', { retention })
  return undefined
})()

export const handler = createEndpointHandler({
  defaultServiceOptions: {
    idempotencyRetention: { hours: 1 },
    journalRetention,
  },
  identityKeys,
  logger: restateLogger,
  services: [
    claimSession,
    claimSessionAudit,
    claimSessionContracts,
    claimSessionVerification,
    customerLock,
    customerLockList,
    secretRotation,
    testData,
  ],
})
