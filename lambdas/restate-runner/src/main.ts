import { endpoint as restateEndpoint } from '@restatedev/restate-sdk/lambda'
import { fromEnv } from './constants.ts'
import { restateLogger } from './logger.ts'
import { claimSession } from './restate/claimSession.ts'
import { claimSessionAudit } from './restate/claimSessionAudit.ts'
import { secretRotation } from './restate/secretRotation.ts'

const endpoint = restateEndpoint()

const identityPublicKey = fromEnv('RESTATE_IDENTITY_PUBLIC_KEY')
if (typeof identityPublicKey === 'string') {
  endpoint.withIdentityV1(identityPublicKey)
}

endpoint
  .setLogger(restateLogger)
  .bind(claimSession)
  .bind(claimSessionAudit)
  .bind(secretRotation)

export const handler = endpoint.handler()
