import pg from 'pg'
import type { Pool, PoolClient, PoolConfig } from 'pg'

type Tx<TQueries> = {
  commit: () => Promise<void>
  rollback: () => Promise<void>
  queries: TQueries
}

export type CreateQueriesFn<TQueries> = (client: Pool | PoolClient) => TQueries

export const createPgClient = <TQueries>(config: PoolConfig, createQueries: CreateQueriesFn<TQueries>) => {
  const pool = new pg.Pool(config)

  let queries: TQueries

  return {
    pool,
    get queries() {
      return (queries ??= createQueries(pool))
    },
    async useTransaction<TResult>(consumer: (tx: Tx<TQueries>) => Promise<TResult>) {
      const client = await pool.connect()
      let complete = false

      const tx: Tx<TQueries> = {
        commit: async () => {
          if (complete) return
          await client.query('COMMIT')
          complete = true
        },
        rollback: async () => {
          if (complete) return
          await client.query('ROLLBACK')
          complete = true
        },
        queries: createQueries(client),
      }

      try {
        await client.query('BEGIN')
      } catch (error) {
        client.release()
        throw error
      }

      try {
        const result = await consumer(tx)
        if (!complete) { await client.query('COMMIT') }
        return result
      } catch (err) {
        if (!complete) { await client.query('ROLLBACK') }
        throw err
      } finally {
        client.release()
      }
    },
  }
}
