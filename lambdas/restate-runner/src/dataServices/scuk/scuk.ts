import type { Pool, PoolClient } from 'pg'
import type { ContactDetails } from './types.ts'

type CustomerRow = {
  customer_id: string
  title: string
  forename: string
  surname: string
  date_of_birth: string | null
  driving_licence_number: string | null
  passport_number: string | null
  address_line_one: string | null
  address_line_two: string | null
  address_line_three: string | null
  address_postcode: string | null
  email_address: string | null
  mobile_phone_number: string | null
  personal_phone_number: string | null
}

type ContractRow = {
  contract_id: string
  account_number: string | null
  eligible: boolean
  has_complaint: boolean
  monthly_payment_amount: string | null
  start_date: string
  sort_code: string | null
  vehicle_model: string | null
  vehicle_registration_number: string | null
}

export function createScukService(client: Pool | PoolClient) {
  return {
    clearData: async () => {
      await client.query('TRUNCATE TABLE scuk.customers, scuk.outreach CASCADE')
    },
    upsertCustomer: async (customerId: string, data: any, outreachId: string | null) => {
      await client.query(`
        INSERT INTO scuk.customers (customer_id, data)
        VALUES ($1, $2)
        ON CONFLICT (customer_id) DO UPDATE SET data = EXCLUDED.data
      `, [customerId, data])

      if (outreachId) {
        await client.query(`
          INSERT INTO scuk.outreach (outreach_id, outreach_type)
          VALUES ($1, 'email')
          ON CONFLICT DO NOTHING
        `, [outreachId])
        await client.query(`
          INSERT INTO scuk.customer_outreach_links (outreach_id, customer_id)
          VALUES ($1, $2)
          ON CONFLICT DO NOTHING
        `, [outreachId, customerId])
      }
    },
    upsertContract: async (contractId: string, customerId: string, data: any) => {
      await client.query(`
        INSERT INTO scuk.contracts (contract_id, customer_id, data)
        VALUES ($1, $2, $3)
        ON CONFLICT (contract_id) DO UPDATE SET customer_id = EXCLUDED.customer_id, data = EXCLUDED.data
      `, [contractId, customerId, data])
    },
    getCustomerIdsByOutreachId: async (outreachId: string) => {
      const result = await client.query<CustomerRow>(
        'SELECT customer_id FROM scuk.customer_outreach_links WHERE outreach_id = $1',
        [outreachId],
      )
      return result.rows.map(row => row.customer_id)
    },
    getCustomerIdsByDetails: async (forename: string, surname: string, dateOfBirth: string) => {
      const result = await client.query<CustomerRow>(`
        SELECT customer_id FROM scuk.customers
        WHERE data->>'forename' = $1 AND data->>'surname' = $2 AND data->>'dateOfBirth' = $3
      `, [forename, surname, dateOfBirth])
      return result.rows.map(row => row.customer_id)
    },
    getLatestContractMeta: async (customerIds: string[]) => {
      const result = await client.query<CustomerRow & ContractRow>(`
        SELECT
          contract_id,
          cus.data->'surname' AS surname,
          cus.data->'title' AS title
        FROM scuk.contracts con
        INNER JOIN scuk.customers cus ON con.customer_id = cus.customer_id
        WHERE cus.customer_id = ANY($1)
        ORDER BY con.data->>'startDate' DESC
        LIMIT 1;
      `, [customerIds])
      if (result.rows.length === 0) {
        return null
      }
      const firstRow = result.rows[0]!
      return {
        contractId: firstRow.contract_id,
        customerName: `${firstRow.title} ${firstRow.surname}`,
      }
    },
    getCustomersForMatch: async (customerIds: string[]) => {
      const result = await client.query<CustomerRow>(`
        SELECT
          customer_id,
          data->'drivingLicenceNumber' AS driving_licence_number,
          data->'address'->'postcode' AS address_postcode,
          data->'mobilePhoneNumber' AS mobile_phone_number
        FROM scuk.customers
        WHERE customer_id = ANY($1)
      `, [customerIds])
      return result.rows.map(row => ({
        id: row.customer_id as string,
        drivingLicenceNumber: row.driving_licence_number,
        postcode: row.address_postcode,
        mobilePhoneNumber: row.mobile_phone_number,
      }))
    },
    getCustomersForMatchResult: async (customerIds: string[], postcode: string) => {
      const result = await client.query<CustomerRow>(`
        SELECT customer_id
        FROM scuk.customers
        WHERE customer_id = ANY($1) AND data->'address'->>'postcode' = $2
      `, [customerIds, postcode])
      return result.rows.map(row => row.customer_id)
    },
    getVerificationFields: async (contractId: string) => {
      const result = await client.query<CustomerRow & ContractRow>(`
        SELECT
          con.data->'accountNumber' AS account_number,
          con.data->'hasComplaint' AS has_complaint,
          con.data->'monthlyPaymentAmount' AS monthly_payment_amount,
          con.data->'sortCode' AS sort_code,
          con.data->'startDate' AS start_date,
          con.data->'vehicleRegistrationNumber' AS vehicle_registration_number,
          cus.data->'dateOfBirth' AS date_of_birth,
          cus.data->'drivingLicenceNumber' AS driving_licence_number,
          cus.data->'emailAddress' AS email_address,
          cus.data->'mobilePhoneNumber' AS mobile_phone_number,
          cus.data->'passportNumber' AS passport_number,
          cus.data->'personalPhoneNumber' AS personal_phone_number
        FROM scuk.contracts con
        INNER JOIN scuk.customers cus ON con.customer_id = cus.customer_id
        WHERE con.contract_id = $1
      `, [contractId])
      const row = result.rows[0]!
      return {
        accountNumber: row.account_number,
        hasComplaint: row.has_complaint,
        monthlyPaymentAmount: row.monthly_payment_amount,
        sortCode: row.sort_code,
        startDate: row.start_date,
        vehicleRegistrationNumber: row.vehicle_registration_number,
        dateOfBirth: row.date_of_birth,
        drivingLicenceNumber: row.driving_licence_number,
        emailAddress: row.email_address,
        mobilePhoneNumber: row.mobile_phone_number,
        passportNumber: row.passport_number,
        personalPhoneNumber: row.personal_phone_number,
      }
    },
    getContactDetails: async (contractId: string) => {
      const result = await client.query<CustomerRow>(`
        SELECT
          cus.data->'address'->'lineOne' AS address_line_one,
          cus.data->'address'->'lineTwo' AS address_line_two,
          cus.data->'address'->'lineThree' AS address_line_three,
          cus.data->'address'->'postcode' AS address_postcode,
          cus.data->'emailAddress' AS email_address,
          cus.data->'forename' AS forename,
          cus.data->'mobilePhoneNumber' AS mobile_phone_number,
          cus.data->'surname' AS surname
        FROM scuk.customers cus
        INNER JOIN scuk.contracts con ON cus.customer_id = con.customer_id
        WHERE con.contract_id = $1
      `, [contractId])
      const row = result.rows[0]!
      return {
        addressLineOne: row.address_line_one ?? '',
        addressLineTwo: row.address_line_two ?? undefined,
        addressLineThree: row.address_line_three ?? undefined,
        addressPostcode: row.address_postcode ?? '',
        emailAddress: row.email_address ?? '',
        forename: row.forename ?? '',
        mobilePhoneNumber: row.mobile_phone_number ?? '',
        surname: row.surname ?? '',
      } satisfies ContactDetails
    },
    getContracts: async (customerIds: string[]) => {
      const result = await client.query<ContractRow>(`
        SELECT
          contract_id,
          data->'eligible' AS eligible,
          data->'vehicleModel' AS vehicle_model,
          data->'vehicleRegistrationNumber' AS vehicle_registration_number
        FROM scuk.contracts
        WHERE customer_id = ANY($1)
        ORDER BY data->>'startDate' DESC
      `, [customerIds])
      return result.rows.map(row => ({
        contractId: row.contract_id,
        eligible: row.eligible,
        vehicleModel: (row.vehicle_model ?? ''),
        vehicleRegistrationNumber: (row.vehicle_registration_number ?? ''),
      }))
    },
    getAccountNumber: async (contractId: string) => {
      const result = await client.query<ContractRow>(`
        SELECT
          data->'accountNumber' AS account_number,
          data->'sortCode' AS sort_code
        FROM scuk.contracts
        WHERE contract_id = $1
      `, [contractId])
      const row = result.rows[0]!
      if (row.account_number === null || row.sort_code === null) {
        return null
      }
      return row.account_number
    },
  }
}

export type ScukService = ReturnType<typeof createScukService>
