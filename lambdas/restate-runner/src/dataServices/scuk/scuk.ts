import type { Pool, PoolClient } from 'pg'

export function createScukService(client: Pool | PoolClient) {
  return {
    getCustomerIdsByOutreachId: async (outreachId: string) => {
      const result = await client.query(
        'SELECT customer_id FROM scuk.customer_outreach_links WHERE outreach_id = $1',
        [outreachId],
      )
      return result.rows.map(row => row.customer_id as string)
    },
    getCustomerIdsByDetails: async (forename: string, surname: string, dateOfBirth: string) => {
      const result = await client.query(`
        SELECT customer_id FROM scuk.customers
        WHERE data->>'forename' = $1 AND data->>'surname' = $2 AND data->>'dateOfBirth' = $3
      `, [forename, surname, dateOfBirth])
      return result.rows.map(row => row.customer_id as string)
    },
    getCustomersForMatch: async (customerIds: string[]) => {
      const result = await client.query(`
        SELECT
          customer_id,
          data->'drivingLicenceNumber' AS driving_licence_number,
          data->'postcode' AS postcode,
          data->'mobilePhoneNumber' AS mobile_phone_number
        FROM scuk.customers
        WHERE customer_id = ANY($1)
      `, [customerIds])
      return result.rows.map(row => ({
        id: row.customer_id as string,
        drivingLicenceNumber: row.driving_licence_number as string | null,
        postcode: row.postcode as string | null,
        mobilePhoneNumber: row.mobile_phone_number as string | null,
      }))
    },
    getCustomersForMatchResult: async (customerIds: string[], postcode: string) => {
      const result = await client.query(`
        SELECT customer_id
        FROM scuk.customers
        WHERE customer_id = ANY($1) AND data->>'postcode' = $2
      `, [customerIds, postcode])
      return result.rows.map(row => row.customer_id as string)
    },
    getVerificationFields: async (customerIds: string[]) => {
      const result = await client.query(`
        SELECT
          contract_id,
          con.data->'accountNumber' AS account_number,
          con.data->'hasComplaint' AS has_complaint,
          con.data->'monthlyPaymentAmount' AS monthly_payment_amount,
          con.data->'sortCode' AS sort_code,
          con.data->'startDate' AS start_date,
          con.data->'vehicleRegistrationNumber' AS vehicle_registration_number,
          cus.data->'dateOfBirth' AS date_of_birth,
          cus.data->'drivingLicenceNumber' AS driving_licence_number,
          cus.data->'emailAddress' AS email_address,
          cus.data->'isProactive' AS is_proactive,
          cus.data->'mobilePhoneNumber' AS mobile_phone_number,
          cus.data->'passportNumber' AS passport_number,
          cus.data->'personalPhoneNumber' AS personal_phone_number
        FROM scuk.contracts con
        INNER JOIN scuk.customers cus ON con.customer_id = cus.customer_id
        WHERE cus.customer_id = ANY($1)
        ORDER BY con.data->>'startDate' DESC
        LIMIT 1;
      `, [customerIds])
      if (result.rows.length === 0) {
        return null
      }
      const row = result.rows[0]!
      return {
        contractId: row.contract_id as string,
        accountNumber: row.account_number as string | null,
        hasComplaint: row.has_complaint as boolean,
        monthlyPaymentAmount: row.monthly_payment_amount as number | null,
        sortCode: row.sort_code as string | null,
        startDate: row.start_date as string,
        vehicleRegistrationNumber: row.vehicle_registration_number as string | null,
        dateOfBirth: row.date_of_birth as string | null,
        drivingLicenceNumber: row.driving_licence_number as string | null,
        emailAddress: row.email_address as string | null,
        isProactive: row.is_proactive as boolean,
        mobilePhoneNumber: row.mobile_phone_number as string | null,
        passportNumber: row.passport_number as string | null,
        personalPhoneNumber: row.personal_phone_number as string | null,
      }
    },
  }
}

export type ScukService = ReturnType<typeof createScukService>
