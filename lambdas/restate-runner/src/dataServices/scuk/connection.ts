import { fromEnv } from '../../constants.ts'
import { createDbConnection, type DbConnection } from '../dbConnection.ts'
import { createScukService, type ScukService } from './scuk.ts'

let scukDb: DbConnection<ScukService>

export const getScukDb = () => {
  if (scukDb === undefined) {
    scukDb = createDbConnection({
      region: fromEnv('AWS_REGION'),
      host: fromEnv('PG_SCUK_HOST')!,
      port: parseInt(fromEnv('PG_SCUK_PORT') ?? '5432', 10),
      database: fromEnv('PG_SCUK_DATABASE'),
      user: fromEnv('PG_SCUK_USER'),
      password: fromEnv('PG_SCUK_PASSWORD'),
      ca: fromEnv('PG_CA'),
      connectionTimeoutMillis: 10000,
    }, createScukService)
  }

  return scukDb
}
