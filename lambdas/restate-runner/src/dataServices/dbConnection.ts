import { Signer } from '@aws-sdk/rds-signer'
import { fromNodeProviderChain } from '@aws-sdk/credential-providers'
import logger from '../logger.ts'
import { createPgClient, type CreateQueriesFn } from './pgClient.ts'

type PoolOptions = {
  region?: string | undefined
  host: string
  port: number
  database?: string | undefined
  user?: string | undefined
  password?: string | undefined
  ca?: string | undefined
  connectionTimeoutMillis?: number | undefined
}

type Password = PoolOptions['password'] | (() => Promise<string>)

export const createDbConnection = <
  TCreateQueriesFn extends CreateQueriesFn<TQueries>,
  TQueries = ReturnType<TCreateQueriesFn>,
>(options: PoolOptions, createQueries: TCreateQueriesFn) => {
  const user = options.user ?? 'postgres'
  // eslint-disable-next-line prefer-destructuring
  let password: Password = options.password
  if (password === undefined) {
    const signer = new Signer({
      region: options.region,
      hostname: options.host,
      port: options.port,
      username: user,
      credentials: fromNodeProviderChain({ clientConfig: { region: options.region } }),
    })
    password = () => signer.getAuthToken()
  }

  try {
    const pgClient = createPgClient<TQueries>({
      host: options.host,
      port: options.port,
      database: options.database ?? 'postgres',
      user,
      password,
      ssl: options.ca === undefined ? undefined : { ca: Buffer.from(options.ca, 'base64') },
      connectionTimeoutMillis: options.connectionTimeoutMillis ?? 10000,
    }, createQueries)
    pgClient.pool.on('error', (error) => {
      logger.error(error, 'Postgres pool error', { host: options.host, port: options.port, database: options.database, user })
    })
    return pgClient
  } catch (error) {
    if (error instanceof Error) {
      logger.error(error, 'Error creating Postgres pool', { host: options.host, port: options.port, database: options.database, user })
    }
    throw error
  }
}

export type DbConnection<TQueries> = ReturnType<typeof createPgClient<TQueries>>
