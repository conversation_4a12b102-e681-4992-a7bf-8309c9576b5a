import { describe, test, expect } from 'vitest'
import { validateBankDetails } from './bankModulus.ts'

describe('Bank account modulus', () => {
  test('Pass modulus 10 check', () => {
    const sortCode = '089999'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Pass modulus 11 check', () => {
    const sortCode = '107999'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Pass modulus 11 and double alternate checks', () => {
    const sortCode = '202959'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 10 & 11 where first check passes and second check fails', () => {
    const sortCode = '871427'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 10 & 11 where first check fails and second check passes', () => {
    const sortCode = '872427'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 10 where in the account number ab=09 and the g=9. The first check passes and second check fails', () => {
    const sortCode = '871427'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 10 where in the account number ab=99 and the g=9. The first check passes and the second check fails', () => {
    const sortCode = '871427'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 3, and the sorting code is the start of a range. As c=6 the second check should be ignored', () => {
    const sortCode = '820000'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 3, and the sorting code is the end of a range. As c=9 the second check should be ignored', () => {
    const sortCode = '827999'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 3. As c<>6 or 9 perform both checks pass', () => {
    const sortCode = '827101'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 4 where the remainder is equal to the checkdigit', () => {
    const sortCode = '134020'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 1 - ensures that 27 has been added to the accumulated total and passes double alternate modulus check', () => {
    const sortCode = '118765'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 6 where the account fails standard check but is a foreign currency account', () => {
    const sortCode = '200915'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 5 where the check passes', () => {
    const sortCode = '938611'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 5 where the check passes with substitution', () => {
    const sortCode = '938600'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 5 where both checks produce a remainder of 0 and pass', () => {
    const sortCode = '938063'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 7 where passes but would fail the standard check', () => {
    const sortCode = '772798'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 8 where the check passes', () => {
    const sortCode = '086090'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 2 & 9 where the first check passes', () => {
    const sortCode = '309070'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 2 & 9 where the first check fails and second check passes with substitution', () => {
    const sortCode = '309070'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 2 & 9 where a!=0 and g!=9 and passes', () => {
    const sortCode = '309070'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 2 & 9 where a!=0 and g=9 and passes', () => {
    const sortCode = '309070'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 5 where the first checkdigit is correct and the second incorrect', () => {
    const sortCode = '938063'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(false)
  })

  test('Exception 5 where the first checkdigit is incorrect and the second correct', () => {
    const sortCode = '938063'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(false)
  })

  test('Exception 5 where the first checkdigit is incorrect with a remainder of 1', () => {
    const sortCode = '938063'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(false)
  })

  test('Exception 1 where it fails double alternate check', () => {
    const sortCode = '118765'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(false)
  })

  test('Pass modulus 11 check and fail double alternate check', () => {
    const sortCode = '203099'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(false)
  })

  test('Fail modulus 11 check and pass double alternate check', () => {
    const sortCode = '203099'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(false)
  })

  test('Fail modulus 10 check', () => {
    const sortCode = '089999'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(false)
  })

  test('Fail modulus 11 check', () => {
    const sortCode = '107999'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(false)
  })

  test('Exception 12/13 where passes modulus 11 check (in this example, modulus 10 check fails, however, there is no need for it to be performed as the first check passed)', () => {
    const sortCode = '074456'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 12/13 where passes the modulus 11check (in this example, modulus 10 check passes as well, however, there is no need for it to be performed as the first check passed)', () => {
    const sortCode = '070116'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 12/13 where fails the modulus 11 check, but passes the modulus 10 check', () => {
    const sortCode = '074456'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })

  test('Exception 14 where the first check fails and the second check passes', () => {
    const sortCode = '180002'
    const accountNumber = '********'
    const result = validateBankDetails(sortCode, accountNumber)
    expect(result).toBe(true)
  })
})
