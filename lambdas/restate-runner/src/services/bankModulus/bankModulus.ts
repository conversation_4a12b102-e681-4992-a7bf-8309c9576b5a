import { loadTxt } from './loadTxt.ts'

type ModType = 'MOD10' | 'MOD11' | 'DBLAL'

type Range = {
  start: number
  end: number
  modType: ModType
  weights: number[]
  exception: number
}

type ValidateResult =
  | 'valid' // passed validation, no more checks
  | 'invalid' // failed validation, no more checks
  | 'pass' // passed check, but could still fail validation later
  | 'fail' // failed check, but could still pass validation later

const rangeStartList: number[] = []
let rangeMap: Map<number, Range[]>
const substitutionMap: Map<string, string> = new Map()

export function validateBankDetails(sortCode: string, accountNumber: string): boolean {
  if (rangeMap === undefined) {
    loadData()
  }

  const subSortCode = substitutionMap.has(sortCode) ? substitutionMap.get(sortCode)! : sortCode
  const ranges = getRanges(+subSortCode)
  if (ranges === undefined) { return true }

  const values = subSortCode.concat(accountNumber).split('').map(v => +v)

  let pass = true
  for (const range of ranges) {
    let result: ValidateResult
    switch (range.exception) {
      case 1: {
        result = validateException1(values, range)
        break
      }
      case 2: {
        result = validateException2(values, range)
        break
      }
      case 3: {
        result = validateException3(values, range)
        break
      }
      case 4: {
        result = validateException4(values, range)
        break
      }
      case 5: {
        result = validateException5(values, range, subSortCode !== sortCode)
        break
      }
      case 6: {
        result = validateException6(values, range)
        break
      }
      case 7: {
        result = validateException7(values, range)
        break
      }
      case 8: {
        result = validateException8(values, range)
        break
      }
      case 9: {
        result = validateException9(values, range)
        break
      }
      case 10: {
        result = validateException10(values, range)
        break
      }
      case 11:
      case 12:
      case 13: {
        result = validateException11or12or13(values, range)
        break
      }
      case 14: {
        result = validateException14(values, range)
        break
      }
      default: {
        result = validateNormal(values, range)
        break
      }
    }
    switch (result) {
      case 'valid':
        return true
      case 'invalid':
        return false
      case 'pass':
        break
      case 'fail':
        pass = false
        break
    }
  }
  return pass
}

function loadData() {
  const weightsFileContent = loadTxt('weights.txt')
  const weightsLines = weightsFileContent.split('\n')

  rangeMap = new Map<number, Range[]>()

  for (const line of weightsLines) {
    const trimmedLine = line.replaceAll(/\s+/g, ' ').trim()
    if (trimmedLine.length === 0) { continue }
    const parts = trimmedLine.split(' ')
    const range: Range = {
      start: +parts[0]!,
      end: +parts[1]!,
      modType: parts[2] as ModType,
      weights: parts.slice(3, 17).map(n => +n),
      exception: parts[17] ? +parts[17] : 0,
    }
    if (rangeMap.has(range.start)) {
      rangeMap.get(range.start)!.push(range)
    } else {
      rangeStartList.push(range.start)
      rangeMap.set(range.start, [range])
    }
  }

  const substitutionsFileContent = loadTxt('substitutions.txt')
  const substitutionLines = substitutionsFileContent.split('\n')

  for (const line of substitutionLines) {
    const trimmedLine = line.replaceAll(/\s+/g, ' ').trim()
    if (trimmedLine.length === 0) { continue }
    const parts = trimmedLine.split(' ')
    if (parts.length !== 2) { continue }
    substitutionMap.set(parts[0]!, parts[1]!)
  }
}

function getRanges(sortCodeNum: number): Range[] | undefined {
  const rangeStart = findRangeStart(rangeStartList, sortCodeNum)
  if (rangeStart === undefined) { return undefined }
  const ranges = rangeMap.get(rangeStart)!
  if (sortCodeNum > ranges[0]!.end) { return undefined }
  return ranges
}

function findRangeStart(arr: number[], target: number): number | undefined {
  let result = arr[0]!
  let low = 0
  let high = arr.length - 1

  while (low <= high) {
    const mid = Math.floor((low + high) / 2)
    result = arr[mid]!

    if (result === target) {
      return result
    }
    if (result < target) {
      low = mid + 1
    } else {
      high = mid - 1
    }
  }

  return low > 0 ? arr[low - 1] : undefined
}

function validateNormal(values: number[], range: Range): ValidateResult {
  const weightedSum = getWeightedSum(range.modType, values, range.weights)
  return weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'pass' : 'invalid'
}

function validateException1(values: number[], range: Range): ValidateResult {
  const weightedSum = getWeightedSum(range.modType, values, range.weights)
  return (weightedSum + 27) % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'pass' : 'invalid'
}

function validateException2(values: number[], range: Range): ValidateResult {
  let { weights } = range
  const a = getByLetter(values, 'a')
  const g = getByLetter(values, 'g')
  if (a !== 0) {
    if (g === 9) {
      weights = [0, 0, 0, 0, 0, 0, 0, 0, 8, 7, 10, 9, 3, 1]
    } else {
      weights = [0, 0, 1, 2, 5, 3, 6, 4, 8, 7, 10, 9, 3, 1]
    }
  }
  const weightedSum = getWeightedSum(range.modType, values, weights)
  return weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'valid' : 'fail'
}

function validateException3(values: number[], range: Range): ValidateResult {
  const c = getByLetter(values, 'c')
  if (c === 6 || c === 9) { return 'pass' }
  const weightedSum = getWeightedSum(range.modType, values, range.weights)
  return weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'pass' : 'invalid'
}

function validateException4(values: number[], range: Range): ValidateResult {
  const weightedSum = getWeightedSum(range.modType, values, range.weights)
  const modulus = weightedSum % (range.modType === 'MOD11' ? 11 : 10)
  const gh = +getByLetters(values, 'gh')
  return modulus === gh ? 'pass' : 'invalid'
}

function validateException5(values: number[], range: Range, isSub: boolean): ValidateResult {
  const weightedSum = getWeightedSum(range.modType, values, range.weights)
  let result
  if (range.modType === 'MOD11') {
    const modulus = weightedSum % 11
    const g = getByLetter(values, 'g')
    if (modulus === 0 && g === 0) {
      result = true
    } else if (modulus === 1) {
      result = false
    } else {
      result = (11 - modulus) === g
    }
  } else {
    const modulus = weightedSum % 10
    const h = getByLetter(values, 'h')
    if (modulus === 0 && h === 0) {
      result = true
    } else {
      result = (10 - modulus) === h
    }
  }
  if (isSub) { return result ? 'valid' : 'invalid' }
  return result ? 'pass' : 'fail'
}

function validateException6(values: number[], range: Range): ValidateResult {
  const a = getByLetter(values, 'a')
  if (a >= 4 && a <= 8) {
    const g = getByLetter(values, 'g')
    const h = getByLetter(values, 'h')
    if (g === h) {
      return 'pass'
    }
  }
  const weightedSum = getWeightedSum(range.modType, values, range.weights)
  return weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'pass' : 'invalid'
}

function validateException7(values: number[], range: Range): ValidateResult {
  let { weights } = range
  const g = getByLetter(values, 'g')
  if (g === 9) {
    weights = [0, 0, 0, 0, 0, 0, 0, 0].concat(range.weights.slice(8))
  }
  const weightedSum = getWeightedSum(range.modType, values, weights)
  return weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'pass' : 'invalid'
}

function validateException8(values: number[], range: Range): ValidateResult {
  const updatedValues = [0, 9, 0, 1, 2, 6].concat(values.slice(6))
  const weightedSum = getWeightedSum(range.modType, updatedValues, range.weights)
  return weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'pass' : 'invalid'
}

function validateException9(values: number[], range: Range): ValidateResult {
  const euroSortCode = '309634'
  const updatedValues = euroSortCode.split('').map(n => +n).concat(values.slice(6))
  const weightedSum = getWeightedSum(range.modType, updatedValues, rangeMap.get(+euroSortCode)![0]!.weights)
  return weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'valid' : 'fail'
}

function validateException10(values: number[], range: Range): ValidateResult {
  let { weights } = range
  const ab = getByLetters(values, 'ab')
  const g = getByLetter(values, 'g')
  if ((ab === '09' || ab === '99') && g === 9) {
    weights = [0, 0, 0, 0, 0, 0, 0, 0].concat(range.weights.slice(8))
  }
  const weightedSum = getWeightedSum(range.modType, values, weights)
  return weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'valid' : 'fail'
}

function validateException11or12or13(values: number[], range: Range): ValidateResult {
  const weightedSum = getWeightedSum(range.modType, values, range.weights)
  return weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'valid' : 'fail'
}

function validateException14(values: number[], range: Range): ValidateResult {
  const weightedSum = getWeightedSum(range.modType, values, range.weights)
  if (weightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0) {
    return 'valid'
  }
  const h = getByLetter(values, 'h')
  if (h !== 0 && h !== 1 && h !== 9) {
    return 'invalid'
  }
  const updatedValues = values.slice(0, 6).concat(0, values.slice(6, 13))
  const updatedWeightedSum = getWeightedSum(range.modType, updatedValues, range.weights)
  return updatedWeightedSum % (range.modType === 'MOD11' ? 11 : 10) === 0 ? 'valid' : 'invalid'
}

// eslint-disable-next-line consistent-return
function getWeightedSum(modType: ModType, values: number[], weights: number[]): number {
  switch (modType) {
    case 'MOD10':
    case 'MOD11':
      return values.reduce((acc, v, i) => acc + (v * weights[i]!), 0)
    case 'DBLAL':
      return values.reduce((acc, v, i) => acc + (v * weights[i]!), '').split('').reduce((acc, v) => acc + +v, 0)
  }
}

function getByLetters(values: number[], letters: string): string {
  const result = []
  for (const letter of letters) {
    result.push(getByLetter(values, letter))
  }
  return result.join('')
}

function getByLetter(values: number[], letter: string): number {
  switch (letter) {
    case 'u': return values[0]!
    case 'v': return values[1]!
    case 'w': return values[2]!
    case 'x': return values[3]!
    case 'y': return values[4]!
    case 'z': return values[5]!
    case 'a': return values[6]!
    case 'b': return values[7]!
    case 'c': return values[8]!
    case 'd': return values[9]!
    case 'e': return values[10]!
    case 'f': return values[11]!
    case 'g': return values[12]!
    case 'h': return values[13]!
  }
  throw new Error('Invalid letter in getByLetter')
}
