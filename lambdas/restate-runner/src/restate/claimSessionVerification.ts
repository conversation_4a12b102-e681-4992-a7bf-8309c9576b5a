import * as restate from '@restatedev/restate-sdk'
import * as z from 'zod/mini'
import { createVirtualObject } from './util.ts'
import dataServices from '../dataServices.ts'
import { getNextQuestionIds, getQuestionFieldById } from './claimSessionVerification/verification.ts'
import type { Persona, VerificationFields } from './claimSessionVerification/verification.ts'

const answerSchema = z.union([
  z.number(),
  z.string(),
])

const answersSchema = z.union([
  z.object({
    skip: z.literal(true),
  }),
  z.object({
    skip: z.optional(z.undefined()),
    answers: z.record(z.string(), answerSchema),
  }),
])

type GetQuestionsInput = { isProactive: boolean; latestContractId: string }
export type GetQuestionsOutput = { questions: string[] }
export type SubmitAnswersInput = z.infer<typeof answersSchema>
export type SubmitAnswersOutput = { success: true; fail?: undefined } | { success: false; fail: boolean }

type State = {
  attempts: number
  fields: VerificationFields
  persona: Persona
  questionIds: string[]
  lastContractQuestionId: string
  lastCustomerQuestionId: string
}

export const claimSessionVerification = createVirtualObject({
  name: 'claim-session-verification',
  handlers: {
    getQuestions: async (ctx: restate.ObjectContext<State>, input: GetQuestionsInput): Promise<GetQuestionsOutput> => {
      const questionIds = await ctx.get('questionIds')
      if (questionIds !== null) {
        return { questions: questionIds }
      }

      let fields = await ctx.get('fields')
      let persona
      if (fields === null) {
        const verificationData = await ctx.run(
          'Get verification data',
          async () => {
            const result = await dataServices.scuk.queries.getVerificationFields(input.latestContractId)
            const startDate = new Date(result.startDate)
            const isAgedLoan = startDate.getUTCFullYear() < 2019
            let personaResult: Persona
            if (input.isProactive) {
              if (isAgedLoan) {
                if (result.hasComplaint) {
                  personaResult = 'P4'
                } else {
                  personaResult = 'P3'
                }
              } else if (result.hasComplaint) {
                personaResult = 'P2'
              } else {
                personaResult = 'P1'
              }
            } else if (isAgedLoan) {
              personaResult = 'R2'
            } else {
              personaResult = 'R1'
            }
            return {
              persona: personaResult,
              fields: {
                contractId: input.latestContractId,
                accountNumber: getLast4Chars(result.accountNumber),
                monthlyPaymentAmount: result.monthlyPaymentAmount,
                startDate: `${startDate.getUTCMonth() + 1}/${startDate.getUTCFullYear()}`,
                sortCode: result.sortCode,
                vehicleRegistrationNumber: result.vehicleRegistrationNumber,
                dateOfBirth: result.dateOfBirth,
                drivingLicenceNumber: result.drivingLicenceNumber,
                emailAddress: result.emailAddress,
                mobilePhoneNumber: getLast4Chars(result.mobilePhoneNumber),
                passportNumber: result.passportNumber,
                personalPhoneNumber: getLast4Chars(result.personalPhoneNumber),
              },
            }
          },
          { maxRetryAttempts: 4 },
        )
        persona = verificationData.persona
        ctx.set('persona', verificationData.persona)
        fields = verificationData.fields
        ctx.set('fields', fields)
      } else {
        persona = (await ctx.get('persona'))!
      }

      const {
        questions,
        nextContractQuestionId,
        nextCustomerQuestionId,
      } = getNextQuestionIds(null, null, persona, fields)

      ctx.set('questionIds', questions)
      if (nextContractQuestionId !== null) {
        ctx.set('lastContractQuestionId', nextContractQuestionId)
      }
      if (nextCustomerQuestionId !== null) {
        ctx.set('lastCustomerQuestionId', nextCustomerQuestionId)
      }
      return { questions }
    },
    submitAnswers: async (ctx: restate.ObjectContext<State>, rawInput: SubmitAnswersInput): Promise<SubmitAnswersOutput> => {
      const parsedInput = answersSchema.safeParse(rawInput)
      if (!parsedInput.success) {
        throw new restate.TerminalError('Invalid input', { errorCode: 400 })
      }
      const input = parsedInput.data

      const fields = await ctx.get('fields')
      if (fields === null) {
        throw new restate.TerminalError('Verification fields not set', { errorCode: 409 })
      }

      if (input.skip !== true) {
        const questionIds = (await ctx.get('questionIds'))!
        let answersAreCorrect = true

        for (const questionId of questionIds) {
          const submittedAnswer = input.answers[questionId]
          if (submittedAnswer === undefined) {
            ctx.console.warn('Missing answer for question', { questionId })
            answersAreCorrect = false
            break
          }
          const questionField = getQuestionFieldById(questionId)
          if (fields[questionField] !== submittedAnswer) {
            answersAreCorrect = false
            break
          }
        }

        if (answersAreCorrect) {
          ctx.clearAll()
          return { success: true }
        }
      }

      let attempts = (await ctx.get('attempts')) ?? 0
      attempts += 1
      if (attempts < 3) {
        ctx.set('attempts', attempts)
        const persona = (await ctx.get('persona'))!
        const lastContractQuestionId = await ctx.get('lastContractQuestionId')
        const lastCustomerQuestionId = await ctx.get('lastCustomerQuestionId')
        const { questions, nextContractQuestionId, nextCustomerQuestionId } = getNextQuestionIds(
          lastContractQuestionId,
          lastCustomerQuestionId,
          persona,
          fields,
        )

        if (questions.length === 2) {
          ctx.set('questionIds', questions)
          if (nextContractQuestionId !== null) {
            ctx.set('lastContractQuestionId', nextContractQuestionId)
          }
          if (nextCustomerQuestionId !== null) {
            ctx.set('lastCustomerQuestionId', nextCustomerQuestionId)
          }
          return { success: false, fail: false }
        }
      }

      ctx.clearAll()
      return { success: false, fail: true }
    },
    clear: restate.handlers.object.exclusive(
      {
        enableLazyState: true,
        journalRetention: 0,
      },
      async (ctx: restate.ObjectContext<State>) => {
        ctx.clearAll()
      },
    ),
  },
  options: {
    ingressPrivate: true,
  },
})

function getLast4Chars(str: string | null): string | null {
  if (str === null || str.length < 4) return null
  return str.slice(-4)
}
