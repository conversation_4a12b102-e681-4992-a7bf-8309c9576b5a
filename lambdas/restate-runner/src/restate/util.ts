import * as restate from '@restatedev/restate-sdk'
import type * as restateCore from '@restatedev/restate-sdk-core'
import type { Context as AWSContext } from 'aws-lambda'
import logger, { formatAsUuid } from '../logger.ts'
import { als } from '../als.ts'

type Context = restate.Context & { key: string }

type AlsHeaders = Record<string, string>

function getHeaders(): AlsHeaders | undefined {
  const store = als.getStore()
  if (store === undefined) return undefined
  return store.app.get('apiHeaders')
}

export function optsWithHeaders<I, O>(opts?: restate.ClientCallOptions<I, O>): restate.Opts<I, O> {
  const headers = getHeaders()
  if (headers === undefined) {
    return restate.Opts.from(opts === undefined ? {} : opts)
  }

  return restate.Opts.from({
    ...opts,
    headers: {
      ...headers,
      ...opts?.headers,
    },
  })
}

export function sendOptsWithHeaders<I>(opts?: restate.ClientSendOptions<I>): restate.SendOpts<I> {
  const headers = getHeaders()
  if (headers === undefined) {
    return restate.SendOpts.from(opts === undefined ? {} : opts)
  }

  return restate.SendOpts.from({
    ...opts,
    headers: {
      ...headers,
      ...opts?.headers,
    },
  })
}

function makeProxy<T>(target: T): T {
  return new Proxy({}, {
    get: (_, method) => {
      // @ts-ignore
      const fn = target[method] as (...args: unknown[]) => unknown
      return (...args: unknown[]) => {
        if (args.length === 0) {
          return fn(optsWithHeaders())
        }
        if (args.length === 1) {
          if (args[0] instanceof restate.Opts) {
            return fn(optsWithHeaders(args[0].getOpts()))
          }
          return fn(args[0], optsWithHeaders())
        }
        if (args.length === 2 && args[1] instanceof restate.Opts) {
          return fn(args[0], optsWithHeaders(args[1].getOpts()))
        }
        return fn(...args)
      }
    },
  }) as T
}

function getKey(ctx: Context) {
  // `ctx.key` is a getter that can throw an error if using service Context
  try {
    return ctx.key
  } catch {
    return undefined
  }
}

function getRestateHandlerSymbol(handler: unknown) {
  const symbols = Object.getOwnPropertySymbols(handler)
  for (const symbol of symbols) {
    if (symbol.toString() === 'Symbol(Handler)') {
      return symbol
    }
  }
  return null
}

function createHandler(name: string, handlerName: string, fn: (ctx: Context, input: unknown) => unknown) {
  const handler = (ctx: Context, input: unknown) => als.run({ app: new Map() }, () => {
    logger.configureStore()
    const req = ctx.request()
    const lambdaContext = req.extraArgs[0] as AWSContext
    let correlationId = req.headers.get('x-correlation-id')
    const traceparent = req.attemptHeaders.get('traceparent')
    let traceId: string | undefined
    let spanId: string | undefined
    if (typeof traceparent === 'string') {
      const traceParts = traceparent.split('-')
      if (traceParts.length === 4) {
        [, traceId, spanId] = traceParts
        if (correlationId === undefined) {
          correlationId = formatAsUuid(traceId!)
        }
      }
    }
    const store = als.getStore()
    if (store !== undefined) {
      const headers: AlsHeaders = {}
      if (correlationId !== undefined) {
        headers['x-correlation-id'] = correlationId
      }
      store.app.set('apiHeaders', headers)
    }
    logger.setRootArg('awsRequestId', lambdaContext.awsRequestId)
    logger.setRootArg('correlationId', correlationId)
    logger.setRootArg('traceId', traceId)
    logger.setRootArg('spanId', spanId)
    logger.setRootArg('restate', {
      invocationId: req.id,
      serviceName: name,
      handlerName,
      key: getKey(ctx),
    })

    const rawServiceClient = ctx.serviceClient.bind(ctx)
    ctx.serviceClient = <TDef>(def: restateCore.ServiceDefinitionFrom<TDef>) => makeProxy(rawServiceClient(def))
    const rawObjectClient = ctx.objectClient.bind(ctx)
    ctx.objectClient = <TDef>(def: restateCore.VirtualObjectDefinitionFrom<TDef>, key: string) => (
      makeProxy(rawObjectClient(def, key))
    )
    const rawWorkflowClient = ctx.workflowClient.bind(ctx)
    ctx.workflowClient = <TDef>(def: restateCore.WorkflowDefinitionFrom<TDef>, key: string) => (
      makeProxy(rawWorkflowClient(def, key))
    )
    return fn(ctx, input)
  })

  const restateSymbol = getRestateHandlerSymbol(fn)
  if (restateSymbol === null) { return handler }
  // @ts-ignore
  const instance = fn[restateSymbol]
  instance.handler = handler
  return instance.transpose()
}

export function createService<TName extends string, TDef>(service: Parameters<typeof restate.service<TName, TDef>>[0]) {
  const handlers = {} as typeof service.handlers
  for (const [key, value] of Object.entries(service.handlers)) {
    // @ts-ignore
    handlers[key] = createHandler(service.name, key, value)
  }
  return restate.service({
    name: service.name,
    handlers,
    options: service.options,
    description: service.description,
    metadata: service.metadata,
  })
}

export function createVirtualObject<TName extends string, TDef>(object: Parameters<typeof restate.object<TName, TDef>>[0]) {
  const handlers = {} as typeof object.handlers
  for (const [key, value] of Object.entries(object.handlers)) {
    // @ts-ignore
    handlers[key] = createHandler(object.name, key, value)
  }
  return restate.object({
    name: object.name,
    handlers,
    options: object.options,
    description: object.description,
    metadata: object.metadata,
  })
}

export function createWorkflow<TName extends string, TDef>(workflow: Parameters<typeof restate.workflow<TName, TDef>>[0]) {
  const handlers = {} as typeof workflow.handlers
  for (const [key, value] of Object.entries(workflow.handlers)) {
    // @ts-ignore
    handlers[key] = createHandler(workflow.name, key, value)
  }
  return restate.workflow({
    name: workflow.name,
    handlers,
    options: workflow.options,
    description: workflow.description,
    metadata: workflow.metadata,
  })
}
