import crypto from 'node:crypto'
import * as restate from '@restatedev/restate-sdk'
import { GetRandomPasswordCommand, GetSecretValueCommand, PutSecretValueCommand } from '@aws-sdk/client-secrets-manager'
import * as z from 'zod/v4-mini'
import logger from '../logger.ts'
import { createVirtualObject } from './util.ts'
import { getSecretsManagerClient } from '../remoteServices/connectors/secretsManager.ts'

const secretOptionsSchema = z.object({
  ExcludeCharacters: z.optional(z.string()),
  ExcludeLowercase: z.optional(z.boolean()),
  ExcludeNumbers: z.optional(z.boolean()),
  ExcludePunctuation: z.optional(z.boolean()),
  ExcludeUppercase: z.optional(z.boolean()),
  IncludeSpace: z.optional(z.boolean()),
  PasswordLength: z.optional(z.number()),
  RequireEachIncludedType: z.optional(z.boolean()),
})

const inputSchema = z.optional(secretOptionsSchema)

type SecretOptions = z.infer<typeof secretOptionsSchema>

type State = {
  scheduledId: string
  secretOptions: SecretOptions
}

const delay = 1000 * 60 * 60 * 24 * 7 // 1 week

export const secretRotation = createVirtualObject({
  name: 'secret-rotation',
  handlers: {
    startSchedule: async (ctx: restate.ObjectContext<State>, options: z.infer<typeof inputSchema>) => {
      const parsedOptions = inputSchema.safeParse(options)
      if (!parsedOptions.success) {
        throw new restate.TerminalError('Invalid secret rotation options', { errorCode: 400 })
      }
      const scheduledId = await ctx.get('scheduledId')
      if (scheduledId === null) {
        const secretId = ctx.key
        await ctx.run('Check secret access', async () => {
          const client = getSecretsManagerClient()
          try {
            await client.send(new GetSecretValueCommand({ SecretId: secretId }))
          } catch (error) {
            if (error instanceof Error) {
              if (error.name === 'AccessDeniedException') {
                logger.warn('Do not have IAM permissions for secret ID', { secretId })
                throw new restate.TerminalError('Do not have IAM permissions for secret ID', { errorCode: 409 })
              }
              if (error.name === 'ResourceNotFoundException') {
                logger.warn('Secret ID does not exist', { secretId })
                throw new restate.TerminalError('Secret ID does not exist', { errorCode: 404 })
              }
            }
            throw error
          }
        })
        if (parsedOptions.data !== undefined) {
          ctx.set('secretOptions', parsedOptions.data)
        }
        const handle = ctx.objectSendClient(secretRotation, ctx.key).rotate(restate.SendOpts.from({ delay }))
        ctx.set('scheduledId', await handle.invocationId)
      }
    },
    stopSchedule: async (ctx: restate.ObjectContext<State>) => {
      const scheduledId = await ctx.get('scheduledId')
      if (scheduledId !== null) {
        ctx.cancel(restate.InvocationIdParser.fromString(scheduledId))
      }
      ctx.clearAll()
    },
    rotate: async (ctx: restate.ObjectContext<State>) => {
      const scheduledId = await ctx.get('scheduledId')
      if (scheduledId !== null && scheduledId !== ctx.request().id) {
        ctx.cancel(restate.InvocationIdParser.fromString(scheduledId))
      }
      ctx.clear('scheduledId')
      const secretId = ctx.key
      const secretOptions = await ctx.get('secretOptions') ?? {}
      const nextVersionId = await ctx.run('Generate next version ID', () => crypto.randomUUID())
      await ctx.run('Rotate secret', async () => {
        const client = getSecretsManagerClient()
        const getSecretResult = await client.send(new GetSecretValueCommand({ SecretId: secretId }))
        if (getSecretResult.VersionId === nextVersionId) {
          return
        }
        const getRandomResult = await client.send(new GetRandomPasswordCommand(secretOptions))
        if (getRandomResult.RandomPassword === undefined) {
          throw new Error('Failed to get random password')
        }
        await client.send(new PutSecretValueCommand({
          SecretId: secretId,
          SecretString: getRandomResult.RandomPassword,
          ClientRequestToken: nextVersionId,
        }))
      })
      const handle = ctx.objectSendClient(secretRotation, ctx.key).rotate(restate.SendOpts.from({ delay }))
      ctx.set('scheduledId', await handle.invocationId)
    },
    updateSecretOptions: async (ctx: restate.ObjectContext<State>, options: SecretOptions) => {
      const parsedOptions = secretOptionsSchema.safeParse(options)
      if (!parsedOptions.success) {
        throw new restate.TerminalError('Invalid secret rotation options', { errorCode: 400 })
      }
      ctx.set('secretOptions', parsedOptions.data)
    },
  },
})

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const typecheckSecretOptions: SecretOptions extends GetRandomPasswordCommand['input']
  ? GetRandomPasswordCommand['input'] extends SecretOptions
    ? true
    : never
  : never = true
// Ensure that SecretOptions matches GetRandomPasswordCommand input
