export type Persona = 'P1' | 'P2' | 'P3' | 'P4' | 'R1' | 'R2'

export type VerificationFields = Record<string, string | number | null>

type QuestionConfig = {
  id: string
  field: string
} & { [persona in Persona]: boolean }

const contractQuestionsConfig: QuestionConfig[] = [
  {
    id: 'agreement_number',
    field: 'contractId',
    P1: true,
    P2: false,
    P3: false,
    P4: false,
    R1: true,
    R2: true,
  },
  {
    id: 'account_number',
    field: 'accountNumber',
    P1: true,
    P2: true,
    P3: true,
    P4: true,
    R1: true,
    R2: true,
  },
  {
    id: 'monthly_payment',
    field: 'monthlyPaymentAmount',
    P1: true,
    P2: true,
    P3: true,
    P4: true,
    R1: true,
    R2: true,
  },
  {
    id: 'sort_code',
    field: 'sortCode',
    P1: true,
    P2: true,
    P3: true,
    P4: true,
    R1: true,
    R2: true,
  },
  {
    id: 'vehicle_registration_number',
    field: 'vehicleRegistrationNumber',
    P1: true,
    P2: true,
    P3: true,
    P4: true,
    R1: true,
    R2: true,
  },
]

const customerQuestionsConfig: QuestionConfig[] = [
  {
    id: 'driving_licence_number',
    field: 'drivingLicenceNumber',
    P1: true,
    P2: true,
    P3: true,
    P4: true,
    R1: true,
    R2: true,
  },
  {
    id: 'passport_number',
    field: 'passportNumber',
    P1: true,
    P2: true,
    P3: true,
    P4: true,
    R1: true,
    R2: true,
  },
  {
    id: 'date_of_birth',
    field: 'dateOfBirth',
    P1: true,
    P2: false,
    P3: true,
    P4: false,
    R1: false,
    R2: false,
  },
  {
    id: 'mobile_phone_number',
    field: 'mobilePhoneNumber',
    P1: true,
    P2: true,
    P3: true,
    P4: true,
    R1: true,
    R2: true,
  },
  {
    id: 'email_address',
    field: 'emailAddress',
    P1: false,
    P2: true,
    P3: false,
    P4: false,
    R1: true,
    R2: true,
  },
  {
    id: 'personal_phone_number',
    field: 'personalPhoneNumber',
    P1: true,
    P2: true,
    P3: true,
    P4: true,
    R1: true,
    R2: true,
  },
]

const exclusivesMap = new Map<string, string[]>([
  ['driving_licence_number', ['passport_number', 'agreement_number']],
  ['passport_number', ['driving_licence_number']],
  ['mobile_phone_number', ['personal_phone_number']],
  ['personal_phone_number', ['mobile_phone_number']],
])

export function isExclusiveTo(q1Id: string, q2Id: string): boolean {
  const exclusives = exclusivesMap.get(q1Id)
  if (exclusives === undefined) { return false }
  return exclusives.includes(q2Id)
}

export function getNextQuestionIds(
  lastContractQuestionId: string | null,
  lastCustomerQuestionId: string | null,
  persona: Persona,
  verificationFields: VerificationFields,
) {
  const questions: string[] = []
  let nextCustomerQuestionId = getNextCustomerQuestionId(
    lastCustomerQuestionId,
    null,
    persona,
    verificationFields,
  )
  if (nextCustomerQuestionId !== null) {
    questions.push(nextCustomerQuestionId)
  }
  let nextContractQuestionId = getNextContractQuestionId(
    lastContractQuestionId,
    nextCustomerQuestionId,
    persona,
    verificationFields,
  )
  if (nextContractQuestionId !== null) {
    questions.push(nextContractQuestionId)
  }
  if (questions.length === 1) {
    if (nextCustomerQuestionId === null) {
      nextContractQuestionId = getNextContractQuestionId(
        nextContractQuestionId,
        nextContractQuestionId,
        persona,
        verificationFields,
      )
      if (nextContractQuestionId !== null) {
        questions.push(nextContractQuestionId)
      }
    } else if (nextContractQuestionId === null) {
      nextCustomerQuestionId = getNextCustomerQuestionId(
        nextCustomerQuestionId,
        nextCustomerQuestionId,
        persona,
        verificationFields,
      )
      if (nextCustomerQuestionId !== null) {
        questions.push(nextCustomerQuestionId)
      }
    }
  }
  return { questions, nextCustomerQuestionId, nextContractQuestionId }
}

function getNextContractQuestionId(
  lastQuestionId: string | null,
  otherQuestionId: string | null,
  persona: Persona,
  verificationFields: VerificationFields,
): string | null {
  let canCheck = lastQuestionId === null
  for (let i = 0; i < contractQuestionsConfig.length; i += 1) {
    const question = contractQuestionsConfig[i]!
    if (canCheck) {
      if (question[persona] && verificationFields[question.field] !== null) {
        if (otherQuestionId === null || !isExclusiveTo(question.id, otherQuestionId)) {
          return question.id
        }
      }
    } else if (question.id === lastQuestionId) {
      canCheck = true
    }
  }
  return null
}

function getNextCustomerQuestionId(
  lastQuestionId: string | null,
  otherQuestionId: string | null,
  persona: Persona,
  verificationFields: VerificationFields,
): string | null {
  let canCheck = lastQuestionId === null
  for (let i = 0; i < customerQuestionsConfig.length; i += 1) {
    const question = customerQuestionsConfig[i]!
    if (canCheck) {
      if (question[persona] && verificationFields[question.field] !== null) {
        if (otherQuestionId === null || !isExclusiveTo(question.id, otherQuestionId)) {
          return question.id
        }
      }
    } else if (question.id === lastQuestionId) {
      canCheck = true
    }
  }
  return null
}

export function getQuestionFieldById(questionId: string) {
  let question = contractQuestionsConfig.find(q => q.id === questionId)
  if (question === undefined) {
    question = customerQuestionsConfig.find(q => q.id === questionId)
  }
  return question!.field
}
