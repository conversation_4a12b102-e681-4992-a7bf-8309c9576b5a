import * as restate from '@restatedev/restate-sdk'
import { createVirtualObject } from './util.ts'
import { customerLockList } from './customerLockList.ts'

type CustomerLockState = {
  locked: boolean
}

// Key: Customer ID
export const customerLock = createVirtualObject({
  name: 'customer-lock',
  handlers: {
    acquire: async (ctx: restate.ObjectContext<CustomerLockState>) => {
      if (await ctx.get('locked') === true) {
        return false
      }
      ctx.set('locked', true)
      ctx.objectClient(customerLockList, '*').lock(ctx.key)
      ctx.objectSendClient(customerLock, ctx.key).clear(restate.SendOpts.from({ delay: { minutes: 10 } }))
      return true
    },
    isLocked: async (ctx: restate.ObjectContext<CustomerLockState>) => {
      const locked = await ctx.get('locked')
      return locked === true
    },
    clear: restate.handlers.object.exclusive(
      {
        enableLazyState: true,
        journalRetention: 0,
      },
      async (ctx: restate.ObjectContext<CustomerLockState>) => {
        ctx.clearAll()
        ctx.objectClient(customerLockList, '*').unlock(ctx.key)
      },
    ),
  },
  options: {
    ingressPrivate: true,
  },
})

export type CustomerLock = typeof customerLock
