import * as restate from '@restatedev/restate-sdk'
import * as z from 'zod/mini'
import { createVirtualObject } from './util.ts'
import dataServices from '../dataServices.ts'

const decisionSchema = z.discriminatedUnion('value', [
  z.object({ value: z.literal('accept') }),
  z.object({
    value: z.literal('decline'),
    reason: z.string(),
  }),
])

type Decision = z.infer<typeof decisionSchema>

const decisionInputSchema = z.object({
  contractId: z.string(),
  decision: decisionSchema,
})

export type DecisionInput = z.infer<typeof decisionInputSchema>

type Contract = {
  id: string
  eligible: boolean
  model: string
  registration: string
  paidActual: number
  paidExpected: number
  aprActual: number
  aprExpected: number
  refund: number
  interest: number
  total: number
  decision?: Decision
}

function mapContractDataToState(data: Awaited<ReturnType<typeof dataServices.scuk.queries.getContracts>>[number]): Contract {
  return {
    id: data.contractId,
    eligible: data.eligible,
    model: data.vehicleModel,
    registration: data.vehicleRegistrationNumber,
    paidActual: 10000,
    paidExpected: 9500,
    aprActual: 9.23,
    aprExpected: 7,
    refund: 500,
    interest: 32,
    total: 532,
  }
}

function mapContractToSummary(contract: Contract) {
  let status
  if (!contract.eligible) {
    status = null
  } else if (contract.decision === undefined) {
    status = 'ready'
  } else if (contract.decision.value === 'accept') {
    status = 'accepted'
  } else {
    status = 'declined'
  }
  return {
    agreementNumber: contract.id,
    eligible: contract.eligible,
    vehicleModel: contract.model,
    vehicleRegistrationNumber: contract.registration,
    status,
  }
}

export type ContractSummary = ReturnType<typeof mapContractToSummary>

function mapContractToOffer(contract: Contract) {
  return {
    paidActual: contract.paidActual,
    paidExpected: contract.paidExpected,
    aprActual: contract.aprActual,
    aprExpected: contract.aprExpected,
    refund: contract.refund,
    interest: contract.interest,
    total: contract.total,
    decision: contract.decision,
  }
}

export type ContractOffer = ReturnType<typeof mapContractToOffer>

export type ConfirmOffersOutput = 'accepted' | 'declined' | 'mix' | 'ineligible' | 'invalid'

type State = {
  contracts: Contract[]
}

export const claimSessionContracts = createVirtualObject({
  name: 'claim-session-contracts',
  handlers: {
    getSummary: async (ctx: restate.ObjectContext<State>, input: { customerIds: string[] }): Promise<ContractSummary[]> => {
      const cache = await ctx.get('contracts')
      if (cache !== null) {
        return cache.map(mapContractToSummary)
      }
      const contracts = await ctx.run('Get contracts', async () => {
        const result = await dataServices.scuk.queries.getContracts(input.customerIds)
        return result.map(mapContractDataToState)
      })
      ctx.set('contracts', contracts)
      return contracts.map(mapContractToSummary)
    },
    getOffer: async (ctx: restate.ObjectContext<State>, input: { contractId: string }): Promise<ContractOffer> => {
      const cache = await ctx.get('contracts')
      if (cache === null) {
        throw new restate.TerminalError('No contracts when getting offer', { errorCode: 404 })
      }
      const existingContract = cache.find(c => c.id === input.contractId)
      if (existingContract === undefined || !existingContract.eligible) {
        throw new restate.TerminalError('Contract not found', { errorCode: 404 })
      }
      return mapContractToOffer(existingContract)
    },
    submitDecision: async (ctx: restate.ObjectContext<State>, rawInput: DecisionInput): Promise<void> => {
      const parsedInput = decisionInputSchema.safeParse(rawInput)
      if (!parsedInput.success) {
        throw new restate.TerminalError('Invalid input', { errorCode: 400 })
      }
      const input = parsedInput.data
      const cache = await ctx.get('contracts')
      if (cache === null) {
        throw new restate.TerminalError('No contracts when getting offer', { errorCode: 404 })
      }
      const existingContract = cache.find(c => c.id === input.contractId)
      if (existingContract === undefined || !existingContract.eligible) {
        throw new restate.TerminalError('Contract not found', { errorCode: 404 })
      }
      existingContract.decision = input.decision
      ctx.set('contracts', cache)
    },
    confirmOffers: async (ctx: restate.ObjectContext<State>): Promise<ConfirmOffersOutput> => {
      const cache = await ctx.get('contracts')
      if (cache === null) {
        throw new restate.TerminalError('No contracts when getting offer', { errorCode: 404 })
      }
      let hasAccepted = false
      let hasDeclined = false
      for (const contract of cache) {
        if (contract.eligible) {
          if (contract.decision === undefined) {
            return 'invalid'
          }
          if (contract.decision.value === 'accept') {
            hasAccepted = true
          } else {
            hasDeclined = true
          }
        }
      }
      if (hasAccepted) { return hasDeclined ? 'mix' : 'accepted' }
      if (hasDeclined) { return 'declined' }
      return 'ineligible'
    },
    clear: restate.handlers.object.exclusive(
      {
        enableLazyState: true,
        journalRetention: 0,
      },
      async (ctx: restate.ObjectContext<State>) => {
        ctx.clearAll()
      },
    ),
  },
  options: {
    ingressPrivate: true,
  },
})
