import * as restate from '@restatedev/restate-sdk'
import * as z from 'zod/mini'
import { createVirtualObject } from './util.ts'
import dataServices from '../dataServices.ts'
import { claimSessionAudit } from './claimSessionAudit.ts'
import { claimSessionContracts } from './claimSessionContracts.ts'
import type { DecisionInput as OfferDecisionInput, ContractSummary, ContractOffer, ConfirmOffersOutput } from './claimSessionContracts.ts'
import { claimSessionVerification } from './claimSessionVerification.ts'
import type { SubmitAnswersInput as VerificationAnswersInput, SubmitAnswersOutput as VerificationAnswersOutput } from './claimSessionVerification.ts'
import { customerLock } from './customerLock.ts'
import { validateBankDetails } from '../services/bankModulus/bankModulus.ts'

const clearSessionDelay: restate.Duration = { days: 1 }

const startWithIdInputSchema = z.object({
  outreachId: z.string(),
})

type StartWithIdInput = z.infer<typeof startWithIdInputSchema>

const startWithDetailsInputSchema = z.object({
  forename: z.string(),
  surname: z.string(),
  dateOfBirth: z.string(),
})

type StartWithDetailsInput = z.infer<typeof startWithDetailsInputSchema>

const matchInputSchema = z.object({
  postcode: z.string(),
})

type MatchInput = z.infer<typeof matchInputSchema>

type VerificationQuestionsOutput = {
  customerName: string
  questions: string[]
}

const contactDetailsSchema = z.object({
  addressLineOne: z.string(),
  addressLineTwo: z.optional(z.string()),
  addressLineThree: z.optional(z.string()),
  addressPostcode: z.string(),
  emailAddress: z.string(),
  forename: z.string(),
  mobilePhoneNumber: z.string(),
  surname: z.string(),
})

export type ContactDetails = z.infer<typeof contactDetailsSchema>

type GetContactDetailsOutput = {
  submitted: boolean
  details: ContactDetails
}

const paymentDetailsSchema = z.union([
  z.object({
    useExisting: z.literal(true),
  }),
  z.object({
    useExisting: z.literal(false),
    payee: z.string(),
    accountNumber: z.string(),
    sortCode: z.string(),
  }),
])

type PaymentDetails = z.infer<typeof paymentDetailsSchema>

type GetPaymentDetailsOutput = {
  accountNumber: string | null
  paymentDetails?: PaymentDetails
}

type SubmitPaymentDetailsOutput = {
  valid: boolean
}

type Step = 'match' | 'verify' | 'contact-details' | 'contracts' | 'payment' | 'complete' | 'exit'

type VoidResult = Promise<{ step?: Step }>

type Result<T> = Promise<{
  step?: undefined
  result: T
} | {
  step: Step
}>

type ClaimSessionState = {
  customerIds: string[]
  customerName: string
  isProactive: boolean
  latestContractId: string
  contactDetails: ContactDetails
  paymentDetails: {
    hasExisting: boolean
    details?: PaymentDetails
  }
  step: Step
  result: Exclude<ConfirmOffersOutput, 'invalid'> | 'retry'
}

export const claimSession = createVirtualObject({
  name: 'claim-session',
  handlers: {
    startWithId: restate.handlers.object.exclusive(
      async (ctx: restate.ObjectContext<ClaimSessionState>, rawInput: StartWithIdInput): Promise<void> => {
        const parsedInput = startWithIdInputSchema.safeParse(rawInput)
        if (!parsedInput.success) {
          throw new restate.TerminalError('Invalid input', { errorCode: 400 })
        }
        const input = parsedInput.data
        const customerIds = await ctx.run(
          'Get customer IDs for outreach ID',
          () => dataServices.scuk.queries.getCustomerIdsByOutreachId(input.outreachId),
          { maxRetryAttempts: 4 },
        )
        if (customerIds.length === 0) {
          throw new restate.TerminalError('No customers found for outreach ID', { errorCode: 404 })
        }
        ctx.objectClient(claimSessionAudit, ctx.key).log({
          type: 'origination',
          data: {
            isProactive: true,
            outreachId: input.outreachId,
            customerIds,
          },
        })
        const latestContractMeta = await ctx.run(
          'Get latest contract meta',
          () => dataServices.scuk.queries.getLatestContractMeta(customerIds),
          { maxRetryAttempts: 4 },
        )
        if (latestContractMeta === null) {
          throw new restate.TerminalError('No contract found for customer', { errorCode: 404 })
        }
        ctx.set('customerIds', customerIds)
        ctx.set('customerName', latestContractMeta.customerName)
        ctx.set('isProactive', true)
        ctx.set('latestContractId', latestContractMeta.contractId)
        ctx.set('step', 'verify')
        ctx.objectSendClient(claimSession, ctx.key).clear(restate.SendOpts.from({ delay: clearSessionDelay }))
      },
    ),
    startWithDetails: restate.handlers.object.exclusive(
      async (ctx: restate.ObjectContext<ClaimSessionState>, rawInput: StartWithDetailsInput): Promise<void> => {
        const parsedInput = startWithDetailsInputSchema.safeParse(rawInput)
        if (!parsedInput.success) {
          throw new restate.TerminalError('Invalid input', { errorCode: 400 })
        }
        const input = parsedInput.data
        const customerIds = await ctx.run(
          'Get customer IDs for details',
          () => dataServices.scuk.queries.getCustomerIdsByDetails(input.forename, input.surname, input.dateOfBirth),
          { maxRetryAttempts: 4 },
        )
        if (customerIds.length === 0) {
          throw new restate.TerminalError('No customers found for details', { errorCode: 404 })
        }
        ctx.objectClient(claimSessionAudit, ctx.key).log({
          type: 'origination',
          data: {
            isProactive: false,
            customerIds,
          },
        })
        ctx.set('customerIds', customerIds)
        ctx.set('isProactive', false)
        ctx.set('step', 'match')
        ctx.objectSendClient(claimSession, ctx.key).clear(restate.SendOpts.from({ delay: clearSessionDelay }))
      },
    ),
    getStep,
    getMatchState: async (ctx: restate.ObjectContext<ClaimSessionState>): VoidResult => {
      const step = await getStep(ctx)
      if (step !== 'match') {
        return { step }
      }
      const customerIds = (await ctx.get('customerIds'))!
      if (customerIds.length > 1) {
        const customers = await ctx.run(
          'Get customers for match',
          () => dataServices.scuk.queries.getCustomersForMatch(customerIds),
          { maxRetryAttempts: 4 },
        )
        if (customers.length !== customerIds.length) {
          throw new restate.TerminalError('Customer records missing', { errorCode: 409 })
        }

        const first = customers.shift()!
        let drivingLicenceMatch = true
        let postcodeMatch = true
        let mobileNumberMatch = true
        for (const customer of customers) {
          if (customer.drivingLicenceNumber === null || customer.drivingLicenceNumber !== first.drivingLicenceNumber) {
            drivingLicenceMatch = false
          }
          if (customer.postcode === null || customer.postcode !== first.postcode) {
            postcodeMatch = false
          }
          if (customer.mobilePhoneNumber === null || customer.mobilePhoneNumber !== first.mobilePhoneNumber) {
            mobileNumberMatch = false
          }
        }
        if (!drivingLicenceMatch && !postcodeMatch && !mobileNumberMatch) {
          return {}
        }
      }
      const latestContractMeta = await ctx.run(
        'Get latest contract meta',
        () => dataServices.scuk.queries.getLatestContractMeta(customerIds),
        { maxRetryAttempts: 4 },
      )
      if (latestContractMeta === null) {
        throw new restate.TerminalError('No contract found for customer', { errorCode: 409 })
      }
      ctx.set('customerName', latestContractMeta.customerName)
      ctx.set('latestContractId', latestContractMeta.contractId)
      ctx.set('step', 'verify')
      return { step: 'verify' }
    },
    processMatchAnswer: async (ctx: restate.ObjectContext<ClaimSessionState>, rawInput: MatchInput): VoidResult => {
      const parsedInput = matchInputSchema.safeParse(rawInput)
      if (!parsedInput.success) {
        throw new restate.TerminalError('Invalid input', { errorCode: 400 })
      }
      const input = parsedInput.data
      const step = await getStep(ctx)
      if (step !== 'match') {
        return { step }
      }
      const currentCustomerIds = (await ctx.get('customerIds'))!
      const nextCustomerIds = await ctx.run(
        'Get customers for match result',
        () => dataServices.scuk.queries.getCustomersForMatchResult(currentCustomerIds, input.postcode),
        { maxRetryAttempts: 4 },
      )
      if (nextCustomerIds.length === 0) {
        throw new restate.TerminalError('No customers found for match answer', { errorCode: 404 })
      }
      const latestContractMeta = await ctx.run(
        'Get latest contract meta',
        () => dataServices.scuk.queries.getLatestContractMeta(nextCustomerIds),
        { maxRetryAttempts: 4 },
      )
      if (latestContractMeta === null) {
        throw new restate.TerminalError('No contract found for customer', { errorCode: 409 })
      }
      ctx.set('customerName', latestContractMeta.customerName)
      ctx.set('latestContractId', latestContractMeta.contractId)
      ctx.set('customerIds', nextCustomerIds)
      ctx.set('step', 'verify')
      return {}
    },
    getVerificationQuestions: async (ctx: restate.ObjectContext<ClaimSessionState>): Result<VerificationQuestionsOutput> => {
      const step = await getStep(ctx)
      if (step !== 'verify') {
        return { step }
      }

      const customerName = (await ctx.get('customerName'))!
      const result = await ctx.objectClient(claimSessionVerification, ctx.key).getQuestions({
        isProactive: (await ctx.get('isProactive'))!,
        latestContractId: (await ctx.get('latestContractId'))!,
      })
      return { result: { customerName, questions: result.questions } }
    },
    submitVerificationAnswers: async (
      ctx: restate.ObjectContext<ClaimSessionState>, rawInput: VerificationAnswersInput,
    ): Result<VerificationAnswersOutput> => {
      const step = await getStep(ctx)
      if (step !== 'verify') {
        return { step }
      }

      const result = await ctx.objectClient(claimSessionVerification, ctx.key).submitAnswers(rawInput)
      if (result.success) {
        const areLocked = await areCustomersLocked(ctx)
        if (areLocked) {
          ctx.set('step', 'complete')
          ctx.set('result', 'retry')
        } else {
          ctx.set('step', 'contact-details')
        }
      } else if (result.fail) {
        ctx.set('step', 'exit')
      }
      return { result }
    },
    getContactDetails: async (ctx: restate.ObjectContext<ClaimSessionState>): Result<GetContactDetailsOutput> => {
      const step = await getStep(ctx)
      if (step !== 'contact-details') {
        return { step }
      }
      let contactDetails = await ctx.get('contactDetails')
      let submitted = true
      if (contactDetails === null) {
        const latestContractId = (await ctx.get('latestContractId'))!
        contactDetails = await ctx.run(
          'Get contact details',
          () => dataServices.scuk.queries.getContactDetails(latestContractId),
          { maxRetryAttempts: 4 },
        )
        submitted = false
      }
      return { result: { submitted, details: contactDetails } }
    },
    submitContactDetails: async (ctx: restate.ObjectContext<ClaimSessionState>, rawInput: ContactDetails): VoidResult => {
      const step = await getStep(ctx)
      if (step !== 'contact-details') {
        return { step }
      }
      const parsedInput = contactDetailsSchema.safeParse(rawInput)
      if (!parsedInput.success) {
        throw new restate.TerminalError('Invalid contact details', { errorCode: 400 })
      }
      ctx.set('contactDetails', parsedInput.data)
      return {}
    },
    confirmContactDetails: async (ctx: restate.ObjectContext<ClaimSessionState>): VoidResult => {
      const step = await getStep(ctx)
      if (step !== 'contact-details') {
        return { step }
      }
      const contactDetails = await ctx.get('contactDetails')
      if (contactDetails === null) {
        throw new restate.TerminalError('Contact details not set', { errorCode: 400 })
      }
      ctx.set('step', 'contracts')
      return {}
    },
    getContractsSummary: async (ctx: restate.ObjectContext<ClaimSessionState>): Result<ContractSummary[]> => {
      const step = await getStep(ctx)
      if (step !== 'contracts') {
        return { step }
      }
      const customerIds = (await ctx.get('customerIds'))!
      const result = await ctx.objectClient(claimSessionContracts, ctx.key).getSummary({ customerIds })
      return { result }
    },
    getContractOffer: async (
      ctx: restate.ObjectContext<ClaimSessionState>, rawInput: { contractId: string },
    ): Result<ContractOffer> => {
      const step = await getStep(ctx)
      if (step !== 'contracts') {
        return { step }
      }
      const result = await ctx.objectClient(claimSessionContracts, ctx.key).getOffer(rawInput)
      return { result }
    },
    submitOfferDecision: async (ctx: restate.ObjectContext<ClaimSessionState>, rawInput: OfferDecisionInput): VoidResult => {
      const step = await getStep(ctx)
      if (step !== 'contracts') {
        return { step }
      }
      await ctx.objectClient(claimSessionContracts, ctx.key).submitDecision(rawInput)
      return {}
    },
    confirmOffers: async (ctx: restate.ObjectContext<ClaimSessionState>): Result<Exclude<ConfirmOffersOutput, 'invalid'>> => {
      const step = await getStep(ctx)
      if (step !== 'contracts') {
        return { step }
      }
      const result = await ctx.objectClient(claimSessionContracts, ctx.key).confirmOffers()
      switch (result) {
        case 'invalid':
          throw new restate.TerminalError('Invalid offer decisions', { errorCode: 400 })
        case 'accepted':
        case 'mix':
          ctx.set('step', 'payment')
          break
        case 'declined':
        case 'ineligible':
          await lockCustomers(ctx)
          ctx.set('step', 'complete')
          break
      }
      ctx.set('result', result)
      return { result }
    },
    getPaymentDetails: async (ctx: restate.ObjectContext<ClaimSessionState>): Result<GetPaymentDetailsOutput> => {
      const step = await getStep(ctx)
      if (step !== 'payment') {
        return { step }
      }
      const paymentDetails = await ctx.get('paymentDetails') ?? undefined
      if (paymentDetails !== undefined && !paymentDetails.hasExisting) {
        return { result: { accountNumber: null, paymentDetails: paymentDetails.details } }
      }
      const latestContractId = (await ctx.get('latestContractId'))!
      const accountNumber = await ctx.run(
        'Get account number',
        async () => {
          const result = await dataServices.scuk.queries.getAccountNumber(latestContractId)
          if (result === null || result.length < 4) {
            return null
          }
          return result.slice(-4)
        },
        { maxRetryAttempts: 4 },
      )
      if (paymentDetails === undefined) {
        ctx.set('paymentDetails', { hasExisting: accountNumber !== null })
      }
      return { result: { accountNumber, paymentDetails: paymentDetails?.details } }
    },
    submitPaymentDetails: async (
      ctx: restate.ObjectContext<ClaimSessionState>, rawInput: PaymentDetails,
    ): Result<SubmitPaymentDetailsOutput> => {
      const step = await getStep(ctx)
      if (step !== 'payment') {
        return { step }
      }
      const parsedInput = paymentDetailsSchema.safeParse(rawInput)
      if (!parsedInput.success) {
        throw new restate.TerminalError('Invalid payment details', { errorCode: 400 })
      }
      const paymentDetails = await ctx.get('paymentDetails')
      if (paymentDetails === null) {
        throw new restate.TerminalError('Payment details not set', { errorCode: 400 })
      }
      const input = parsedInput.data
      if (!paymentDetails.hasExisting && input.useExisting) {
        throw new restate.TerminalError('Cannot use non-existant payment details', { errorCode: 400 })
      }
      if (!input.useExisting && !validateBankDetails(input.sortCode, input.accountNumber)) {
        return { result: { valid: false } }
      }
      ctx.set('paymentDetails', {
        hasExisting: paymentDetails.hasExisting,
        details: input,
      })
      if (input.useExisting) {
        await lockCustomers(ctx)
        ctx.set('step', 'complete')
      }
      return { result: { valid: true } }
    },
    confirmPaymentDetails: async (ctx: restate.ObjectContext<ClaimSessionState>): VoidResult => {
      const step = await getStep(ctx)
      if (step !== 'payment') {
        return { step }
      }
      const paymentDetails = await ctx.get('paymentDetails')
      if (paymentDetails === null) {
        throw new restate.TerminalError('Payment details not set', { errorCode: 400 })
      }
      await lockCustomers(ctx)
      ctx.set('step', 'complete')
      return {}
    },
    getResult: async (ctx: restate.ObjectContext<ClaimSessionState>) => {
      const step = await getStep(ctx)
      if (step !== 'complete') {
        return { step }
      }
      const result = await ctx.get('result')
      if (result === null) {
        throw new restate.TerminalError('Result not set', { errorCode: 409 })
      }
      return { result }
    },
    clear: restate.handlers.object.exclusive(
      {
        enableLazyState: true,
        ingressPrivate: true,
        journalRetention: 0,
      },
      async (ctx: restate.ObjectContext<ClaimSessionState>): Promise<void> => {
        await ctx.objectClient(claimSessionAudit, ctx.key).clear()
        await ctx.objectClient(claimSessionContracts, ctx.key).clear()
        await ctx.objectClient(claimSessionVerification, ctx.key).clear()
        ctx.clearAll()
      },
    ),
  },
})

async function getStep(ctx: restate.ObjectContext<ClaimSessionState>): Promise<Step> {
  const step = await ctx.get('step')
  if (step === null) {
    throw new restate.TerminalError('Session does not exist', { errorCode: 401 })
  }
  return step
}

async function areCustomersLocked(ctx: restate.ObjectContext<ClaimSessionState>): Promise<boolean> {
  const customerIds = (await ctx.get('customerIds'))!
  for (const customerId of customerIds) {
    if (await ctx.objectClient(customerLock, customerId).isLocked()) {
      return true
    }
  }
  return false
}

async function lockCustomers(ctx: restate.ObjectContext<ClaimSessionState>): Promise<boolean> {
  const customerIds = (await ctx.get('customerIds'))!
  for (const customerId of customerIds) {
    if (!await ctx.objectClient(customerLock, customerId).acquire()) {
      return false
    }
  }
  return true
}

export type ClaimSession = typeof claimSession
