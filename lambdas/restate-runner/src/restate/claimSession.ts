import * as restate from '@restatedev/restate-sdk'
import * as z from 'zod/v4-mini'
import logger from '../logger.ts'
import { createVirtualObject } from './util.ts'
import dataServices from '../dataServices.ts'
import { claimSessionAudit } from './claimSessionAudit.ts'
import { getNextQuestionIds, getQuestionFieldById } from './claimSession/verification.ts'
import type { Persona, VerificationFields } from './claimSession/verification.ts'

const clearSessionDelay = 1000 * 60 * 60 * 24 // 24 hours

const startWithIdInputSchema = z.object({
  outreachId: z.string(),
})

type StartWithIdInput = z.infer<typeof startWithIdInputSchema>

const startWithDetailsInputSchema = z.object({
  forename: z.string(),
  surname: z.string(),
  dateOfBirth: z.string(),
})

type StartWithDetailsInput = z.infer<typeof startWithDetailsInputSchema>

const matchInputSchema = z.object({
  postcode: z.string(),
})

type MatchInput = z.infer<typeof matchInputSchema>

const verificationAnswerSchema = z.union([
  z.number(),
  z.string(),
])

const verificationAnswersSchema = z.union([
  z.object({
    skip: z.literal(true),
  }),
  z.object({
    skip: z.optional(z.undefined()),
    answers: z.record(z.string(), verificationAnswerSchema),
  }),
])

type VerificationAnswersInput = z.infer<typeof verificationAnswersSchema>

type ClaimSessionState = {
  customerIds: string[]
  status: 'match' | 'verify' | 'contact-details' | 'complete' | 'exit'
  verificationAttempts: number
  verificationFields: VerificationFields
  verificationPersona: Persona
  verificationQuestionIds: string[]
  verificationLastContractQuestionId: string
  verificationLastCustomerQuestionId: string
}

export const claimSession = createVirtualObject({
  name: 'claim-session',
  handlers: {
    startWithId: restate.handlers.object.exclusive(
      async (ctx: restate.ObjectContext<ClaimSessionState>, rawInput: StartWithIdInput) => {
        const parsedInput = startWithIdInputSchema.safeParse(rawInput)
        if (!parsedInput.success) {
          throw new restate.TerminalError('Invalid input', { errorCode: 400 })
        }
        const input = parsedInput.data
        const customerIds = await ctx.run(
          'Get customer IDs for outreach ID',
          () => dataServices.scuk.queries.getCustomerIdsByOutreachId(input.outreachId),
          { maxRetryAttempts: 4 },
        )
        if (customerIds.length === 0) {
          throw new restate.TerminalError('No customers found for outreach ID', { errorCode: 404 })
        }
        ctx.objectClient(claimSessionAudit, ctx.key).log({
          type: 'origination',
          data: {
            type: 'outreach',
            outreachId: input.outreachId,
            customerIds,
          },
        })
        ctx.set('customerIds', customerIds)
        ctx.set('status', 'verify')
        ctx.objectSendClient(claimSession, ctx.key).clear(restate.SendOpts.from({ delay: clearSessionDelay }))
      },
    ),
    startWithDetails: restate.handlers.object.exclusive(
      async (ctx: restate.ObjectContext<ClaimSessionState>, rawInput: StartWithDetailsInput) => {
        const parsedInput = startWithDetailsInputSchema.safeParse(rawInput)
        if (!parsedInput.success) {
          throw new restate.TerminalError('Invalid input', { errorCode: 400 })
        }
        const input = parsedInput.data
        const customerIds = await ctx.run(
          'Get customer IDs for details',
          () => dataServices.scuk.queries.getCustomerIdsByDetails(input.forename, input.surname, input.dateOfBirth),
          { maxRetryAttempts: 4 },
        )
        if (customerIds.length === 0) {
          throw new restate.TerminalError('No customers found for details', { errorCode: 404 })
        }
        ctx.objectClient(claimSessionAudit, ctx.key).log({
          type: 'origination',
          data: {
            type: 'anonymous',
            customerIds,
          },
        })
        ctx.set('customerIds', customerIds)
        ctx.set('status', 'match')
        ctx.objectSendClient(claimSession, ctx.key).clear(restate.SendOpts.from({ delay: clearSessionDelay }))
      },
    ),
    getStatus: async (ctx: restate.ObjectContext<ClaimSessionState>) => {
      const status = await ctx.get('status')
      if (status === null) {
        throw new restate.TerminalError('Session does not exist', { errorCode: 404 })
      }
      return status
    },
    getMatchState: async (ctx: restate.ObjectContext<ClaimSessionState>) => {
      const status = await ctx.get('status')
      if (status === 'verify') {
        return { status: 'verify' }
      }
      if (status !== 'match') {
        return { status: 'unknown' }
      }
      const customerIds = (await ctx.get('customerIds'))!
      if (customerIds.length === 1) {
        ctx.set('status', 'verify')
        return { status: 'verify' }
      }
      const customers = await ctx.run(
        'Get customers for match',
        () => dataServices.scuk.queries.getCustomersForMatch(customerIds),
        { maxRetryAttempts: 4 },
      )
      if (customers.length !== customerIds.length) {
        throw new restate.TerminalError('Customer records missing', { errorCode: 409 })
      }

      const firstCustomer = customers.shift()!
      let drivingLicenceMatch = true
      let postcodeMatch = true
      let mobileNumberMatch = true
      for (const customer of customers) {
        if (customer.drivingLicenceNumber === null || customer.drivingLicenceNumber !== firstCustomer.drivingLicenceNumber) {
          drivingLicenceMatch = false
        }
        if (customer.postcode === null || customer.postcode !== firstCustomer.postcode) {
          postcodeMatch = false
        }
        if (customer.mobilePhoneNumber === null || customer.mobilePhoneNumber !== firstCustomer.mobilePhoneNumber) {
          mobileNumberMatch = false
        }
      }
      if (drivingLicenceMatch || postcodeMatch || mobileNumberMatch) {
        ctx.set('status', 'verify')
        return { status: 'verify' }
      }
      return { status: 'match' }
    },
    processMatchAnswer: async (ctx: restate.ObjectContext<ClaimSessionState>, rawInput: MatchInput) => {
      const parsedInput = matchInputSchema.safeParse(rawInput)
      if (!parsedInput.success) {
        throw new restate.TerminalError('Invalid input', { errorCode: 400 })
      }
      const input = parsedInput.data
      const status = await ctx.get('status')
      if (status !== 'match') {
        throw new restate.TerminalError('Session is not in match state', { errorCode: 409 })
      }
      const currentCustomerIds = (await ctx.get('customerIds'))!
      const nextCustomerIds = await ctx.run(
        'Get customers for match result',
        () => dataServices.scuk.queries.getCustomersForMatchResult(currentCustomerIds, input.postcode),
        { maxRetryAttempts: 4 },
      )
      if (nextCustomerIds.length === 0) {
        throw new restate.TerminalError('No customers found for match answer', { errorCode: 404 })
      }
      ctx.set('customerIds', nextCustomerIds)
      ctx.set('status', 'verify')
    },
    getVerificationQuestions: async (ctx: restate.ObjectContext<ClaimSessionState>) => {
      const status = await ctx.get('status')
      if (status !== 'verify') {
        throw new restate.TerminalError('Session is not in verify state', { errorCode: 409 })
      }

      const verificationQuestionIds = await ctx.get('verificationQuestionIds')
      if (verificationQuestionIds !== null) {
        return { questions: verificationQuestionIds }
      }

      let verificationFields = await ctx.get('verificationFields')
      let persona
      if (verificationFields === null) {
        const customerIds = (await ctx.get('customerIds'))!
        const verificationData = await ctx.run(
          'Get verification data',
          async () => {
            const result = await dataServices.scuk.queries.getVerificationFields(customerIds)
            if (result === null) { return null }
            const startDate = new Date(result.startDate)
            const isAgedLoan = startDate.getUTCFullYear() < 2019
            let personaResult: Persona
            if (result.isProactive) {
              if (isAgedLoan) {
                if (result.hasComplaint) {
                  personaResult = 'P4'
                } else {
                  personaResult = 'P3'
                }
              } else if (result.hasComplaint) {
                personaResult = 'P2'
              } else {
                personaResult = 'P1'
              }
            } else if (isAgedLoan) {
              personaResult = 'R2'
            } else {
              personaResult = 'R1'
            }
            return {
              persona: personaResult,
              fields: {
                contractId: result.contractId,
                accountNumber: result.accountNumber,
                monthlyPaymentAmount: result.monthlyPaymentAmount,
                sortCode: result.sortCode,
                vehicleRegistrationNumber: result.vehicleRegistrationNumber,
                dateOfBirth: result.dateOfBirth,
                drivingLicenceNumber: result.drivingLicenceNumber,
                emailAddress: result.emailAddress,
                mobilePhoneNumber: result.mobilePhoneNumber,
                passportNumber: result.passportNumber,
                personalPhoneNumber: result.personalPhoneNumber,
              },
            }
          },
          { maxRetryAttempts: 4 },
        )
        if (verificationData === null) {
          throw new restate.TerminalError('No verification data found for customers', { errorCode: 404 })
        }
        persona = verificationData.persona
        ctx.set('verificationPersona', verificationData.persona)
        verificationFields = verificationData.fields
        ctx.set('verificationFields', verificationFields)
      } else {
        persona = (await ctx.get('verificationPersona'))!
      }

      const {
        questions,
        nextContractQuestionId,
        nextCustomerQuestionId,
      } = getNextQuestionIds(null, null, persona, verificationFields)

      ctx.set('verificationQuestionIds', questions)
      if (nextContractQuestionId !== null) {
        ctx.set('verificationLastContractQuestionId', nextContractQuestionId)
      }
      if (nextCustomerQuestionId !== null) {
        ctx.set('verificationLastCustomerQuestionId', nextCustomerQuestionId)
      }
      return { questions }
    },
    submitVerificationAnswers: async (ctx: restate.ObjectContext<ClaimSessionState>, rawInput: VerificationAnswersInput) => {
      const parsedInput = verificationAnswersSchema.safeParse(rawInput)
      if (!parsedInput.success) {
        throw new restate.TerminalError('Invalid input', { errorCode: 400 })
      }
      const input = parsedInput.data
      const status = await ctx.get('status')
      if (status !== 'verify') {
        throw new restate.TerminalError('Session is not in verify state', { errorCode: 409 })
      }

      const verificationFields = await ctx.get('verificationFields')
      if (verificationFields === null) {
        throw new restate.TerminalError('Verification fields not set', { errorCode: 409 })
      }

      if (input.skip !== true) {
        const verificationQuestionIds = (await ctx.get('verificationQuestionIds'))!
        let answersAreCorrect = true

        for (const questionId of verificationQuestionIds) {
          const submittedAnswer = input.answers[questionId]
          if (submittedAnswer === undefined) {
            logger.warn('Missing answer for question', { questionId })
            answersAreCorrect = false
            break
          }
          const questionField = getQuestionFieldById(questionId)
          if (verificationFields[questionField] !== submittedAnswer) {
            answersAreCorrect = false
            break
          }
        }

        if (answersAreCorrect) {
          ctx.clear('verificationAttempts')
          ctx.clear('verificationFields')
          ctx.clear('verificationPersona')
          ctx.clear('verificationQuestionIds')
          ctx.clear('verificationLastContractQuestionId')
          ctx.clear('verificationLastCustomerQuestionId')
          ctx.set('status', 'contact-details')
          return { success: true }
        }
      }

      let verificationAttempts = (await ctx.get('verificationAttempts')) ?? 0
      verificationAttempts += 1
      if (verificationAttempts < 3) {
        ctx.set('verificationAttempts', verificationAttempts)
        const persona = (await ctx.get('verificationPersona'))!
        const lastContractQuestionId = await ctx.get('verificationLastContractQuestionId')
        const lastCustomerQuestionId = await ctx.get('verificationLastCustomerQuestionId')
        const { questions, nextContractQuestionId, nextCustomerQuestionId } = getNextQuestionIds(
          lastContractQuestionId,
          lastCustomerQuestionId,
          persona,
          verificationFields,
        )

        if (questions.length === 2) {
          ctx.set('verificationQuestionIds', questions)
          if (nextContractQuestionId !== null) {
            ctx.set('verificationLastContractQuestionId', nextContractQuestionId)
          }
          if (nextCustomerQuestionId !== null) {
            ctx.set('verificationLastCustomerQuestionId', nextCustomerQuestionId)
          }
          return { success: false, fail: false }
        }
      }

      ctx.clear('verificationAttempts')
      ctx.clear('verificationFields')
      ctx.clear('verificationPersona')
      ctx.clear('verificationQuestionIds')
      ctx.clear('verificationLastContractQuestionId')
      ctx.clear('verificationLastCustomerQuestionId')
      ctx.set('status', 'exit')
      return { success: false, fail: true }
    },
    clear: async (ctx: restate.ObjectContext<ClaimSessionState>) => {
      await ctx.objectClient(claimSessionAudit, ctx.key).clear()
      ctx.clearAll()
    },
  },
})

export type ClaimSession = typeof claimSession
