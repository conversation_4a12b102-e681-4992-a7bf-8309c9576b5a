import * as restate from '@restatedev/restate-sdk'
import { createVirtualObject } from './util.ts'

type AuditLog = {
  type: 'origination'
  data: Record<string, unknown>
}

type ClaimSessionAuditState = {
  logs: AuditLog[]
}

export const claimSessionAudit = createVirtualObject({
  name: 'claim-session-audit',
  handlers: {
    log: async (ctx: restate.ObjectContext<ClaimSessionAuditState>, input: AuditLog) => {
      const logs = await ctx.get('logs') || []
      logs.push(input)
      ctx.set('logs', logs)
    },
    getLogs: async (ctx: restate.ObjectContext<ClaimSessionAuditState>) => {
      const logs = await ctx.get('logs') || []
      return logs
    },
    clear: restate.handlers.object.exclusive(
      {
        enableLazyState: true,
        journalRetention: 0,
      },
      async (ctx: restate.ObjectContext<ClaimSessionAuditState>) => {
        ctx.clearAll()
      },
    ),
  },
  options: {
    ingressPrivate: true,
  },
})
