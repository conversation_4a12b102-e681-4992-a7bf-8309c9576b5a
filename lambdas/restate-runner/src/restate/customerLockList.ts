import * as restate from '@restatedev/restate-sdk'
import { createVirtualObject } from './util.ts'
import type { CustomerLock } from './customerLock.ts'

const customerLock: CustomerLock = { name: 'customer-lock' }

type CustomerLockListState = {
  [customerId: string]: true
}

// Singleton
export const customerLockList = createVirtualObject({
  name: 'customer-lock-list',
  handlers: {
    lock: restate.handlers.object.exclusive(
      { enableLazyState: true },
      async (ctx: restate.ObjectContext<CustomerLockListState>, id: string) => {
        ctx.set(id, true)
      },
    ),
    unlock: restate.handlers.object.exclusive(
      { enableLazyState: true },
      async (ctx: restate.ObjectContext<CustomerLockListState>, id: string) => {
        ctx.clear(id)
      },
    ),
    unlockAll: restate.handlers.object.exclusive(
      { journalRetention: 0 },
      async (ctx: restate.ObjectContext<CustomerLockListState>) => {
        const customerIds = await ctx.stateKeys()
        for (const customerId of customerIds) {
          await ctx.objectClient(customerLock, customerId).clear()
        }
      },
    ),
  },
  options: {
    ingressPrivate: true,
  },
})
