import * as restate from '@restatedev/restate-sdk'
import * as z from 'zod/mini'
import { createService } from '../restate/util.ts'
import dataServices from '../dataServices.ts'
import { getRows } from './csv.ts'
import type { CreateCustomerInput, CreateContractInput } from './schema.ts'
import { createCustomerSchema, createContractSchema, csvRowSchema } from './schema.ts'
import { customerLockList } from '../restate/customerLockList.ts'

export const testData = createService({
  name: 'test-data',
  handlers: {
    createCustomer,
    createContract,
    overwriteFromCsv: restate.handlers.handler(
      { input: restate.serde.binary },
      async (ctx: restate.Context, data: Uint8Array) => {
        const records = await getRows(data)
        if (records === null) {
          return { error: 'Invalid CSV format' }
        }
        const parsedFirstRow = csvRowSchema.safeParse(records[0])
        if (!parsedFirstRow.success) {
          return { error: z.prettifyError(parsedFirstRow.error) }
        }
        const parsedInput = z.array(csvRowSchema).safeParse(records)
        if (!parsedInput.success) {
          return { error: z.prettifyError(parsedInput.error) }
        }

        await ctx.run(
          'Clear tables',
          () => dataServices.scuk.queries.clearData(),
          { maxRetryAttempts: 4 },
        )

        await ctx.objectClient(customerLockList, '*').unlockAll()

        const customerPromises: restate.RestatePromise<void>[] = []
        for (let i = 0; i < parsedInput.data.length; i += 1) {
          const row = parsedInput.data[i]!
          const customerId = `customer-${i + 1}`

          customerPromises.push(createCustomer(ctx, { customerId, outreachId: row.outreachId, data: row }))
        }
        await restate.RestatePromise.all(customerPromises)

        const contractPromises = []
        for (let i = 0; i < parsedInput.data.length; i += 1) {
          const row = parsedInput.data[i]!
          const customerId = `customer-${i + 1}`
          contractPromises.push(createContract(ctx, { contractId: row.contractId, customerId, data: row }))
        }
        await restate.RestatePromise.all(contractPromises)
        return { error: null }
      },
    ),
  },
})

export type TestData = typeof testData

function createCustomer(ctx: restate.Context, rawInput: CreateCustomerInput) {
  const parsedInput = createCustomerSchema.safeParse(rawInput)
  if (!parsedInput.success) {
    throw new restate.TerminalError('Invalid input', { errorCode: 400 })
  }
  const input = parsedInput.data
  return ctx.run(
    `Create customer ${input.customerId}`,
    () => dataServices.scuk.queries.upsertCustomer(
      input.customerId,
      input.data,
      input.outreachId,
    ),
    { maxRetryAttempts: 4 },
  )
}

function createContract(ctx: restate.Context, rawInput: CreateContractInput) {
  const parsedInput = createContractSchema.safeParse(rawInput)
  if (!parsedInput.success) {
    throw new restate.TerminalError('Invalid input', { errorCode: 400 })
  }
  const input = parsedInput.data
  return ctx.run(
    `Create contract ${input.contractId}`,
    () => dataServices.scuk.queries.upsertContract(
      input.contractId,
      input.customerId,
      input.data,
    ),
    { maxRetryAttempts: 4 },
  )
}
