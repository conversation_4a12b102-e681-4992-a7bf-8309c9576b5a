import * as z from 'zod/mini'

const numberFormat = new Intl.NumberFormat('en-GB', {
  style: 'decimal',
  minimumFractionDigits: 2,
  maximumFractionDigits: 2,
})

const requiredStringSchema = z.string().check(z.minLength(1))
const optionalStringSchema = z.pipe(
  z.optional(z.nullable(z.string())),
  z.transform(val => val || null),
)
const dateSchema = z.string().check(
  z.regex(/^\d{4}-\d{2}-\d{2}$/, { error: 'Date must be in YYYY-MM-DD format' }),
)

function createBooleanSchema(defaultValue: boolean) {
  // eslint-disable-next-line no-underscore-dangle
  return z._default(z.boolean(), defaultValue)
}

const requiredCsvStringSchema = z.string({ error: 'This field is required' }).check(z.minLength(1, { error: 'This field is required' }))
const optionalCsvStringSchema = z.pipe(
  z.string({ error: 'This field is required' }),
  z.transform(val => (val === '' ? null : val)),
)
const csvDateSchema = z.string({ error: 'This field is required' }).check(
  z.regex(/^\d{2}\/\d{2}\/\d{4}$/, { error: 'Date must be in DD/MM/YYYY format' }),
  z.overwrite((val) => {
    const [day, month, year] = val.split('/')
    return `${year}-${month}-${day}`
  }),
)

function createCsvBooleanSchema(defaultValue: boolean) {
  return z.pipe(
    z.pipe(
      z.string({ error: 'This field is required' }),
      z.literal(['', 'TRUE', 'FALSE'], { error: 'Can only be TRUE or FALSE' }),
    ),
    z.transform(val => (val === '' ? defaultValue : val === 'TRUE')),
  )
}

const customerSchema = z.object({
  title: requiredStringSchema,
  forename: requiredStringSchema,
  surname: requiredStringSchema,
  dateOfBirth: dateSchema,
  mobilePhoneNumber: optionalStringSchema,
  personalPhoneNumber: optionalStringSchema,
  emailAddress: optionalStringSchema,
  address: z.pipe(
    z.optional(z.object({
      lineOne: optionalStringSchema,
      lineTwo: optionalStringSchema,
      lineThree: optionalStringSchema,
      postcode: optionalStringSchema.check(z.overwrite(val => (val === null ? val : val.toUpperCase().replaceAll(' ', '')))),
    })),
    z.transform(val => val ?? {}),
  ),
  drivingLicenceNumber: optionalStringSchema,
  passportNumber: optionalStringSchema,
})

export const createCustomerSchema = z.object({
  customerId: requiredStringSchema,
  outreachId: optionalStringSchema,
  data: customerSchema,
})

export type CreateCustomerInput = z.infer<typeof createCustomerSchema>

const contractSchema = z.object({
  eligible: createBooleanSchema(true),
  startDate: dateSchema,
  hasComplaint: createBooleanSchema(false),
  accountNumber: optionalStringSchema.check(z.refine(val => val === null || /^\d{8}$/.test(val), { error: 'Account number must be 8 digits' })),
  sortCode: optionalStringSchema.check(z.refine(val => val === null || /^\d{6}$/.test(val), { error: 'Sort code must be 6 digits' })),
  monthlyPaymentAmount: optionalStringSchema.check(
    z.refine(val => val === null || /^\d+(\.\d{1,2})?$/.test(val), { error: 'Money value not readable' }),
    z.overwrite(val => (val === null ? val : numberFormat.format(+val))),
  ),
  vehicleModel: optionalStringSchema,
  vehicleRegistrationNumber: optionalStringSchema.check(z.overwrite(val => (val === null ? val : val.toUpperCase()))),
})

export const createContractSchema = z.object({
  contractId: requiredStringSchema,
  customerId: requiredStringSchema,
  data: contractSchema,
})

export type CreateContractInput = z.infer<typeof createContractSchema>

export const csvRowSchema = z.pipe(
  z.object({
    title: requiredCsvStringSchema,
    first_name: requiredCsvStringSchema,
    last_name: requiredCsvStringSchema,
    date_of_birth: csvDateSchema,
    mobile_number: optionalCsvStringSchema,
    personal_number: optionalCsvStringSchema,
    email: optionalCsvStringSchema,
    address_line_one: optionalCsvStringSchema,
    address_line_two: optionalCsvStringSchema,
    address_line_three: optionalCsvStringSchema,
    address_postcode: optionalCsvStringSchema.check(z.overwrite(val => (val === null ? val : val.toUpperCase().replaceAll(' ', '')))),
    driving_licence: optionalCsvStringSchema,
    passport: optionalCsvStringSchema,
    outreach_id: optionalCsvStringSchema,
    agreement_number: requiredCsvStringSchema,
    eligible: createCsvBooleanSchema(true),
    inception_date: csvDateSchema,
    has_complaints: createCsvBooleanSchema(false),
    account_number: optionalCsvStringSchema.check(z.refine(val => val === null || /^\d{8}$/.test(val), { error: 'Account number must be 8 digits' })),
    sort_code: optionalCsvStringSchema.check(z.refine(val => val === null || /^\d{6}$/.test(val), { error: 'Sort code must be 6 digits' })),
    monthly_payment: optionalCsvStringSchema.check(
      z.refine(val => val === null || /^\d+(\.\d{1,2})?$/.test(val), { error: 'Money value not readable' }),
      z.overwrite(val => (val === null ? val : numberFormat.format(+val))),
    ),
    vehicle_model: optionalCsvStringSchema,
    vehicle_registration: optionalCsvStringSchema.check(z.overwrite(val => (val === null ? val : val.toUpperCase()))),
  }),
  z.transform(row => ({
    outreachId: row.outreach_id,
    title: row.title,
    forename: row.first_name,
    surname: row.last_name,
    dateOfBirth: row.date_of_birth,
    mobilePhoneNumber: row.mobile_number,
    personalPhoneNumber: row.personal_number,
    emailAddress: row.email,
    address: {
      lineOne: row.address_line_one,
      lineTwo: row.address_line_two,
      lineThree: row.address_line_three,
      postcode: row.address_postcode,
    },
    drivingLicenceNumber: row.driving_licence,
    passportNumber: row.passport,
    contractId: row.agreement_number,
    eligible: row.eligible,
    startDate: row.inception_date,
    hasComplaint: row.has_complaints,
    accountNumber: row.account_number,
    sortCode: row.sort_code,
    monthlyPaymentAmount: row.monthly_payment,
    vehicleModel: row.vehicle_model,
    vehicleRegistrationNumber: row.vehicle_registration,
  })),
)
