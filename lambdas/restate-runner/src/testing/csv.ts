import { parse } from 'csv-parse'
import logger from '../logger.ts'

export async function getRows(data: Uint8Array) {
  const rows = await new Promise<unknown[] | null>((resolve) => {
    const buffer = Buffer.from(data)
    parse(buffer, { delimiter: ',', columns: true }, (err, parseResult) => {
      if (err) {
        logger.error(err, 'Error parsing CSV data', { source: buffer.toString() })
        resolve(null)
      } else {
        resolve(parseResult)
      }
    })
  })
  return rows
}
