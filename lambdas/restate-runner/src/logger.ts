import type { LoggerTransport } from '@restatedev/restate-sdk'
import { Context<PERSON>ogger, Logger, getLevel } from '@kpmg-uk/logger'
import { subscribeToHttpEvents } from '@kpmg-uk/http-client'
import { APP_NAME, fromEnv } from './constants.ts'
import { als } from './als.ts'

const logger = new ContextLogger({
  label: APP_NAME,
  level: getLevel(fromEnv('LOGGING_LEVEL')),
  argsKey: 'details',
  alsInstance: als,
})

export default logger

const sdkLogger = new Logger({
  label: APP_NAME,
  level: getLevel(fromEnv('RESTATE_LOGGING')?.toLowerCase()),
  argsKey: 'details',
})

export const formatAsUuid = (traceId: string) => (
  `${traceId.slice(0, 8)}-${traceId.slice(8, 12)}-${traceId.slice(12, 16)}-${traceId.slice(16, 20)}-${traceId.slice(20)}`
)

export const restateLogger: LoggerTransport = (params, message, details) => {
  if (params.replaying) return

  if (params.source === 'SYSTEM') {
    sdkLogger.log(params.level === 'trace' ? 'debug' : params.level, message.trim(), { restateSdk: true })
  } else if (params.source === 'JOURNAL') {
    let correlationId = params.context!.request?.headers.get('x-correlation-id')
    const traceparent = params.context!.request?.attemptHeaders.get('traceparent')
    let traceId: string | undefined
    let spanId: string | undefined
    if (typeof traceparent === 'string') {
      const traceParts = traceparent.split('-')
      if (traceParts.length === 4) {
        [, traceId, spanId] = traceParts
        if (correlationId === undefined) {
          correlationId = formatAsUuid(traceId!)
        }
      }
    }
    if (details instanceof Error) {
      sdkLogger.log('error', message.trim(), {
        awsRequestId: params.context!.additionalContext!.AWSRequestId,
        correlationId,
        traceId,
        spanId,
        restateSdk: true,
        restate: {
          invocationId: params.context!.invocationId,
          serviceName: params.context!.serviceName,
          handlerName: params.context!.handlerName,
          key: params.context!.key,
        },
        error: details.message,
        stack: details.stack,
      })
      return
    }
    sdkLogger.log(params.level === 'trace' ? 'debug' : params.level, message.trim(), {
      awsRequestId: params.context!.additionalContext!.AWSRequestId,
      correlationId,
      traceId,
      spanId,
      restateSdk: true,
      restate: {
        invocationId: params.context!.invocationId,
        serviceName: params.context!.serviceName,
        handlerName: params.context!.handlerName,
        key: params.context!.key,
        extra: details,
      },
    })
  } else {
    // Logs from ctx.console.*
    logger.log(params.level, message, { details })
  }
}

subscribeToHttpEvents((message) => {
  switch (message.type) {
    case 'request':
      logger.debug('HTTP request to API', message.meta)
      break
    case 'request-cancelled':
      logger.info('HTTP request to API cancelled', message.meta)
      break
    case 'request-error':
      logger.error(message.error, 'HTTP request to API failed', message.meta)
      break
    case 'response':
      logger.debug('HTTP response from API', message.meta)
      break
    case 'response-error':
      logger.warn('HTTP response from API unsuccessful', message.meta)
      break
  }
})
