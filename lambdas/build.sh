#!/bin/bash

# Will NOT work if run directly on macos, only works when run from alpine Docker container

# This script will only seek to zip up the lambda code if the file contents have changed or the zip doesn't already exist
# It will also only re-install dependencies if the package*.json files have changed since the last install

cd $( cd "$(dirname "${BASH_SOURCE[0]}")" ; pwd -P )/$1

should_bundle=$(jq '.bundle' < package.json)

hash() {
  local result=$(tar -C . -cf - --sort=name --mtime='UTC 2020-01-01' $* | sha256sum)
  echo ${result::-3}
}

default_bundle() {
  esbuild ./src/main.js --bundle --sourcemap --sources-content=false --platform=node --target=node20.16.0 --format=esm --main-fields=module,main --external:pg-native --outfile=bundle/main.mjs --banner:js='import { createRequire } from "module";const require = createRequire(import.meta.url);'
}

bundle_code() {
  if [[ $should_bundle == "true" ]]; then
    if [[ $(jq '.scripts.bundle' < package.json) == "null" ]]; then
      default_bundle 2>&1
    else
      npm run bundle 2>&1
    fi
  fi
}

create_zip() {
  if [[ $should_bundle == "true" ]]; then
    zip -r1 ../function.zip bundle -X > /dev/null
  else
    zip -r1 ../function.zip node_modules src package.json -X > /dev/null
  fi
}

complete_hash_match=f
dependency_hash_match=f

complete_hash=$(hash src .npmrc package*.json)

if [ -f dist/complete_hash ]; then
  current_complete_hash=$(cat dist/complete_hash)
  if [[ "$current_complete_hash" == "$complete_hash" ]]; then
    complete_hash_match=t
  fi
fi

if [[ "$complete_hash_match" == "f" ]]; then
  dependency_hash=$(hash package*.json)

  if [ -f dist/dependency_hash ]; then
    current_dependency_hash=$(cat dist/dependency_hash)
    if [[ "$current_dependency_hash" == "$dependency_hash" ]]; then
      dependency_hash_match=t
    fi
  fi

  if [[ "$dependency_hash_match" == "f" ]]; then
    rm -rf dist
  else
    rm -rf dist/bundle dist/src dist/.npmrc dist/package*.json
  fi

  mkdir -p dist
  cp -R src .npmrc package*.json dist
  cp tsconfig*.json dist 2> /dev/null
  cd dist

  if [[ "$dependency_hash_match" == "f" ]] || [ ! -d node_modules ]; then
    npm config --location=project set engine-strict false
    if [[ $should_bundle == "true" ]]; then
      # If bundling, we need to install dev dependencies
      NODE_ENV=development DISABLE_OPENCOLLECTIVE=true npm ci --no-progress
    else
      DISABLE_OPENCOLLECTIVE=true npm ci --no-progress
    fi
  fi

  bundle_code
  create_zip

  echo -n $complete_hash > complete_hash
  echo -n $dependency_hash > dependency_hash
  cd ..
elif [ ! -f function.zip ]; then
  cd dist
  if [ ! -f bundle/main.mjs ]; then
    bundle_code
  fi
  create_zip
  cd ..
fi
