secrets:
  npm-token:
    environment: GH_NPM_TOKEN

services:
  claims-portal:
    ports:
      - 26100:80
    environment:
      AWS_ACCESS_KEY_ID: localstack
      AWS_ENDPOINT: http://localstack:4566
      AWS_REGION: eu-west-1
      AWS_SECRET_ACCESS_KEY: localstack
      LOCALHOST_MODE: true
      NODE_SINGLE_THREAD: true
      PORT: 80
      REDIS_HOSTNAME: redis://redis:6379
      RESTATE_INGRESS_URL: http://restate:8080
      SECRET_ID_COOKIE_SECRET: cookie-secret
    build:
      context: ../services/claims-portal
      secrets:
        - npm-token
