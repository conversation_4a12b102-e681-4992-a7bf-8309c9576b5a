# OpenID Connect (OIDC)

This folder contains the setup configuration for Keycloak to act as an IDP for our webapps and enable Single Sign On via OIDC strategy, to mimic how we will integrate with <PERSON><PERSON> in deployed environments

## Keycloak notes

You can access the management UI at http://localhost:26003/admin/master/console/#/sanmf to make any changes

Userna<PERSON> and password are `admin`

You can access the user portal at http://localhost:26003/realms/sanmf/account/#/ - this is useful to log out to test another user

### Updating the "sanmf.json" configuration file

Once your changes are made in the management UI, you need to attach a shell to the Keycloak Docker container, then run the following:

```sh
/opt/keycloak/bin/kc.sh export --file /opt/keycloak/data/import/sanmf.json --realm sanmf
```

As the container has this path volume mounted, you will see the updates in Git automatically

## Configuration

### Clients

| Client | Hostnames |
| --- | --- |
| TBC | TBC |

### Users

| Username | Password | Role |
| --- | --- | --- |
| ops1 | a | ops |
| ops2 | a | ops |
| <EMAIL> | a | rm |
| <EMAIL> | a | rm |

### Example flow

This is the flow for a portal

![SSO flow](./oidc-sso.png)
