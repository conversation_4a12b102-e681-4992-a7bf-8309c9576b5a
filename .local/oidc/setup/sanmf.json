{"id": "754f808e-deb7-492e-9aaf-df685c428a66", "realm": "sanmf", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "8be7e9d2-c824-4674-ad26-c5586e956855", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "754f808e-deb7-492e-9aaf-df685c428a66", "attributes": {}}, {"id": "1ead4d62-dc16-4881-a3ae-1a1f9d71e1e7", "name": "default-roles-sanmf", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "754f808e-deb7-492e-9aaf-df685c428a66", "attributes": {}}, {"id": "5b69c343-1b07-48a8-9f6d-d1daa6b40629", "name": "Portal - OPS", "description": "", "composite": false, "clientRole": false, "containerId": "754f808e-deb7-492e-9aaf-df685c428a66", "attributes": {}}, {"id": "43ff3606-d5ce-4ce9-a227-dcded6f3194f", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "754f808e-deb7-492e-9aaf-df685c428a66", "attributes": {}}], "client": {"realm-management": [{"id": "ea5a858c-2f40-48be-bcd4-7a3cc22d1eb7", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "592a4db8-f221-46eb-9556-8e631a467446", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "e3177618-12d2-4cbe-a7c7-55aefae1ce00", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "d0fd095c-57ba-4ce2-8f80-964931eb2e2e", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "fb8363a0-c387-4c51-96a8-83571cca3011", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "91ac489e-162d-41dc-98c1-c5ba831bafd7", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "a1d24469-c4fc-477e-bcbd-a84fcd64a248", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "ea18b60f-5b94-41f8-93e7-6a9f079b24b7", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "ae24ee90-f231-4dcb-9ce4-42d586057068", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "74c8d64a-8869-4f7a-8945-5b312ea6afdc", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "b247586d-c9ba-46d3-b0b2-9e8e47f833ae", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "fc46ef3e-e04c-4a8b-866b-c7d561ee5f3c", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "59b1c271-3a38-4fa7-87ad-b2f42da02680", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "9d111c4a-4495-4bb0-9749-42bf6716d132", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "c7cb12cb-e504-4c2b-bd1a-e4afd61a107a", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["query-realms", "view-identity-providers", "manage-identity-providers", "query-clients", "manage-authorization", "manage-users", "query-users", "manage-events", "view-users", "manage-clients", "manage-realm", "view-realm", "view-authorization", "create-client", "view-clients", "query-groups", "impersonation", "view-events"]}}, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "ea403d1d-17fe-4cf5-8334-cd7fe0d80c92", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "88e560cb-8ff7-4a22-accb-c495b23e62de", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "4366e7b0-939e-473b-a8f1-a525c5b5b2a9", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}, {"id": "f20f6cec-bb3d-424a-ae3e-980661fd3f16", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "41087b22-896c-4259-85ea-49be8ab16572", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "cc888eac-7a36-4742-92fd-a3f37c810f93", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "f23879cc-9775-4c20-80a7-a67c1f3a4af7", "attributes": {}}], "account": [{"id": "3a44f118-6cdf-455f-91e2-71a4d80a3832", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "61f1b820-d792-43b1-b364-b592326593a1", "attributes": {}}, {"id": "1eaa197d-ef32-44a1-9b3e-92c61571c0e7", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "61f1b820-d792-43b1-b364-b592326593a1", "attributes": {}}, {"id": "0f847518-b48d-44cb-968c-e025e506b31e", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "61f1b820-d792-43b1-b364-b592326593a1", "attributes": {}}, {"id": "b937606e-5d9b-4a6d-8a28-2b3805f7cd02", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "61f1b820-d792-43b1-b364-b592326593a1", "attributes": {}}, {"id": "31ae7091-36b6-47e6-a0ba-5576bd327d14", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "61f1b820-d792-43b1-b364-b592326593a1", "attributes": {}}, {"id": "6916f334-1c41-42fd-bfb7-644260fe10a9", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "61f1b820-d792-43b1-b364-b592326593a1", "attributes": {}}, {"id": "17ca2997-94d1-4641-b6e3-123d08fb8f52", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "61f1b820-d792-43b1-b364-b592326593a1", "attributes": {}}, {"id": "911e3efd-86d8-4482-b650-964c0107c70b", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "61f1b820-d792-43b1-b364-b592326593a1", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "1ead4d62-dc16-4881-a3ae-1a1f9d71e1e7", "name": "default-roles-sanmf", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "754f808e-deb7-492e-9aaf-df685c428a66"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppGoogleName", "totpAppMicrosoftAuthenticatorName", "totpAppFreeOTPName"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "686769e7-ea16-41a4-a97b-4f65c523e02b", "createdTimestamp": 1686748344143, "username": "ops1", "enabled": true, "totp": false, "emailVerified": false, "firstName": "Ops", "lastName": "One", "email": "<EMAIL>", "credentials": [{"id": "09af2754-c907-4ebd-95ca-d940924df50e", "type": "password", "userLabel": "My password", "createdDate": 1686748349140, "secretData": "{\"value\":\"WKwEwN0RX+CFCwA5889o2GuVVt+UrkZZ3GzWjTAk5Qk=\",\"salt\":\"r32sq5BnPVFBR0shA7Bung==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["Portal - OPS"], "notBefore": 0, "groups": []}, {"id": "d92ea8df-580c-4325-a53b-ebca6c7d21d0", "createdTimestamp": 1686748369037, "username": "ops2", "enabled": true, "totp": false, "emailVerified": false, "firstName": "Ops", "lastName": "Two", "email": "<EMAIL>", "credentials": [{"id": "508fd65c-f343-475f-a801-5fb83502dcd2", "type": "password", "userLabel": "My password", "createdDate": *************, "secretData": "{\"value\":\"sIttfAnp/+xRE5bPP6EVf8IOrmxD76NrF4D+1s0SCRo=\",\"salt\":\"0SMISBqxzqNixKMG3C+qAg==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["Portal - OPS"], "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "61f1b820-d792-43b1-b364-b592326593a1", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/sanmf/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/sanmf/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "b83e29ee-a8b7-4b98-b893-109d68387a82", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/sanmf/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/sanmf/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "66260bab-92df-4755-ba01-a5eb9cb242f3", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "c4a4f051-4701-4e7a-ae11-0227f7c47749", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "f23879cc-9775-4c20-80a7-a67c1f3a4af7", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "41087b22-896c-4259-85ea-49be8ab16572", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "102862b5-d627-498a-ad97-117f712b2945", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/sanmf/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/sanmf/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "e7528f7b-4f2b-41ca-bb1e-5880b7100645", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "acr", "profile", "roles", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "9be87c13-aee3-4b2a-a7f5-516b22f92aa5", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "d079826e-a3db-405e-b069-567123bd0541", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "25732d42-1406-48c0-aa42-453da4a274bc", "name": "Portal Role", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"aggregate.attrs": "false", "userinfo.token.claim": "false", "multivalued": "false", "user.attribute": "role", "id.token.claim": "true", "access.token.claim": "false", "claim.name": "role"}}, {"id": "dda7760e-e294-493e-b241-f9b4b97e249d", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "a35ee44f-fda9-456d-b1e0-b9c6c86a9b67", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "c17da42c-ad15-4d04-af6a-1e106533ba7b", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "long"}}, {"id": "b649410f-0a9a-4a79-9eb1-3113b1a56333", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "fa2ae16b-e618-4bd0-83a5-945004318150", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "cae4432d-d1e1-4c7d-b54f-dbd59342db96", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "bd6f9f97-f9f9-400a-ae00-e5e1c9fd239f", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "3245b27b-71b3-40f5-81c9-376be165d025", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "f6bdc609-761d-4b15-a664-1a5c44ab8369", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "fb03eed2-5144-4a6d-acbd-d214cb192202", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "73f8f229-a3f2-473f-b714-b8f87ece8791", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "e74eb46b-b3ce-4851-96e6-e65702988357", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "9a7674d9-20fb-4ef2-8b02-b0c1821bea56", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "a7ac5c7a-3eab-431c-937c-7d2ec003f2bf", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}]}, {"id": "b8782c04-4bd7-4f14-a8e1-040d2ecb73ea", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "e94e0960-52b1-40ef-8050-63b1172bb76f", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "854ceeed-dc16-4689-aa22-8ea2220a4b57", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "2bb77e5c-e08f-45d5-89cd-4ab59447b4f8", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "354ab32c-598f-4497-b864-426966d51537", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "399e3628-a526-489f-a6e0-7cc1ae233b51", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "fe294f7f-4681-462e-b817-63344e1d6fbf", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "eb5f6cb1-129e-4e59-a5cd-2511a1938313", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "9a2c9fa2-5419-48dd-8f6d-fe338d38674f", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "76ba22f7-f137-4dda-af46-24c7a3fb5c8f", "name": "groups", "description": "", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false", "gui.order": "", "consent.screen.text": ""}, "protocolMappers": [{"id": "22cc9841-1486-463e-866e-d661f93d7712", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "false", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "abbdbc93-8273-458f-aa13-23599ecea9f8", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "acedcb0e-1af3-4d97-9467-c22ef212464e", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "6388de57-e0b4-437e-9238-e4c5b8355f72", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "fde33677-9106-4370-987d-42546c8d4dc0", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}]}, {"id": "ef952e45-9311-475d-bc09-65414c7fa9f8", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "73f4f02e-2ced-44cf-b1f1-c5b89406a90a", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "113c2ae5-34ec-4cab-a77b-633d224cba51", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "e774a9dd-e4de-494d-810d-f89f0f278097", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "a5ca52e9-ba69-4121-9c54-e8768900c6e9", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "fedecb81-523c-42c0-abef-5a3ee5c7248e", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "b316fdbd-8ac2-48b8-8b7a-f6bf87359827", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "7f3e4d71-490c-4abc-860e-72f264f241b6", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr", "groups"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "183b08ea-3271-4be0-905d-f2baa0174b96", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "ba8c29af-4b54-40f9-b5cb-34af3b15cc43", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "72d2ca30-5590-4d64-8db3-d49b2464b4b9", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "70c0de4b-47e4-4bfa-b945-d5f862b0d3c5", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "61109950-fb89-4334-a55e-1484a82ac0d3", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "f6400af8-3bea-4a07-a0fb-f4341b565dd1", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "b4623bdb-72b7-42d2-ad77-6289b44410be", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-address-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper"]}}, {"id": "62231d02-a1ce-42ec-9956-22f780464207", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-role-list-mapper", "saml-user-property-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper"]}}], "org.keycloak.keys.KeyProvider": [{"id": "d15afc69-e158-4790-805f-a8a9f1b9009e", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["9b408fe2-551f-4f03-ac06-c6dcc25f8c9a"], "secret": ["Y11Kmjw6eb4HK-vqHfFqQ_lSB5TwBQy8VkG7JNQcvTVnb2qotMjgkzzNODvPvlluuDcQ7ighRSJVWBzF4afj2w"], "priority": ["100"], "algorithm": ["HS256"]}}, {"id": "da6de860-b81e-41c0-b7e1-853f161be861", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"kid": ["e554a47e-0962-4e5b-8e6f-e84b4a93b0d7"], "secret": ["MYWxdudk6tIcgxB2Ug7knA"], "priority": ["100"]}}, {"id": "d8570368-c41e-4591-9310-da0bfad2106e", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEpQIBAAKCAQEArkK7EpRi19TQ2HVMYvwohUgUD2x807bIVqM5DTIhBUEGKu7g42rUTNQxEagVyS0hCVwOnmn254YSk8MtUbR0RI6NjYsuECM+oj2p+LTDppb1TchZnhpTPzcyrxbjNGmLH1jYia/So+tZFmQt1BfvEmKJqg/knIAIVoiC+Z1wtlaLpggeyM44H9GRBy/qiBqHwpcKISu0P8bwFrm5Tlx9TyopM6ug2Gfe9rlRqr35D5g20EOO/VorxgcugrfMXuu4aWNRBBevrEd4CKFQ6Y4ii9dauvveBpS22NVphahC4A8yACKASClAdl0C0X0kVvLCiv0DuuGeNZ1cQN0DnEB/lwIDAQABAoIBAA0thrAVAJSJx3FXDXu+zeGmXb382kO4YtYKadoRnGWlUS4V+nO4NG3K7hsDtwILo5/tkj1wjfdcMDN0Ywz2tIQqs5GsoMMfpzDlsODZ6qHCcLJ9ICnQm9L//Zk4Bd6EzLyGH3nqDefPeGE1GjIbzTx6ryZpJUSgegDxOdZejZN8QAdhK8ju+KUf6VBQInbv6//wMrouw00Hy9b5VquOdBkv9kNU372g8235astL5n0/mFheIqNivuvX2X5I3eHo0rUJ2CypuMWMvma7BGEOMuCZEYlGDTYeXZYXwuqr3W22zS90YRY93Ye5pB6W9rQ9R8mL3wOy8MDdo9Lo0hQuMwECgYEA1+3EK5uexL/EOHBmVyCLguEhklBEWjC1aPUet25a/EDSdOVnyXXfyJKCl3qC6nMauNKFAoyVjCVTyAu6Q8cp2HxKi9wlUYMvhm7B61kN/DlvV2W6zbw4EmKFjE9aJN6Rl6VHJ4Y/UDTDUvaIlhi0UzAlukfM5rB18ec01XdnF+ECgYEAzplq1lkNe9dy4FNa5D197LatvLQbV8v21ENuFhPPG2scbusiAP4UU5shZ+rf2J/lIddkTsopimtACcoit1oLmwawXYLCxYHccLLM2NqLGptYaAqvrsIEBLj65FFvYKl1mggg8vdGg+f4iGEZD2uzYSimfvxhjjTcymY6NVLpJncCgYEAwCaF+4hwsQU1yEO9zqrXe5B8H4fuS5Qs20nGG9XIgdHtdrjuswiCLCq0q7gJ6Dil/vLYCnywWBafh9Vn3AjP5CY28F7nlrH3CNpnFtgY6eryegO9SHfGLhYeYc37mfO1/AzIVRPSI73QU3Ov+GHjlmOyLyLV5DQDwVDOY2fuBgECgYEApVJZDttGEfKO53lsQzX9vV1VW6qYMsJxatvmjN4fHeaxYwTfOgIXi+uYaMqng3Y36cOtbesSIMTAAtVwwF3a6ZwJp1qKt3e6SaiaiXEgXRMwRFlAykxQ4OKRSgDsamJQTAjzLNm44LbQx3lDj3Z0nWvlB7q36g7dG02AiMkm7vMCgYEApFBAC3u2N3eXFIb2LYTYYSSRLotf7uVUqs+i+9PnT/O79ilxyFQ1fKZoDXyeqT4lM457NpNh/xt6XsewOGkXi4stsq/iUwIeW/IiQJE4f4DSFP3IKdVurwbAjjNW/HFeZhy/GqhXoqtfHIjty3DK83DEEUr/gTQoIlJAO/28d+o="], "keyUse": ["SIG"], "certificate": ["MIICmzCCAYMCBgGItZR4dDANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZzYW5jZGQwHhcNMjMwNjEzMTYyNDQ2WhcNMzMwNjEzMTYyNjI2WjARMQ8wDQYDVQQDDAZzYW5jZGQwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCuQrsSlGLX1NDYdUxi/CiFSBQPbHzTtshWozkNMiEFQQYq7uDjatRM1DERqBXJLSEJXA6eafbnhhKTwy1RtHREjo2Niy4QIz6iPan4tMOmlvVNyFmeGlM/NzKvFuM0aYsfWNiJr9Kj61kWZC3UF+8SYomqD+ScgAhWiIL5nXC2VoumCB7Izjgf0ZEHL+qIGofClwohK7Q/xvAWublOXH1PKikzq6DYZ972uVGqvfkPmDbQQ479WivGBy6Ct8xe67hpY1EEF6+sR3gIoVDpjiKL11q6+94GlLbY1WmFqELgDzIAIoBIKUB2XQLRfSRW8sKK/QO64Z41nVxA3QOcQH+XAgMBAAEwDQYJKoZIhvcNAQELBQADggEBADG69sLsWYVbnmPP5H8EvRDnseTcy1Nq4696UXpatdqsFZ2Txq7/YhecjzrQ4dZGd4oQWK1cA0f39Uukn3QzCTLWCp0yD4qc978DBO60vYiweBq7dl5SaXKvQuU5rTkzYVvaRizqRPKjR+L3mIXTm/PIzbIEkajoaTQyd+qoZN1Jo6dhYGxdYDftcjLRrAly5hTM5GnGz2AAVbO1BaoyyGBn2T1sMZstxy/EESi8bIXz7NizDFkRhTMxMCqx/QAZ7ikMgsOUZPXG7dAZkQIQ8iM7yrjex/FVYYIpVyFXdPkjbbebBUwGK+zCEWQCHeHsI25qh+oYiHzb9agDOgFgi7s="], "priority": ["100"]}}, {"id": "d539df0d-4fa0-490b-b0ab-b658d0d7618b", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAs/Z9tFpqwPBlp5iRmrpfTHMOZZCTmmBGWJ5dw31h/KnA9v9TnE1T6LHoPEUoIFnVmWNoNK5NiSboeVhN2WdDLHqhM0adAjSgZpwuB70/3QhjnhoLYg8ys+dN5e/IP5OLqQGeLBRoC+ttV8Me5qRZOSwKzP/5jmOlro44kfVAze6NYr2/A1XT/6G4zTFP20DCkeyOH7HdupMF5OnsWYcVMQStmffCUtTQpN3vpIsfSE0d0R7QDZBaO7/3OBJNEfsxomCz59yrQ5Ex8aYJsj14sDM8vfWGD2iLLtNgWWbFNt9z4iUt1WanZ1ItZnhcb5+6WegPWCT96K2HHp2GhtqwdQIDAQABAoIBAAG+LK5796+03ejD6TKvhMla2rQjtJvYoqXGEHaag465kb6/b0hcVihmD+LRiqQ+Xa61tnMk9jRFvH4JKGV0HHbutk2NIAUlWSGIeEhz56chpyrttqS1X/jVD/CSCp7vAPnBV1sdVK3U34/rhr38fOHsXSudSQXwTVD49W9+1nW7SjHwHeMXEjhlXBG6vzqjGyy5PV+rLLn5O2ddwO3HC3ZVppxNi1cFc3tFPkiGo7ItD4ugkFOQ0sw0aOaBo0y61cIPGf+rM82oqvsatwh6ZPW+YoZq4r/8bdCBKyYFM4wWKMtKmRVgvF37d3shV9XZLbzz5FCjogtyM+Obr+HfyjECgYEA/Yl/czLq/9aQvY8VEkfSRgy2s8fksyxdkPUsqejhkExNs5uKUJ38oVCJGc0kwgp1BEnWNbTS2wTQJEwXMVJrVwgn6Mq6Kaehot0AtYSEI4erDfECHAIcLNUbplnbELQvE5wPxRaoqJDoWEgnNf3NzSki3a20y0QB7rcn7uFObRkCgYEAtbYG6E6t+w5Alu+xxoQrzzvBnIHuUqDHeZ2tP7mfFwgpv7rYjdVG0eg56Sx+1o9ga4hJypMAtUqJQJ+xngpJb4SefhZ6sAyZq/iTNCf8RZL0kqqpnbsvz9Y8Usm4U+AE7Ij16x1G/fFiGmnaVwzwYlkh71gY4ZSO0q6y52cU7b0CgYEA3RFfB9uF/L3iRubTwLTPLDU5uVi6mWA91X9yClT9GQzvU4Rz/gdDmITYhp7RRkBH+A1JslqQ4Fg6BOwz7NWosqJx6hGacWexEIkTbTK25SQOioFzDWjpmE3ZZhtDYDU8NyUEux0ewpxXe2Kuje1X3WPY+uHTmskZxkP9ldTuxUECgYADT3+st9I/orDWjyNkBIBv1Bzg5xUaeEdqFc9UO+OEHef00EilvN7/cAWuE9tzuUT+cIs3n5oBs4vwm3g28uUHnRjuHya5EmGyWmMELj5QXi8KhQV2FDT4ymRq9bXgAVLVpGEmmW/zrHQnGwqZT2kJwan2nFpHsdcwS3KC+MPO2QKBgEfv4aov8rm7u7/KF941IAj8UIMR8XtS4hoT336LiWUnD8RT2odT3AI99DdPKy/KZ4q901FmK5x+i/S3KX/Lb1km+XZf4m3U3BWVUMW5I/c8iSNIK4eeS2jGo5HpW6NTf6B0XH2KiMYtVfO2r4vkuOBvhyyWIJRIIhj1q6UBy4bL"], "keyUse": ["ENC"], "certificate": ["MIICmzCCAYMCBgGItZR5uDANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZzYW5jZGQwHhcNMjMwNjEzMTYyNDQ2WhcNMzMwNjEzMTYyNjI2WjARMQ8wDQYDVQQDDAZzYW5jZGQwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCz9n20WmrA8GWnmJGaul9Mcw5lkJOaYEZYnl3DfWH8qcD2/1OcTVPoseg8RSggWdWZY2g0rk2JJuh5WE3ZZ0MseqEzRp0CNKBmnC4HvT/dCGOeGgtiDzKz503l78g/k4upAZ4sFGgL621Xwx7mpFk5LArM//mOY6WujjiR9UDN7o1ivb8DVdP/objNMU/bQMKR7I4fsd26kwXk6exZhxUxBK2Z98JS1NCk3e+kix9ITR3RHtANkFo7v/c4Ek0R+zGiYLPn3KtDkTHxpgmyPXiwMzy99YYPaIsu02BZZsU233PiJS3VZqdnUi1meFxvn7pZ6A9YJP3orYcenYaG2rB1AgMBAAEwDQYJKoZIhvcNAQELBQADggEBAJSf7uET5ODIK02sIRV9wDRc3Z9vFxy/OvqqUFAKhkc6sIZyN8V6u9WCu1ezIG5NubAOdloT4yzpwHGO6XTiwGRTTiJzlILhG8c1wk337eLznVit2iwpVoNKxdnlGTNGNQVGsUin79DmtswRXyLQtTDBGUpZlQQFd1x7I7pSe2FSUUv/+kmp1mWO3LMICXZUW4vSD3Mf1yQczNrGuCVuWdUg4Tw3uDHLiJu/QGJB69Az5s/4qn1Qmc2x6e8MDOmj+FJRg9QNiF+HO++6omop1CDp56jt4JJ41Q9KH5XZerkq82t7mWLeAN7bW1K5/SMc/v7BtL6rNaXJJ3jp3aoCm5o="], "priority": ["100"], "algorithm": ["RSA-OAEP"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "9e14b30d-647d-46a0-a18f-5b2799cb5359", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "********-3534-4096-a7c8-7fb6bf8e7353", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "basic-auth-otp", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "45e849fb-77df-44ee-988a-972d1be43b75", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "c04892b9-0e3f-4644-bcaa-46ec226a6373", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "90416c23-dabe-4186-b15d-7a6c3bacf489", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "fdd847c9-e31a-4140-9588-4c27ee31c251", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "782d445c-2d9d-4090-a449-6db3fdf3d823", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e438da89-d5d5-4263-b739-c39314bc928d", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "ee26eb53-7f34-458e-a473-80f61cbdec6a", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "********-7038-4d7b-a60c-c6a1f28419a8", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "69dda470-8b84-4a0a-9671-119e4e4034fa", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "858f7ec1-0991-415c-b4db-8b0c295fe41d", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "2d8447ac-e260-4a43-8577-adbbcbbb5b27", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "932fcb9e-a4b5-4b3d-8322-8dec948105c5", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "d8ddea84-627f-41c4-8afe-c1913c9999fd", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "bbb1f5cf-25b4-41a2-8643-00a913838056", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Authentication Options", "userSetupAllowed": false}]}, {"id": "9cdaaaab-8184-4dcf-82ad-8baeb4252e39", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "55955c5b-7d49-43d5-82ef-3d0ce55fa277", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-profile-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "f59ea488-54c7-469d-9834-0e6b9031e915", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "d72e8eaa-8e38-4fe8-80a8-d8c1f9724fb8", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "f7906a53-2bd5-4aac-9ade-97b6e4f56a06", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "4718dba1-8323-4a90-9264-289d95598a51", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "webauthn-register", "name": "Webauthn Register", "providerId": "webauthn-register", "enabled": true, "defaultAction": false, "priority": 70, "config": {}}, {"alias": "webauthn-register-passwordless", "name": "Webauthn Register Passwordless", "providerId": "webauthn-register-passwordless", "enabled": true, "defaultAction": false, "priority": 80, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5", "realmReusableOtpCode": "false"}, "keycloakVersion": "21.1.1", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}