services:
  localstack:
    image: localstack/localstack:4.5.0
    ports:
      - 26000:4566
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      MAIN_CONTAINER_NAME: sanmf-localstack-1

  postgres:
    image: postgres:15
    ports:
      - 26001:5432
    environment:
      POSTGRES_PASSWORD: testpassword

  redis:
    image: redis:7
    ports:
      - 26002:6379

  oidc:
    image: quay.io/keycloak/keycloak:21.1.1
    ports:
     - 26003:8080
    command: start-dev --import-realm
    environment:
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
    volumes:
      - ${PWD}/oidc/setup:/opt/keycloak/data/import

  jaegar:
    image: jaegertracing/all-in-one:1.46
    ports:
      - 26004:16686
    environment:
      COLLECTOR_OTLP_ENABLED: "true"

  restate:
    image: restatedev/restate:1.3.2
    hostname: restate-1
    ports:
      - 26005:8080
      - 26006:9070
      - 26007:9071
    environment:
      RESTATE_TRACING_ENDPOINT: http://jaegar:4317
      RESTATE_INGRESS__ADVERTISED_INGRESS_ENDPOINT: http://localhost:26005
    volumes:
      - ${PWD}/restate/.aws:/root/.aws
      - ${PWD}/restate/data:/restate-data
