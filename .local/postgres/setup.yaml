services:
  postgres-setup-complete:
    depends_on:
      postgres-setup:
        condition: service_completed_successfully
    image: alpine:3.22
    command: ["sleep", "1"]

  postgres-setup:
    depends_on:
      postgres:
        condition: service_started
    build:
      context: ./postgres
    environment:
      SKIP_DB_LIST:
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ${PWD}/postgres/scripts:/var/run/scripts
      - ${PWD}/../db:/var/run/db
