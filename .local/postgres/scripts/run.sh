#!/bin/bash

while ! [[ $(echo "\dn" | docker exec -i sanmf-postgres-1 psql -U postgres 2> /dev/null) ]]; do
  echo "Postgres loading..."
  sleep 1
done

echo "Postgres ready"

# DBs can be skipped by setting the SKIP_DB_LIST env var to a comma separated list of folders
# e.g. auth-service-db,case-data-vault-db

declare -A skip_db_map

if [[ ! -z $SKIP_DB_LIST ]]; then
  for db in $(echo $SKIP_DB_LIST | tr "," "\n"); do
    skip_db_map[$db]='1'
  done
fi

for db in ./db/*/ ; do
  db_name=$(basename $db)
  if [[ "${skip_db_map[$db_name]}" == "1" ]]; then
    echo "Skipping $db_name"
  else
    for script in ${db}scripts/* ; do
      docker exec -i sanmf-postgres-1 psql -U postgres < $script
    done
  fi
done
