services:
  localstack-setup:
    depends_on:
      localstack:
        condition: service_started
    build:
      context: ./localstack
    command: ["node", "src/deployRestateLambda.js"]
    volumes:
      - ~/.aws:/home/<USER>/.aws
      - ${PWD}/..:/home/<USER>/repo
      - ${PWD}/localstack/src:/home/<USER>/app/src
    environment:
      AWS_ACCESS_KEY_ID: localstack
      AWS_ENDPOINT: http://localstack:4566
      AWS_REGION: eu-west-1
      AWS_SECRET_ACCESS_KEY: localstack
      LAMBDA_NAME: ${LAMBDA_NAME}
      NODE_AUTH_TOKEN: ${GH_NPM_TOKEN}
