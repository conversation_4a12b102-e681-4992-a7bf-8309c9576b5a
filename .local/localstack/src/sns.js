import { SNSClient, CreateTopicCommand, ListSubscriptionsCommand, ListTopicsCommand, SubscribeCommand } from '@aws-sdk/client-sns'
import { lambdaArn, topicArn, queueArn } from './arn.js'

const endpoint = process.env.AWS_ENDPOINT

const snsClient = new SNSClient({ endpoint })

export const listTopics = async () => {
  console.log('Getting topics')
  const listTopicsCommand = new ListTopicsCommand({})
  const result = await snsClient.send(listTopicsCommand)
  console.log('Got topics')
  return new Set(result.Topics.map(topic => topic.TopicArn.split(':').at(-1)))
}

export const createTopic = async (name) => {
  console.log('Creating topic', name)
  const createTopicCommand = new CreateTopicCommand({ Name: name })
  await snsClient.send(createTopicCommand)
  console.log('Created topic', name)
}

export const listSubscriptions = async () => {
  console.log('Getting topic subscriptions')
  const listSubscriptionsCommand = new ListSubscriptionsCommand({})
  const result = await snsClient.send(listSubscriptionsCommand)
  console.log('Got topic subscriptions')
  return result.Subscriptions.reduce((acc, subscription) => {
    if (subscription.Protocol === 'lambda') {
      acc.push({
        lambda: subscription.Endpoint.split(':').at(-1),
        topic: subscription.TopicArn.split(':').at(-1),
      })
    }
    return acc
  }, [])
}

export const subscribeLambdaToTopic = async (lambda, topic) => {
  console.log('Subscribing', lambda, 'to', topic)
  const subscribeCommand = new SubscribeCommand({
    TopicArn: topicArn(topic),
    Protocol: 'lambda',
    Endpoint: lambdaArn(lambda),
  })
  await snsClient.send(subscribeCommand)
  console.log('Subscribed', lambda, 'to', topic)
}

export const subscribeQueueToTopic = async (queue, topic) => {
  console.log('Subscribing', queue, 'to', topic)
  const subscribeCommand = new SubscribeCommand({
    TopicArn: topicArn(topic),
    Protocol: 'sqs',
    Endpoint: queueArn(queue),
  })
  await snsClient.send(subscribeCommand)
  console.log('Subscribed', queue, 'to', topic)
}
