import { SESClient, VerifyEmailIdentityCommand } from '@aws-sdk/client-ses'

const endpoint = process.env.AWS_ENDPOINT

const sesClient = new SESClient({ endpoint })

export const addVerifiedEmailIdentity = async (id) => {
  console.log('Adding verified email', id)
  const verifyEmailIdentityCommand = new VerifyEmailIdentityCommand({
    EmailAddress: id,
  })
  await sesClient.send(verifyEmailIdentityCommand)
  console.log('Verified email added', id)
}
