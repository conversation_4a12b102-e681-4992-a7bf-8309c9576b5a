import http from 'http'
import { publishLambdaVersion, deleteLambdaVersion } from './lambda.js'

export const registerLambda = async (lambda) => {
  const arn = await publishLambdaVersion(lambda)

  console.log('Registering lambda with restate', arn)

  await new Promise(async (resolve, reject) => {
    const req = http.request({
      protocol: 'http:',
      hostname: 'restate',
      port: 9070,
      path: '/deployments',
      method: 'POST',
      headers: {
        'content-type': 'application/json',
      },
    }, (res) => {
      const data = []
      res.on('data', (chunk) => {
        data.push(chunk.toString())
      })
      res.on('end', () => {
        if (res.statusCode === 201) {
          resolve()
        } else {
          console.log('Status:', res.statusCode)
          console.log('Response:', data.join(''))
          reject()
        }
      })
    })
    req.on('error', reject)
    req.write(JSON.stringify({ arn, force: true }))
    req.end()
  })

  console.log('Registered lambda with restate', lambda)
}

export const drainRestate = async () => {
  /** @type {{ id: string, ty: string, endpoint: string }[]} */
  const drainedDeployments = await new Promise((resolve, reject) => {
    const req = http.request({
      protocol: 'http:',
      hostname: 'restate',
      port: 9070,
      path: '/query',
      method: 'POST',
      headers: {
        'content-type': 'application/json',
        'accept': 'application/json',
      },
    }, (res) => {
      const data = []
      res.on('data', (chunk) => {
        data.push(chunk.toString())
      })
      res.on('end', () => {
        if (res.statusCode === 200) {
          resolve(JSON.parse(data.join('')).rows)
        } else {
          console.log('Status:', res.statusCode)
          console.log('Response:', data.join(''))
          reject()
        }
      })
    })
    req.on('error', reject)
    req.write(JSON.stringify({
      query: `
        SELECT d.id, d.ty, d.endpoint
        FROM sys_deployment d
        LEFT JOIN sys_service s ON (d.id = s.deployment_id)
        LEFT JOIN sys_invocation_status i ON (d.id = i.pinned_deployment_id)
        WHERE s.name IS NULL
        AND i.id IS NULL;
      `,
    }))
    console.log('Querying restate for drained deployments')
    req.end()
  })

  for (let i = 0; i < drainedDeployments.length; i += 1) {
    const deployment = drainedDeployments[i]
    console.log('Removing drained deployment', deployment.id, deployment.endpoint)
    await new Promise((resolve, reject) => {
      const req = http.request({
        protocol: 'http:',
        hostname: 'restate',
        port: 9070,
        path: `/deployments/${deployment.id}?force=true`,
        method: 'DELETE',
      }, (res) => {
        const data = []
        res.on('data', (chunk) => {
          data.push(chunk.toString())
        })
        res.on('end', () => {
          if (res.statusCode === 202) {
            resolve()
          } else {
            console.log('Failed to remove drained deployment', deployment.id, deployment.endpoint)
            console.log('Status:', res.statusCode)
            console.log('Response:', data.join(''))
            reject()
          }
        })
      })
      req.on('error', reject)
      req.end()
    })
    if (deployment.ty === 'lambda') {
      try {
        await deleteLambdaVersion(deployment.endpoint)
      } catch {
        console.log('Failed to delete lambda version, may not exist', deployment.endpoint)
      }
    }
    console.log('Removed drained deployment', deployment.id, deployment.endpoint)
  }
}
