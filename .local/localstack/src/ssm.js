import { SSMClient, GetParameterCommand, PutParameterCommand } from '@aws-sdk/client-ssm'

const endpoint = process.env.AWS_ENDPOINT

const ssmClient = new SSMClient({ endpoint })

const getParameter = async (name) => {
  console.log('Getting parameter', name)
  const getParameterCommand = new GetParameterCommand({
    Name: name,
    WithDecryption: true,
  })
  try {
    const result = await ssmClient.send(getParameterCommand)
    console.log('Got parameter', name)
    return result.Parameter.Value
  } catch {
    console.log('Parameter', name, 'does not exist')
    return null
  }
}

export const putParameter = async (name, value, secure = false) => {
  const currentValue = await getParameter(name)
  if (currentValue === value) {
    console.log('Parameter', name, 'already set')
    return
  }
  console.log('Updating parameter', name)
  const putParameterCommand = new PutParameterCommand({
    Name: name,
    Value: value,
    Overwrite: true,
    Type: secure ? 'SecureString' : 'String',
  })
  await ssmClient.send(putParameterCommand)
  console.log('Updated parameter', name)
}
