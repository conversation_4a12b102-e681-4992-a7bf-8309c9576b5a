import { SQSClient, CreateQueueCommand, ListQueuesCommand } from '@aws-sdk/client-sqs'

const endpoint = process.env.AWS_ENDPOINT

const sqsClient = new SQSClient({ endpoint })

export const listQueues = async () => {
  console.log('Getting queues')
  const listQueuesCommand = new ListQueuesCommand({})
  const result = await sqsClient.send(listQueuesCommand)
  console.log('Got queues')
  return new Set(result.QueueUrls?.map(url => url.split('/').at(-1)))
}

export const createQueue = async (name) => {
  console.log('Creating queue', name)
  const createQueueCommand = new CreateQueueCommand({ QueueName: name })
  await sqsClient.send(createQueueCommand)
  console.log('Created queue', name)
}
