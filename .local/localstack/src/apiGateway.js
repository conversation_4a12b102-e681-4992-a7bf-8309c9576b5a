import { APIGatewayClient, CreateDeploymentCommand, CreateResourceCommand, CreateRestApiCommand, GetResourcesCommand, GetRestApisCommand, PutIntegrationCommand, PutIntegrationResponseCommand, PutMethodCommand } from '@aws-sdk/client-api-gateway'
import { lambdaArn } from './arn.js'

const endpoint = process.env.AWS_ENDPOINT
const region = 'eu-west-1'

const apiClient = new APIGatewayClient({ endpoint })

export const listApis = async () => {
  console.log('Getting APIs')
  const listApisCommand = new GetRestApisCommand({})
  const result = await apiClient.send(listApisCommand)
  console.log('Got APIs')
  return new Set(result.items?.map(api => api.id))
}

export const createApi = async (id, name) => {
  console.log('Creating API', name)
  const createRestApiCommand = new CreateRestApiCommand({ name, tags: { '_custom_id_': id } })
  await apiClient.send(createRestApiCommand)
  console.log('Created API', name)
}

export const listApiIntegrations = async (apis) => {
  console.log('Getting existing API integrations')
  const integrations = {}
  for (let i = 0; i < apis.length; i += 1) {
    const restApiId = apis[i]
    const getResourcesCommand = new GetResourcesCommand({ restApiId })
    const result = await apiClient.send(getResourcesCommand)
    const paths = result.items.reduce((acc, item) => {
      const { pathPart, resourceMethods = {}, path } = item
      if (pathPart) {
        const methods = Object.keys(resourceMethods)
        acc[path] = methods
      }
      return acc
    }, {})
    integrations[restApiId] = {
      parentId: result.items[0].id,
      paths,
    }
  }
  console.log('Got existing API integrations')
  return integrations
}

export const createApiEndpointIntegration = async (restApiId, httpMethod, endpointParts, details) => {
  console.log('Getting API parent ID for', restApiId)
  const getResourcesCommand = new GetResourcesCommand({ restApiId })
  const resources = await apiClient.send(getResourcesCommand)
  let parentId = resources.items[0].id

  console.log('Creating API endpoint resources', restApiId, endpointParts)
  let resourceId
  for (let i = 0; i < endpointParts.length; i += 1) {
    const createResourceCommand = new CreateResourceCommand({
      restApiId,
      parentId,
      pathPart: endpointParts[i],
    })
    const { id } = await apiClient.send(createResourceCommand)
    resourceId = id
    parentId = id
    console.log('Created API endpoint resource', restApiId, endpointParts)
  }

  console.log('Defining API method', restApiId, endpointParts)
  const putMethodCommand = new PutMethodCommand({
    restApiId,
    resourceId,
    httpMethod,
    authorizationType: 'None',
  })
  await apiClient.send(putMethodCommand)
  console.log('API method defined', restApiId, endpointParts)
  
  const { type } = details
  if (type === 'AWS_PROXY') {
    console.log('Creating AWS_PROXY API integration', restApiId, endpointParts)
    const { lambda } = details
    const putIntegrationMethod = new PutIntegrationCommand({
      restApiId,
      resourceId,
      httpMethod,
      type,
      integrationHttpMethod: 'ANY',
      uri: `arn:aws:apigateway:${region}:lambda:path/2015-03-31/functions/${lambdaArn(lambda)}/invocations`,
      passthroughBehavior: 'WHEN_NO_MATCH',
    })
    await apiClient.send(putIntegrationMethod)

  } else if (type === 'MOCK') {
    console.log('Creating MOCK API integration', restApiId, endpointParts)
    const putIntegrationMethod = new PutIntegrationCommand({
      restApiId,
      resourceId,
      httpMethod,
      type,
      integrationHttpMethod: 'ANY',
      passthroughBehavior: 'WHEN_NO_MATCH',
    })
    await apiClient.send(putIntegrationMethod)

    const { responseTemplates, statusCode } = details
    const putIntegrationResponse = new PutIntegrationResponseCommand({
      restApiId,
      resourceId,
      httpMethod,
      statusCode,
      responseTemplates
    })
    await apiClient.send(putIntegrationResponse)
  }

  console.log('Created API integration', restApiId, endpointParts)
}

export const createApiDeployment = async (restApiId) => {
  console.log('Creating API deployment', restApiId)
  const createDeploymentCommand = new CreateDeploymentCommand({ restApiId, stageName: 'dev' })
  await apiClient.send(createDeploymentCommand)
  console.log('Created API deployment', restApiId)
}
