import { CreateSecretCommand, GetSecretValueCommand, SecretsManagerClient, UpdateSecretCommand } from "@aws-sdk/client-secrets-manager"

const endpoint = process.env.AWS_ENDPOINT

const secretManagerClient = new SecretsManagerClient({ endpoint })

const getSecret = async (secretId) => {
  console.log('Getting secret', secretId)
  const getSecretValue = new GetSecretValueCommand({ SecretId: secretId })
  try {
    const result = await secretManagerClient.send(getSecretValue)
    console.log('Got secret', secretId)
    return result.SecretString
  } catch {
    console.log('Secret', secretId, 'does not exist')
    return null
  }
}

export const putSecret = async (name, value) => {
  const currentValue = await getSecret(name)
  if (currentValue === value) {
    console.log('Secret', name, 'already set correctly')
  } else if (currentValue === null) {
    console.log('Creating secret', name)
    const createSecretCommand = new CreateSecretCommand({
      Name: name,
      SecretString: value,
    })
    await secretManagerClient.send(createSecretCommand)
    console.log('Created secret', name)
  } else {
    console.log('Updating secret', name)
    const updateSecretCommand = new UpdateSecretCommand({
      SecretId: name,
      SecretString: value,
    })
    await secretManagerClient.send(updateSecretCommand)
    console.log('Updated secret', name)
  }
}
