import fs from 'fs'
import { S3Client, CreateBucketCommand, ListBucketsCommand, PutBucketNotificationConfigurationCommand, PutBucketVersioningCommand, PutObjectCommand } from '@aws-sdk/client-s3'
import { lambdaArn, topicArn, queueArn } from './arn.js'

const endpoint = process.env.AWS_ENDPOINT
const mountRepoDir = '/home/<USER>/repo'

const s3Client = new S3Client({ endpoint, forcePathStyle: true })

export const listBuckets = async () => {
  console.log('Getting buckets')
  const listBucketsCommand = new ListBucketsCommand({})
  const result = await s3Client.send(listBucketsCommand)
  console.log('Got buckets')
  return new Set(result.Buckets.map(bucket => bucket.Name))
}

export const createBucket = async (name) => {
  console.log('Creating bucket', name)
  const createBucketCommand = new CreateBucketCommand({
    Bucket: name,
    CreateBucketConfiguration: {
      LocationConstraint: 'eu-west-1',
    },
  })
  await s3Client.send(createBucketCommand)
  console.log('Created bucket', name)
}

export const putVersioning =  async (bucket) => {
  const status = process.env.S3_VERSIONING_ENABLED === 'true' ? 'Enabled' : 'Suspended'
  console.log('Setting versioning state for bucket', bucket)
  const putBucketVersioningCommand = new PutBucketVersioningCommand({
    Bucket: bucket,
    VersioningConfiguration: {
      Status: status,
    }
  })
  await s3Client.send(putBucketVersioningCommand)
  console.log(`${status} versioning for bucket`, bucket)
}

export const putNotificationConfiguration = async (bucket, notifications) => {
  console.log('Setting notification configuration for bucket', bucket)
  const notificationConfig = notifications.reduce((acc, [type, name, events, filter]) => {
    switch (type) {
      case 'topic': {
        acc.TopicConfigurations.push({
          TopicArn: topicArn(name),
          Events: events,
          Filter: filter,
        })
        break
      }
      case 'queue': {
        acc.QueueConfigurations.push({
          QueueArn: queueArn(name),
          Events: events,
          Filter: filter,
        })
        break
      }
      case 'lambda': {
        acc.LambdaFunctionConfigurations.push({
          LambdaFunctionArn: lambdaArn(name),
          Events: events,
          Filter: filter,
        })
        break
      }
      default:
    }
    return acc
  }, {
    TopicConfigurations: [],
    QueueConfigurations: [],
    LambdaFunctionConfigurations: [],
  })
  const putBucketNotificationConfigurationCommand = new PutBucketNotificationConfigurationCommand({
    Bucket: bucket,
    NotificationConfiguration: notificationConfig,
  })
  await s3Client.send(putBucketNotificationConfigurationCommand)
  console.log('Set notification configuration for bucket', bucket)
}

export const putObject = async (bucket, key, filePath) => {
  console.log('Putting object', key, 'into', bucket)
  const putObjectCommand = new PutObjectCommand({
    Bucket: bucket,
    Key: key,
    Body: fs.createReadStream(`${mountRepoDir}/${filePath}`),
  })
  await s3Client.send(putObjectCommand)
  console.log('Put object', key, 'into', bucket)
}
