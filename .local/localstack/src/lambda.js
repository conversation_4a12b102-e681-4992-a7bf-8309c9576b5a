import childProcess from 'child_process'
import fs from 'fs/promises'
import { setTimeout } from 'timers/promises'
import pLimit from 'p-limit'
import {
  LambdaClient,
  CreateEventSourceMappingCommand,
  CreateFunctionCommand,
  DeleteFunctionCommand,
  GetFunctionCommand,
  ListEventSourceMappingsCommand,
  ListFunctionsCommand,
  PublishVersionCommand,
  UpdateFunctionCodeCommand,
  UpdateFunctionConfigurationCommand,
} from '@aws-sdk/client-lambda'
import semver from 'semver'
import { iamArn, queueArn, isQueueArn } from './arn.js'

const endpoint = process.env.AWS_ENDPOINT
const mountLambdasDir = '/home/<USER>/repo/lambdas'

const lambdaClient = new LambdaClient({ endpoint })

export const listLambdas = async () => {
  console.log('Getting lambdas')
  const listFunctionsCommand = new ListFunctionsCommand({})
  const result = await lambdaClient.send(listFunctionsCommand)
  console.log('Got lambdas')
  return new Set(result.Functions.map(lambdaFunction => lambdaFunction.FunctionName))
}

const getEventSourceFromArn = (arn) => {
  if (isQueueArn(arn)) {
    return {
      type: 'queue',
      source: arn.split(':').at(-1),
    }
  }
  throw new Error(`Unknown Event Source for arn ${arn}`,)
}

export const listEventSourceMappings = async () => {
  console.log('Getting lambda event source mappings')
  const listEventSourceMappingsCommand = new ListEventSourceMappingsCommand({})
  const result = await lambdaClient.send(listEventSourceMappingsCommand)
  console.log('Got lambda event source mappings')
  return result.EventSourceMappings.map((eventSourceMapping) => {
    const eventSource = getEventSourceFromArn(eventSourceMapping.EventSourceArn)
    return {
      lambda: eventSourceMapping.FunctionArn.split(':').at(-1),
      type: eventSource.type,
      source: eventSource.source,
    }
  })
}

const buildLimiter = pLimit(4)

const buildLambda = name => buildLimiter(() => new Promise((resolve, reject) => {
  console.log('Building lambda', name)
  const startTime = Date.now()
  childProcess.exec(`bash build.sh ${name}`, { cwd: mountLambdasDir }, (error, stdout, stderr) => {
    if (error === null) {
      if (stderr) {
        const realError = stderr.split('\n').reduce((acc, stderrLine) => {
          if (acc) return acc
          const line = stderrLine.trim()
          // Ignore npm update notices but don't suppress real issues
          return line
            && !line.startsWith('npm notice')
            && !line.toLowerCase().startsWith('npm warn deprecated')
            && !line.toLowerCase().startsWith('npm warn ebadengine')
        }, false)

        if (realError) {
          console.log('Error in lambda build script', name)
          reject(new Error(stderr))
          return
        }
      }
      const duration = Date.now() - startTime
      console.log('Built lambda', name, 'in', duration, 'ms')
      resolve()
    } else {
      reject(error)
    }
  })
}))

const shouldLambdaBundle = name => fs.readFile(`${mountLambdasDir}/${name}/package.json`)
  .then(buf => JSON.parse(buf.toString()).bundle === true)

const getLambdaHash = name => fs.readFile(`${mountLambdasDir}/${name}/dist/complete_hash`)
  .then(buf => buf.toString())
  .catch(() => null)

const getLambdaZip = name => fs.readFile(`${mountLambdasDir}/${name}/function.zip`)

const getLambdaEnv = name => fs.readFile(`${mountLambdasDir}/${name}/env.json`)
  .then(buf => JSON.parse(buf.toString()))

const getLambdaRuntime = name => fs.readFile(`${mountLambdasDir}/${name}/package.json`)
  .then(buf => JSON.parse(buf.toString()).engines.node)
  .then(version => semver.major(semver.minVersion(version)))
  .catch(() => semver.major(process.version)) // Default to version that localstack-setup runs on
  .then(major => `nodejs${major}.x`)

const getArchitecture = () => {
  switch (process.arch) {
    case 'arm64':
      return 'arm64'
    default:
      return 'x86_64'
  }
}

const waitForLambdaCreate = async (name) => {
  const command = new GetFunctionCommand({
    FunctionName: name,
  })
  let retryCount = 0
  while (true) {
    const result = await lambdaClient.send(command)
    if (result.Configuration.State === 'Active') {
      break
    }
    if (result.Configuration.State !== 'Pending' || retryCount >= 3) {
      console.log('Lambda not ready yet', name, '-', result.Configuration.State, '-', result.Configuration.StateReason)
    }
    retryCount += 1
    await setTimeout(1000)
  }
}

export const createLambda = async (name) => {
  await buildLambda(name)
  const zip = await getLambdaZip(name)
  const env = await getLambdaEnv(name)
  console.log('Creating lambda', name)
  const createFunctionCommand = new CreateFunctionCommand({
    FunctionName: name,
    Runtime: await getLambdaRuntime(name),
    Role: iamArn('lambda-role'),
    Handler: await shouldLambdaBundle(name) ? 'bundle/main.handler' : 'src/main.handler',
    Code: {
      ZipFile: zip,
    },
    Timeout: 30,
    PackageType: 'Zip',
    Environment: { Variables: env },
    Architectures: [getArchitecture()], // To use correct Docker image architecture
  })
  await lambdaClient.send(createFunctionCommand)
  await waitForLambdaCreate(name)
  console.log('Created lambda', name)
}

const waitForUpdateLambda = async (name) => {
  const command = new GetFunctionCommand({
    FunctionName: name,
  })
  let retryCount = 0
  while (true) {
    const result = await lambdaClient.send(command)
    if (result.Configuration.LastUpdateStatus === 'Successful') {
      break
    }
    if (result.Configuration.LastUpdateStatus !== 'InProgress' || retryCount >= 3) {
      console.log('Lambda not ready yet', name, '-', result.Configuration.LastUpdateStatus, '-', result.Configuration.LastUpdateStatusReason)
    }
    retryCount += 1
    await setTimeout(1000)
  }
}

export const updateLambda = async (name) => {
  const preHash = await getLambdaHash(name)
  await buildLambda(name)
  const postHash = await getLambdaHash(name)
  console.log('Updating lambda', name)

  const env = await getLambdaEnv(name)
  const updateFunctionConfigCommand = new UpdateFunctionConfigurationCommand({
    FunctionName: name,
    Runtime: await getLambdaRuntime(name),
    Handler: await shouldLambdaBundle(name) ? 'bundle/main.handler' : 'src/main.handler',
    Environment: { Variables: env },
  })
  await lambdaClient.send(updateFunctionConfigCommand)

  if (preHash !== postHash) {
    const zip = await getLambdaZip(name)
    console.log('Updating lambda code', name)
    const updateFunctionCodeCommand = new UpdateFunctionCodeCommand({
      FunctionName: name,
      ZipFile: zip,
    })
    await lambdaClient.send(updateFunctionCodeCommand)
    console.log('Updated lambda code', name)
  }

  await waitForUpdateLambda(name)
  console.log('Updated lambda', name)
}

export const publishLambdaVersion = async (name) => {
  console.log('Publishing lambda version', name)
  const publishVersionCommand = new PublishVersionCommand({
    FunctionName: name,
  })
  const result = await lambdaClient.send(publishVersionCommand)
  console.log('Published lambda version', name, result.Version)
  return result.FunctionArn
}

export const deleteLambdaVersion = async (arn) => {
  console.log('Deleting lambda version', arn)
  const deleteFunctionCommand = new DeleteFunctionCommand({
    FunctionName: arn,
  })
  await lambdaClient.send(deleteFunctionCommand)
  console.log('Deleted lambda version', arn)
}

const getEventSourceArn = (type, name) => {
  switch (type) {
    case 'queue': return queueArn(name)
    default: throw new Error(`Unknown event source type ${type} for ${name}`)
  }
}

export const createEventSourceMapping = async (lambdaName, eventSourceType, eventSourceName, batchSize) => {
  const eventSourceArn = getEventSourceArn(eventSourceType, eventSourceName)
  console.log('Creating event source mapping for', lambdaName, 'on', eventSourceName)
  const createEventSourceMappingCommand = new CreateEventSourceMappingCommand({
    FunctionName: lambdaName,
    EventSourceArn: eventSourceArn,
    StartingPosition: 'LATEST',
    BatchSize: batchSize,
    FunctionResponseTypes: batchSize > 1 ? ['ReportBatchItemFailures'] : undefined,
    MaximumBatchingWindowInSeconds: batchSize > 1 ? 5 : undefined,
    ScalingConfig: { MaximumConcurrency: 1 },
  })
  await lambdaClient.send(createEventSourceMappingCommand)
  console.log('Created event source mapping for', lambdaName, 'on', eventSourceName)
}
