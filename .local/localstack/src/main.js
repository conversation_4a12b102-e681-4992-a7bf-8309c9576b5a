import { createApi, createApiDeployment, createApiEndpointIntegration, listApiIntegrations, listApis } from './apiGateway.js'
import { createEventSourceMapping, createLambda, listEventSourceMappings, listLambdas, publishLambdaVersion, updateLambda } from './lambda.js'
import { putSecret } from './secretsManager.js'
import { createBucket, listBuckets, putNotificationConfiguration, putObject, putVersioning } from './s3.js'
import { createTopic, listSubscriptions, listTopics, subscribeLambdaToTopic, subscribeQueueToTopic } from './sns.js'
import { createQueue, listQueues } from './sqs.js'
import { putParameter } from './ssm.js'
import { addVerifiedEmailIdentity } from './ses.js'
import { registerLambda, drainRestate } from './restate.js'

// const region = 'eu-west-1'

/* API Gateway */
const apis = [
]

const apiMap = [
]

/* Lambda */
const lambdas = [
  'restate-runner',
]

const lambdaRestate = [
  'restate-runner',
]

const lambdaEventSourceMap = [
]

/* SecretsManager */
const secrets = [
  ['services-api-key', 'an-api-key-1'],
  ['cookie-secret', 'ThisIsASuperSecretThirtyTwoMinCharSecret'],
]

/* S3 */
const buckets = [
]

const files = [
]

const bucketNotifications = [
]

/* SNS */
const topics = [
]

const lambdaTopicMap = [
]

const queueTopicMap = [
]

/* SQS */
const queues = [
]

/* SSM */
const parameters = [
]

/* SES */
const verifiedEmails = [
  '<EMAIL>',
]

/*
  ----
  Main
  ----
*/
await import('./healthcheck.js')

const existingApis = await listApis()
const existingApiIntegrations = await listApiIntegrations(Array.from(existingApis))
const existingLambdas = await listLambdas()
const existingBuckets = await listBuckets()
const existingTopics = await listTopics()
const existingQueues = await listQueues()
const existingSubscriptions = await listSubscriptions()
const existingEventSourceMappings = await listEventSourceMappings()

const apiPromises = {}
apis.forEach(([id, name]) => {
  if (existingApis.has(id)) {
    apiPromises[id] = Promise.resolve()
  } else {
    apiPromises[id] = createApi(id, name)
  }
})

const lambdaPromises = {}
lambdas.forEach((lambda) => {
  if (existingLambdas.has(lambda)) {
    lambdaPromises[lambda] = updateLambda(lambda)
  } else {
    lambdaPromises[lambda] = createLambda(lambda)
  }
})

const lambdaRestatePromises = []
lambdaRestate.forEach((lambda) => {
  lambdaRestatePromises.push(
    lambdaPromises[lambda]
      .then(() => publishLambdaVersion(lambda))
      .then(() => registerLambda(lambda))
  )
})

const restateDrainPromises = [Promise.allSettled(lambdaRestatePromises).then(() => drainRestate())]

const bucketPromises = {}
buckets.forEach((bucket) => {
  if (existingBuckets.has(bucket)) {
    bucketPromises[bucket] = Promise.resolve()
  } else {
    bucketPromises[bucket] = createBucket(bucket)
  }
})

const bucketVersioningPromises = []
buckets.forEach(bucket => {
  bucketVersioningPromises.push(bucketPromises[bucket].then(() => putVersioning(bucket)))
})

const topicPromises = {}
topics.forEach((topic) => {
  if (existingTopics.has(topic)) {
    topicPromises[topic] = Promise.resolve()
  } else {
    topicPromises[topic] = createTopic(topic)
  }
})

const queuePromises = {}
queues.forEach((queue) => {
  if (existingQueues.has(queue)) {
    queuePromises[queue] = Promise.resolve()
  } else {
    queuePromises[queue] = createQueue(queue)
  }
})

const secretPromises = []
secrets.forEach(([secretId, value]) => {
  secretPromises.push(putSecret(secretId, value))
})

const parameterPromises = []
parameters.forEach(([name, value, secure]) => {
  parameterPromises.push(putParameter(name, value, secure))
})

const filePromises = []
files.forEach(([bucket, key, filePath]) => {
  filePromises.push(bucketPromises[bucket].then(() => putObject(bucket, key, filePath)))
})

const sesPromises = []
verifiedEmails.forEach((id) => {
  sesPromises.push(addVerifiedEmailIdentity(id))
})

const deploymentsRequired = new Set()

const apiIntegrationPromises = apis.reduce((acc, [apiId]) => { acc[apiId] = []; return acc }, {})
apiMap.forEach(([apiId, method, endpointParts, details]) => {
  const endpoint = '/' + endpointParts.join('/')
  if (!existingApiIntegrations[apiId]?.paths[endpoint]?.includes(method)) {
    const prePromises = [
      apiPromises[apiId],
      ...(details.lambda ? [lambdaPromises[details.lambda]] : []),
    ]
    apiIntegrationPromises[apiId].push(Promise.all(prePromises).then(() => createApiEndpointIntegration(apiId, method, endpointParts, details)))
    deploymentsRequired.add(apiId)
  }
})

const apiDeploymentPromises = []
deploymentsRequired.forEach((apiId) => {
  const prePromises = apiIntegrationPromises[apiId].concat(apiPromises[apiId])
  apiDeploymentPromises.push(Promise.all(prePromises).then(() => createApiDeployment(apiId)))
})

const bucketNotificationPromises = []
bucketNotifications.forEach(([bucket, notifications]) => {
  const prePromises = notifications.map(([type, name]) => {
    switch (type) {
      case 'topic': return topicPromises[name]
      case 'queue': return queuePromises[name]
      case 'lambda': return lambdaPromises[name]
      default: return Promise.reject(`Unknown bucket notification type ${type} for ${name}`)
    }
  })
  prePromises.push(bucketPromises[bucket])
  bucketNotificationPromises.push(Promise.all(prePromises).then(() => putNotificationConfiguration(bucket, notifications)))
})

const lambdaTopicPromises = []
lambdaTopicMap.forEach(([lambda, topic]) => {
  if (!existingSubscriptions.some(sub => sub.lambda === lambda && sub.topic === topic)) {
    const prePromises = [
      lambdaPromises[lambda],
      topicPromises[topic],
    ]
    lambdaTopicPromises.push(Promise.all(prePromises).then(() => subscribeLambdaToTopic(lambda, topic)))
  }
})

const queueTopicPromises = []
queueTopicMap.forEach(([queue, topic]) => {
  if (!existingSubscriptions.some(sub => sub.queue === queue && sub.topic === topic)) {
    const prePromises = [
      queuePromises[queue],
      topicPromises[topic],
    ]
    queueTopicPromises.push(Promise.all(prePromises).then(() => subscribeQueueToTopic(queue, topic)))
  }
})

const lambdaEventSourcePromises = []
lambdaEventSourceMap.forEach(([lambda, type, source, batchSize]) => {
  if (!existingEventSourceMappings.some(eventSourceMap => eventSourceMap.lambda === lambda && eventSourceMap.type === type && eventSourceMap.source === source)) {
    const prePromises = [lambdaPromises[lambda]]
    switch (type) {
      case 'queue': {
        prePromises.push(queuePromises[source])
        break
      }
      default: {
        prePromises.push(Promise.reject(`Unknown lambda event source type ${type} for ${source}`))
      }
    }
    lambdaEventSourcePromises.push(Promise.all(prePromises).then(() => createEventSourceMapping(lambda, type, source, batchSize)))
  }
})

console.log('Waiting for setup to complete')
await Promise.all([
  Promise.allSettled(Object.values(apiPromises)),
  Promise.allSettled(Object.values(lambdaPromises)),
  Promise.allSettled(restateDrainPromises),
  Promise.allSettled(Object.values(bucketPromises)),
  Promise.allSettled(bucketVersioningPromises),
  Promise.allSettled(Object.values(topicPromises)),
  Promise.allSettled(Object.values(queuePromises)),
  Promise.allSettled(secretPromises),
  Promise.allSettled(parameterPromises),
  Promise.allSettled(filePromises),
  Promise.allSettled(sesPromises),
  Promise.allSettled(Object.values(apiIntegrationPromises).reduce((acc, arr) => acc.concat(arr), [])),
  Promise.allSettled(apiDeploymentPromises),
  Promise.allSettled(bucketNotificationPromises),
  Promise.allSettled(lambdaTopicPromises),
  Promise.allSettled(lambdaEventSourcePromises),
  Promise.allSettled(queueTopicPromises),
]).then((allResults) => {
  for (let i = 0; i < allResults.length; i += 1) {
    const results = allResults[i]
    for (let j = 0; j < results.length; j += 1) {
      const result = results[j]
      if (result.status === 'rejected') throw result.reason
    }
  }
})
console.log('Done!')
