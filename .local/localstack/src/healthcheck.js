import http from 'http'

const endpoint = new URL(process.env.AWS_ENDPOINT)

const localstackHealthcheck = async () => new Promise((resolve, reject) => {
  http.get({
    hostname: endpoint.hostname,
    port: endpoint.port,
    path: '/_localstack/init',
  }, (res) => {
    const chunks = []
    res.on('data', (chunk) => {
      chunks.push(chunk)
    })

    res.on('end', () => {
      resolve(JSON.parse(Buffer.concat(chunks).toString()).completed.READY === true)
    })
  }).on('error', (error) => {
    if (error.code === 'ECONNREFUSED') {
      resolve(false)
    } else {
      reject(error)
    }
  })
})

const sleep = ms => new Promise(resolve => setTimeout(resolve, ms))

while (true) {
  const ready = await localstackHealthcheck()
  if (ready) break
  console.log('Localstack loading...')
  await sleep(1000)
}
console.log('Localstack ready')
