services:
  localstack-setup-complete:
    depends_on:
      localstack-setup:
        condition: service_completed_successfully
    image: alpine:3.22
    command: ["sleep", "1"]

  localstack-setup:
    depends_on:
      localstack:
        condition: service_started
    build:
      context: ./localstack
    command: ["node", "src/main.js"]
    volumes:
      - ~/.aws:/home/<USER>/.aws
      - ${PWD}/..:/home/<USER>/repo
      - ${PWD}/localstack/src:/home/<USER>/app/src
    environment:
      AWS_ACCESS_KEY_ID: localstack
      AWS_ENDPOINT: http://localstack:4566
      AWS_REGION: eu-west-1
      AWS_SECRET_ACCESS_KEY: localstack
      NODE_AUTH_TOKEN: ${GH_NPM_TOKEN}
      S3_VERSIONING_ENABLED: "true"
