#!/bin/bash

# This script sets up just localstack
# Run from the root of the repo with `sh .local/localstack.sh`

cd $( cd "$(dirname "${BASH_SOURCE[0]}")" ; pwd -P )

export COMPOSE_IGNORE_ORPHANS=True

# Start containers
docker compose -f infra.yaml -f localstack/setup.yaml -p sanmf up -d

# Shim to avoid race condition
docker wait sanmf-localstack-setup-complete-1 > /dev/null

# Remove setup containers
docker compose -f infra.yaml -f localstack/setup.yaml -p sanmf rm --volumes --force
