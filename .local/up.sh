#!/bin/bash

# This script sets up the local environment
# Run from the root of the repo with `sh .local/up.sh`

cd $( cd "$(dirname "${BASH_SOURCE[0]}")" ; pwd -P )

# Start containers
docker compose \
  -f infra.yaml \
  -f localstack/setup.yaml \
  -f postgres/setup.yaml \
  -f services.yaml \
  -p sanmf \
  up -d --build

# Shim to avoid race condition
docker wait sanmf-localstack-setup-complete-1 sanmf-postgres-setup-complete-1 > /dev/null

# Remove setup containers
docker compose \
  -f infra.yaml \
  -f localstack/setup.yaml \
  -f postgres/setup.yaml \
  -p sanmf \
  rm --volumes --force
