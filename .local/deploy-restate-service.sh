#!/bin/bash

cd $( cd "$(dirname "${BASH_SOURCE[0]}")" ; pwd -P )

export COMPOSE_IGNORE_ORPHANS=True

docker compose -p sanmf -f services.yaml up --build -d $1

if [[ $2 == "register" ]]; then
  curl -w "\n" http://localhost:26006/deployments -H 'content-type: application/json' -d "{\"uri\": \"http://$1:9080\", \"force\": true}"
  cd ../restate-services/$1
  if [[ -f "post-deploy.sh" ]]; then
    export RESTATEURL="http://localhost:26006"
    ./post-deploy.sh
  fi
else
  echo "Did not re-register service with Restate"
fi
