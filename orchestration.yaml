---
skip:

infra:
  terraform:
    - 'infra/**'
    - 'lambdas/**'

migrations-db:
  # Make sure the key is the same value as the k8s/<KEY> + db/<KEY>
  scuk-db:
    - 'db/scuk-db/**'
    - 'k8s/scuk-db/**'

lambdas:
  # Make sure the key is the same value as the lambdas/<KEY>
  restate-runner:
    - 'lambdas/restate-runner/src/**'
    - 'lambdas/restate-runner/.npmrc'
    - 'lambdas/restate-runner/eslint.config.js'
    - 'lambdas/restate-runner/package*.json'
    - 'lambdas/restate-runner/tsconfig.json'

services:
  # Make sure the key is the same value as the k8s/<KEY> + services/<KEY>
  claims-portal:
    - 'k8s/claims-portal/**'
    - 'services/claims-portal/.dockerignore'
    - 'services/claims-portal/Dockerfile'
    - 'services/claims-portal/api/src/**'
    - 'services/claims-portal/api/.env-cmdrc.json'
    - 'services/claims-portal/api/.npmrc'
    - 'services/claims-portal/api/eslint.config.js'
    - 'services/claims-portal/api/package*.json'
    - 'services/claims-portal/webapp/src/**'
    - 'services/claims-portal/webapp/.eslintrc'
    - 'services/claims-portal/webapp/.npmrc'
    - 'services/claims-portal/webapp/babel.config.json'
    - 'services/claims-portal/webapp/package*.json'
    - 'services/claims-portal/webapp/webpack.common.js'
    - 'services/claims-portal/webapp/webpack.prod.js'

restate-services:
  # Make sure the key is the same value as the k8s/<KEY> + restate-services/<KEY>
