{{/*
Expand the name of the chart.
*/}}
{{- define "data.get" -}}
    {{- $environment := index . 0 }}
    {{- $key := index . 1 }}
    {{- $test_1 := dict
        "account_id" "************"
        "domain" "test-1.scf.kpmgcdd-fincrime.com"
        "is_prod_env" false
        "scheduled" true
        "log_retention_days" 90
        "backup_retention_days" 10
        "vpc_cidr" "**********/19"
        "nat_ip" "**************"
        "eks_cluster_oidc_arn" "arn:aws:iam::************:oidc-provider/oidc.eks.eu-west-2.amazonaws.com/id/CF4A5B6F058A938FB2156FAE390F2EB6"
    }}
    {{- $envs_config := dict
        "test-1" $test_1
    }}
    {{- get (get $envs_config $environment | default (dict $key "unknown-env") ) $key | default "unknown-key" }}
{{- end }}

{{- define "data.project" -}}
    scf
{{- end }}

{{- define "data.environment" -}}
    {{ .Values.global.environment }}
{{- end }}
{{- define "data.account_id" -}}
    {{- include "data.get" (list .Values.global.environment "account_id") }}
{{- end }}
{{- define "data.domain" -}}
    {{- include "data.get" (list .Values.global.environment "domain") }}
{{- end }}
{{- define "data.is_prod_env" -}}
    {{- include "data.get" (list .Values.global.environment "is_prod_env") }}
{{- end }}
{{- define "data.scheduled" -}}
    {{- include "data.get" (list .Values.global.environment "scheduled") }}
{{- end }}
{{- define "data.log_retention_days" -}}
    {{- include "data.get" (list .Values.global.environment "log_retention_days") }}
{{- end }}
{{- define "data.backup_retention_days" -}}
    {{- include "data.get" (list .Values.global.environment "backup_retention_days") }}
{{- end }}
{{- define "data.vpc_cidr" -}}
    {{- include "data.get" (list .Values.global.environment "vpc_cidr") }}
{{- end }}
{{- define "data.nat_ip" -}}
    {{- include "data.get" (list .Values.global.environment "nat_ip") }}
{{- end }}
{{- define "data.eks_cluster_oidc_arn" -}}
    {{- include "data.get" (list .Values.global.environment "eks_cluster_oidc_arn") }}
{{- end }}

{{- define "data.kpmgips" -}}
    {{- $kpmgips := list
        "**************/32"
        "**************/32"
        "*************/26"
        "*************/32"
        "***************/32"
        "*************/24"}}
    {{- join "," $kpmgips }}
{{- end }}
{{- define "data.custips" -}}
    {{- $custips := list
        "127.0.0.1/32"
    }}
    {{- join "," $custips }}
{{- end }}