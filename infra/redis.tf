module "redis" {
  for_each = local.redis_instances
  source   = "git::https://github.com/KPMG-UK/terraform-aws-elasticache-redis.git/?ref=tf13-3.0.0-upgrade"

  name              = "${var.environment}-${each.key}"
  environment       = var.environment
  redis_clusters    = 1
  vpc_id            = module.pdata.vpc_id
  allowed_cidr      = module.pdata.private_subnet_cidr_blocks
  redis_version     = lookup(each.value, "version", "7.1")
  subnets           = module.pdata.private_subnet_ids
  apply_immediately = true
}
