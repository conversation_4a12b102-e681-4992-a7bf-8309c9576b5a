locals {
  db_admin_username = "myadmin"
  db_secret_names = {
    for k in local.db_instance_names : k => aws_secretsmanager_secret.dbs_secret[k].name
  }
}
resource "random_password" "dbs_random_string" {
  for_each         = local.db_instance_names
  length           = 30
  lower            = true
  numeric          = true
  special          = true
  upper            = true
  override_special = "$^&*()_-+={}[]<>,.;"
}

resource "aws_secretsmanager_secret" "dbs_secret" {
  for_each = local.db_instance_names
  #checkov:skip=CKV2_AWS_57: Autorotate requires password update on DB TODO
  name                    = "db/${var.environment}-${each.key}"
  description             = "DB link"
  kms_key_id              = module.pdata.kms_arn
  recovery_window_in_days = 7
  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_security_group" "dbs" {
  for_each    = local.db_instance_names
  name        = "${var.environment}-${each.key}-db"
  vpc_id      = module.pdata.vpc_id
  description = "DB sg for ${each.key}"

  ingress {
    description     = "Access to postgres DB from services"
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [data.aws_security_group.cluster_sg.id]
  }
  ingress {
    description = "Access from self"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    self        = true
  }
  egress {
    description = "Access to self DB"
    from_port   = 5432
    to_port     = 5432
    protocol    = "tcp"
    self        = true
  }
  tags = {
    Name = "${var.environment}-${each.key}-db"
  }
}
resource "aws_security_group" "dbs_accessor" {
  for_each    = local.db_instance_names
  name        = "${var.environment}-${each.key}-db-accessors"
  vpc_id      = module.pdata.vpc_id
  description = "sg to access ${each.key} db"

  tags = {
    Name = "${var.environment}-${each.key}-accessors"
  }
}
resource "aws_iam_role" "rds_monitoring_role" {
  name = "${var.environment}-rds-monitoring-role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Sid    = ""
      Principal = {
        Service = [
          "monitoring.rds.amazonaws.com"
        ]
      }
    }]
  })

  inline_policy {
    name = "rdsmonitoring"

    policy = jsonencode({
      "Version" : "2012-10-17",
      "Statement" : [
        {
          "Sid" : "EnableCreationAndManagementOfRDSCloudwatchLogGroups",
          "Effect" : "Allow",
          "Action" : [
            "logs:CreateLogGroup",
            "logs:PutRetentionPolicy"
          ],
          "Resource" : [
            "arn:aws:logs:*:*:log-group:RDS*"
          ]
        },
        {
          "Sid" : "EnableCreationAndManagementOfRDSCloudwatchLogStreams",
          "Effect" : "Allow",
          "Action" : [
            "logs:CreateLogStream",
            "logs:PutLogEvents",
            "logs:DescribeLogStreams",
            "logs:GetLogEvents"
          ],
          "Resource" : [
            "arn:aws:logs:*:*:log-group:RDS*:log-stream:*"
          ]
        }
      ]
    })
  }
}

resource "aws_lambda_invocation" "db_autoroles" {
  for_each      = local.aws_db_infos
  function_name = data.aws_lambda_function.db_autoroles.arn
  input = jsonencode({
    "secretname" : aws_secretsmanager_secret.dbs_secret[each.key].name,
    "userdetails" : [
      {
        "name" : "readwrite",
        "privileges" : [{ "preset" : "readwrite" }],
        "haslogin" : false
      },
      {
        "name" : "readonly",
        "privileges" : [{ "preset" : "readonly" }],
        "haslogin" : false
      },
      {
        "name" : each.value.service_username,
        "iamauth" : true,
        "roles" : ["readwrite"]
      },
      {
        "name" : each.value.service_read_username,
        "iamauth" : true,
        "roles" : ["readonly"]
      },
    ]
  })
  depends_on = [aws_db_instance.dbs, aws_secretsmanager_secret_version.dbs_secret_val]
}