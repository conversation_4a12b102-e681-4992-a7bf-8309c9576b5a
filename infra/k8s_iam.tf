# IAM
resource "aws_iam_role" "k8s_service" {
  for_each = local.iam_services
  name     = "k8s-${var.environment}-${local.repo_namespace}-${each.key}"
  assume_role_policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Allow",
        "Principal" : {
          "Federated" : module.pdata.eks_cluster_oidc_arn
        },
        "Action" : "sts:AssumeRoleWithWebIdentity",
        "Condition" : {
          "StringEquals" : {
            "${replace(data.aws_iam_openid_connect_provider.eks_cluster_oidc.url, "https://", "")}:sub" : "system:serviceaccount:${module.pdata.eks_cluster_microservices_namespace}:${local.repo_namespace}-${each.key}"
            "${replace(data.aws_iam_openid_connect_provider.eks_cluster_oidc.url, "https://", "")}:aud" : "sts.amazonaws.com"
          }
        }
      }
    ]
  })
  tags = {
    "ServiceAccountName"      = "${local.repo_namespace}-${each.key}"
    "ServiceAccountNameSpace" = module.pdata.eks_cluster_microservices_namespace
  }
}

resource "kubernetes_service_account_v1" "service" {
  for_each = aws_iam_role.k8s_service
  metadata {
    name      = replace(each.value.name, "k8s-${var.environment}-", "")
    namespace = module.pdata.eks_cluster_microservices_namespace
    annotations = {
      "eks.amazonaws.com/role-arn" = each.value.arn
    }
  }
  automount_service_account_token = true
}

## Attach policies
resource "aws_iam_policy" "service" {
  for_each    = local.iam_services
  name        = "k8s-${var.environment}-${local.repo_namespace}-${each.key}"
  path        = "/"
  description = "k8s-${var.environment}-${local.repo_namespace}-${each.key}"
  policy = jsonencode({
    Version = "2012-10-17" #tfsec:ignore:aws-iam-no-policy-wildcards
    Statement = concat([
      {
        Action = [
          "xray:PutTraceSegments",
          "xray:PutTelemetryRecords",
          "xray:GetSamplingRules",
          "xray:GetSamplingTargets",
          "xray:GetSamplingStatisticSummaries"
        ]
        Effect   = "Allow"
        Resource = "*"
      },
      ],
      length(each.value) > 0 ?
      [{
        Action = [
          "kms:Decrypt",
          "kms:GenerateDataKey",
        ]
        Effect   = "Allow"
        Resource = module.pdata.kms_arn
      }] : [],
      each.value
    )
  })
}

resource "aws_iam_role_policy_attachment" "service" {
  for_each   = local.iam_services
  role       = aws_iam_role.k8s_service[each.key].name
  policy_arn = aws_iam_policy.service[each.key].arn
}
