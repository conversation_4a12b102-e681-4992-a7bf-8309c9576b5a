variable "environment" {
  type        = string
  description = "Environment name to apply to"
}
variable "create_ecr" {
  type    = bool
  default = false
}
variable "argocd_auto_sync" {
  type        = bool
  default     = true
  description = "Wether or not the application should be automatically synced"
}
variable "argocd_sync_branch" {
  type        = string
  default     = ""
  description = "The branch argocd sync with for CD"
}
variable "branded_domain" {
  type        = string
  default     = ""
  description = "List of service names to not deploy"
}
#### Start Engineering Area ####
variable "skip_services" {
  type        = list(string)
  default     = []
  description = "List of service names to not deploy"
}
variable "skip_lambdas" {
  type        = list(string)
  default     = []
  description = "List of service names to not deploy"
}
variable "service_extra_configs" {
  type        = any
  default     = {}
  description = "Merge with service configs"
}
variable "service_extra_secrets" {
  type        = any
  default     = {}
  description = "Merge with service secrets"
}
variable "service_dbs" {
  type        = any
  default     = {}
  description = "Override Services db settings"
}
variable "service_redis" {
  type        = any
  default     = {}
  description = "Override Services redis settings"
}
variable "service_s3" {
  type        = any
  default     = {}
  description = "Override Services s3 settings"
}

variable "service_sns" {
  type        = any
  default     = {}
  description = "Override Services SNS settings"
}

variable "service_sqs" {
  type        = any
  default     = {}
  description = "Override Services SQS settings"
}
#### End Engineering Area ####

variable "db_default_settings" {
  type = any
  default = {
    allocated_storage       = 20
    max_allocated_storage   = 20
    engine_version          = "17.4"
    aurora_engine_version   = "17.4"
    instance_class          = "db.t4g.small"
    backup_retention_period = 2
    db_name                 = "postgres"
    ca_cert_name            = "rds-ca-rsa2048-g1"
  }
}
variable "s3_default_settings" {
  type = object({
    lifecycle_transition_oia_days        = number
    lifecycle_noncurrent_expiration_days = number
  })
  default = {
    lifecycle_transition_oia_days        = 30
    lifecycle_noncurrent_expiration_days = 90
  }
}
variable "sqs_default_settings" {
  type = object({
    delay_seconds                  = number
    max_message_size               = number
    message_retention_seconds      = number
    receive_wait_time_seconds      = number
    visibility_timeout_seconds     = number
    dlq_delay_seconds              = number
    dlq_max_message_size           = number
    dlq_message_retention_seconds  = number
    dlq_receive_wait_time_seconds  = number
    dlq_visibility_timeout_seconds = number
  })
  default = {
    delay_seconds                  = 0
    max_message_size               = 262144  # 256 KiB
    message_retention_seconds      = 1209600 # 14 days
    receive_wait_time_seconds      = 0
    visibility_timeout_seconds     = 30
    dlq_delay_seconds              = 0
    dlq_max_message_size           = 262144  # 256 KiB
    dlq_message_retention_seconds  = 1209600 # 14 days
    dlq_receive_wait_time_seconds  = 0
    dlq_visibility_timeout_seconds = 30
  }
}
variable "lambda_default_settings" {
  type = object({
    timeout     = number
    runtime     = string
    memory_size = number
  })
  default = {
    timeout     = 10
    runtime     = "nodejs22.x"
    memory_size = 512
  }
}