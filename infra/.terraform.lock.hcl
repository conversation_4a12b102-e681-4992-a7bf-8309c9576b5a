# This file is maintained automatically by "terraform init".
# Manual edits may be lost in future updates.

provider "registry.terraform.io/argoproj-labs/argocd" {
  version     = "7.5.2"
  constraints = ">= 7.0.3"
  hashes = [
    "h1:xbwAklebCnLZQaxpblok7ttVKl527heij5sWFBuIdg4=",
    "zh:046c334c73b0faac86d95a3cffb8dc8d7d03281befa0639cb41b21d66b9667af",
    "zh:04d30a2532e7fcdac4dce4cebd37c173b0e71a96111f8a76514d7a2afb3c1a13",
    "zh:0c43e43f517a2933f6cad53c9626509b46c7a9805f58743e810d56b62856b123",
    "zh:2d5af869bfeb8e783ccbe6f985b5e0d1c7fa0af198969f720c2fd5f5c31d6354",
    "zh:40f2276e4b6a966ff24a29f694ada09d0a0812836c43759634d80fa3d4dbcaaa",
    "zh:462925dd757dea1668440f289d2f1a02d304413e34fa3fb7468ea75b52c3ccbf",
    "zh:6a1b382c987040dc718a3f3bfec4b33e9621b28c0fceef013a2290b4623efb04",
    "zh:a812b1b7606f7f1b042eafe143fd8c2453df8029d9c2bb8b5d5862a256544e7c",
    "zh:a957973272ac65463def58444beaf393d33851818d5c71bca9b55ab753eec49f",
    "zh:f8e83fe69415bf3a22246c83dbdd9b9b0940734275beecf7ae309ec3126af4ab",
  ]
}

provider "registry.terraform.io/hashicorp/aws" {
  version     = "5.94.1"
  constraints = ">= 4.0.0, >= 5.32.0, >= 5.42.0, >= 5.92.0"
  hashes = [
    "h1:dYdnGlaCJONFyGk/t3Y4iJzQ8EiJr2DaDdZ/2JV5PZU=",
    "zh:14fb41e50219660d5f02b977e6f786d8ce78766cce8c2f6b8131411b087ae945",
    "zh:3bc5d12acd5e1a5f1cf78a7f05d0d63f988b57485e7d20c47e80a0b723a99d26",
    "zh:4835e49377f80a37c6191a092f636e227a9f086e3cc3f0c9e1b554da8793cfe8",
    "zh:605971275adae25096dca30a94e29931039133c667c1d9b38778a09594312964",
    "zh:8ae46b4a9a67815facf59da0c56d74ef71bcc77ae79e8bfbac504fa43f267f8e",
    "zh:913f3f371c3e6d1f040d6284406204b049977c13cb75aae71edb0ef8361da7dd",
    "zh:91f85ae8c73932547ad7139ce0b047a6a7c7be2fd944e51db13231cc80ce6d8e",
    "zh:96352ae4323ce137903b9fe879941f894a3ce9ef30df1018a0f29f285a448793",
    "zh:9b12af85486a96aedd8d7984b0ff811a4b42e3d88dad1a3fb4c0b580d04fa425",
    "zh:9b51922c9201b1dc3d05b39f9972715db5f67297deee088793d02dea1832564b",
    "zh:a689e82112aa71e15647b06502d5b585980cd9002c3cc8458f092e8c8a667696",
    "zh:c3723fa3e6aff3c1cc0088bdcb1edee168fe60020f2f77161d135bf473f45ab2",
    "zh:d6a2052b864dd394b01ad1bae32d0a7d257940ee47908d02df7fa7873981d619",
    "zh:dda4c9c0406cc54ad8ee4f19173a32de7c6e73abb5a948ea0f342d567df26a1d",
    "zh:f42e0fe592b97cbdf70612f0fbe2bab851835e2d1aaf8cbb87c3ab0f2c96bb27",
  ]
}

provider "registry.terraform.io/hashicorp/external" {
  version     = "2.3.4"
  constraints = ">= 1.0.0"
  hashes = [
    "h1:cCabxnWQ5fX1lS7ZqgUzsvWmKZw9FA7NRxAZ94vcTcc=",
    "zh:037fd82cd86227359bc010672cd174235e2d337601d4686f526d0f53c87447cb",
    "zh:0ea1db63d6173d01f2fa8eb8989f0809a55135a0d8d424b08ba5dabad73095fa",
    "zh:17a4d0a306566f2e45778fbac48744b6fd9c958aaa359e79f144c6358cb93af0",
    "zh:298e5408ab17fd2e90d2cd6d406c6d02344fe610de5b7dae943a58b958e76691",
    "zh:38ecfd29ee0785fd93164812dcbe0664ebbe5417473f3b2658087ca5a0286ecb",
    "zh:59f6a6f31acf66f4ea3667a555a70eba5d406c6e6d93c2c641b81d63261eeace",
    "zh:78d5eefdd9e494defcb3c68d282b8f96630502cac21d1ea161f53cfe9bb483b3",
    "zh:ad0279dfd09d713db0c18469f585e58d04748ca72d9ada83883492e0dd13bd58",
    "zh:c69f66fd21f5e2c8ecf7ca68d9091c40f19ad913aef21e3ce23836e91b8cbb5f",
    "zh:d4a56f8c48aa86fc8e0c233d56850f5783f322d6336f3bf1916e293246b6b5d4",
    "zh:f2b394ebd4af33f343835517e80fc876f79361f4688220833bc3c77655dd2202",
    "zh:f31982f29f12834e5d21e010856eddd19d59cd8f449adf470655bfd19354377e",
  ]
}

provider "registry.terraform.io/hashicorp/kubernetes" {
  version     = "2.36.0"
  constraints = ">= 2.20.0"
  hashes = [
    "h1:94wlXkBzfXwyLVuJVhMdzK+VGjFnMjdmFkYhQ1RUFhI=",
    "zh:07f38fcb7578984a3e2c8cf0397c880f6b3eb2a722a120a08a634a607ea495ca",
    "zh:1adde61769c50dbb799d8bf8bfd5c8c504a37017dfd06c7820f82bcf44ca0d39",
    "zh:39707f23ab58fd0e686967c0f973c0f5a39c14d6ccfc757f97c345fdd0cd4624",
    "zh:4cc3dc2b5d06cc22d1c734f7162b0a8fdc61990ff9efb64e59412d65a7ccc92a",
    "zh:8382dcb82ba7303715b5e67939e07dd1c8ecddbe01d12f39b82b2b7d7357e1d9",
    "zh:88e8e4f90034186b8bfdea1b8d394621cbc46a064ff2418027e6dba6807d5227",
    "zh:a6276a75ad170f76d88263fdb5f9558998bf3a3f7650d7bd3387b396410e59f3",
    "zh:bc816c7e0606e5df98a0c7634b240bb0c8100c3107b8b17b554af702edc6a0c5",
    "zh:cb2f31d58f37020e840af52755c18afd1f09a833c4903ac59270ab440fab57b7",
    "zh:ee0d103b8d0089fb1918311683110b4492a9346f0471b136af46d3b019576b22",
    "zh:f569b65999264a9416862bca5cd2a6177d94ccb0424f3a4ef424428912b9cb3c",
    "zh:f688b9ec761721e401f6859c19c083e3be20a650426f4747cd359cdc079d212a",
  ]
}

provider "registry.terraform.io/hashicorp/local" {
  version     = "2.5.2"
  constraints = ">= 1.0.0"
  hashes = [
    "h1:IyFbOIO6mhikFNL/2h1iZJ6kyN3U00jgkpCLUCThAfE=",
    "zh:136299545178ce281c56f36965bf91c35407c11897f7082b3b983d86cb79b511",
    "zh:3b4486858aa9cb8163378722b642c57c529b6c64bfbfc9461d940a84cd66ebea",
    "zh:4855ee628ead847741aa4f4fc9bed50cfdbf197f2912775dd9fe7bc43fa077c0",
    "zh:4b8cd2583d1edcac4011caafe8afb7a95e8110a607a1d5fb87d921178074a69b",
    "zh:52084ddaff8c8cd3f9e7bcb7ce4dc1eab00602912c96da43c29b4762dc376038",
    "zh:71562d330d3f92d79b2952ffdda0dad167e952e46200c767dd30c6af8d7c0ed3",
    "zh:78d5eefdd9e494defcb3c68d282b8f96630502cac21d1ea161f53cfe9bb483b3",
    "zh:805f81ade06ff68fa8b908d31892eaed5c180ae031c77ad35f82cb7a74b97cf4",
    "zh:8b6b3ebeaaa8e38dd04e56996abe80db9be6f4c1df75ac3cccc77642899bd464",
    "zh:ad07750576b99248037b897de71113cc19b1a8d0bc235eb99173cc83d0de3b1b",
    "zh:b9f1c3bfadb74068f5c205292badb0661e17ac05eb23bfe8bd809691e4583d0e",
    "zh:cc4cbcd67414fefb111c1bf7ab0bc4beb8c0b553d01719ad17de9a047adff4d1",
  ]
}

provider "registry.terraform.io/hashicorp/null" {
  version     = "3.2.3"
  constraints = ">= 2.0.0"
  hashes = [
    "h1:I0Um8UkrMUb81Fxq/dxbr3HLP2cecTH2WMJiwKSrwQY=",
    "zh:22d062e5278d872fe7aed834f5577ba0a5afe34a3bdac2b81f828d8d3e6706d2",
    "zh:23dead00493ad863729495dc212fd6c29b8293e707b055ce5ba21ee453ce552d",
    "zh:28299accf21763ca1ca144d8f660688d7c2ad0b105b7202554ca60b02a3856d3",
    "zh:55c9e8a9ac25a7652df8c51a8a9a422bd67d784061b1de2dc9fe6c3cb4e77f2f",
    "zh:756586535d11698a216291c06b9ed8a5cc6a4ec43eee1ee09ecd5c6a9e297ac1",
    "zh:78d5eefdd9e494defcb3c68d282b8f96630502cac21d1ea161f53cfe9bb483b3",
    "zh:9d5eea62fdb587eeb96a8c4d782459f4e6b73baeece4d04b4a40e44faaee9301",
    "zh:a6355f596a3fb8fc85c2fb054ab14e722991533f87f928e7169a486462c74670",
    "zh:b5a65a789cff4ada58a5baffc76cb9767dc26ec6b45c00d2ec8b1b027f6db4ed",
    "zh:db5ab669cf11d0e9f81dc380a6fdfcac437aea3d69109c7aef1a5426639d2d65",
    "zh:de655d251c470197bcbb5ac45d289595295acb8f829f6c781d4a75c8c8b7c7dd",
    "zh:f5c68199f2e6076bce92a12230434782bf768103a427e9bb9abee99b116af7b5",
  ]
}

provider "registry.terraform.io/hashicorp/random" {
  version = "3.7.1"
  hashes = [
    "h1:t152MY0tQH4a8fLzTtEWx70ITd3azVOrFDn/pQblbto=",
    "zh:3193b89b43bf5805493e290374cdda5132578de6535f8009547c8b5d7a351585",
    "zh:3218320de4be943e5812ed3de995946056db86eb8d03aa3f074e0c7316599bef",
    "zh:419861805a37fa443e7d63b69fb3279926ccf98a79d256c422d5d82f0f387d1d",
    "zh:4df9bd9d839b8fc11a3b8098a604b9b46e2235eb65ef15f4432bde0e175f9ca6",
    "zh:5814be3f9c9cc39d2955d6f083bae793050d75c572e70ca11ccceb5517ced6b1",
    "zh:63c6548a06de1231c8ee5570e42ca09c4b3db336578ded39b938f2156f06dd2e",
    "zh:697e434c6bdee0502cc3deb098263b8dcd63948e8a96d61722811628dce2eba1",
    "zh:78d5eefdd9e494defcb3c68d282b8f96630502cac21d1ea161f53cfe9bb483b3",
    "zh:a0b8e44927e6327852bbfdc9d408d802569367f1e22a95bcdd7181b1c3b07601",
    "zh:b7d3af018683ef22794eea9c218bc72d7c35a2b3ede9233b69653b3c782ee436",
    "zh:d63b911d618a6fe446c65bfc21e793a7663e934b2fef833d42d3ccd38dd8d68d",
    "zh:fa985cd0b11e6d651f47cff3055f0a9fd085ec190b6dbe99bf5448174434cdea",
  ]
}

provider "registry.terraform.io/hashicorp/vault" {
  version = "4.7.0"
  hashes = [
    "h1:6LHoYgrQI2EcqqtrvdbrCY7KqSc5HwWXKBVN0k5wIdc=",
    "zh:1c6012ea079fdfab4c8fb956fafedbfeeb1935958474e8e4138908ac7326d3ed",
    "zh:381e8a33e2147d538cdd598d0e646c04b335e6e5218b1433b4a68c7e833c62e3",
    "zh:43c579b64d81d69042c1a8cb0a6fe168c91b1ae6b35ab0141b60d59398eabf89",
    "zh:5f3aff72630044e8aefaf6c8b52f9a653c77892571089efc57a1d3c9796045e4",
    "zh:78d5eefdd9e494defcb3c68d282b8f96630502cac21d1ea161f53cfe9bb483b3",
    "zh:83e799f5bb9f5a602f72a04b4aedbc66eb57ad7b857a6d052e5e5d29bd6f9cdf",
    "zh:8a3feae82963916c93f171a0f5522ad3fcf2981ec3d5b8462a99ea62f8f20aa0",
    "zh:9b44247f40ca2c3d492a104a7b1dad4734e4becfe80e965010c2b6687d554faf",
    "zh:aaa3fc59dceae83ee1fdcc41049a7b0d429fa56438a226533d4c0ee80e2e7ebc",
    "zh:af08f66218d3dcd3cd890f29735fd97475a105cd84d689317324c73c4dff9ba9",
    "zh:ba8160e39d6879b4a95493b233454fa93b8a487d14b1110efb39e5933040cd5e",
    "zh:e83252aa43f654063658caaaec2c622b8e37a971327c1caee0e38eb2f8271f04",
  ]
}

provider "registry.terraform.io/okta/okta" {
  version     = "4.16.0"
  constraints = ">= 3.36.0"
  hashes = [
    "h1:4kQI1Ckaq4ENgppIjlipJM93v6kMaQw9yz+461fPBFk=",
    "zh:231c05ec92710bb3410d72c1348e6db77cbc8cdf02faf7ff76297106f85f65aa",
    "zh:235213ae3aa6fe42b9237745b28ed97df24a211f03921dcde01d9a6e3ff069e7",
    "zh:246bd794fa53c8e9aacd2d32c34b752e71fabbca7d68670a84358d67d67dca1a",
    "zh:25f1e74c53c37570d20e7f41d069dfd93bfec36a43b87e0e58bcd002fb7b5365",
    "zh:31d15d73a02c9c6d94a960ee839185dc136f00f71251777ac13f762b40620e7f",
    "zh:362b25b49812fa799c606026d55b9155b40ca5db2fdfd5510b81fdd19272bacc",
    "zh:3edc8b439b9c613c9e7b0858c9895d0454a12fd76a9dfdca551b5eca7c28ff6f",
    "zh:510ad9609659e1ad6216410adf98c96f5b5fe285a2add433696e4311e49a49ca",
    "zh:67b5427ffbe6e90779e3a83152120bd62e417392436e37ea0d172450abec4c6f",
    "zh:68296d7df5ebfe982d4b3d3889c88d06f46931dcb31f7b52c00ed2d5fcc97b0e",
    "zh:94ccda7ebe104086ddd6e98295bdc9f482ed163998faac8d1ab9c0c14628d6e4",
    "zh:a71fd5755841ada7f5416e608486a116018cc8a388cb56136a706d2dd5b38163",
    "zh:e74a471bb1aab68730fb7c1f63e49bb3b727236ba257cc539d7340151b32a45f",
    "zh:ff005ae0c6586c6943c6b470323ce5bad750944f452e55f3e726ab51600fd44e",
  ]
}
