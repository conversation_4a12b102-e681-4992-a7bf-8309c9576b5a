provider "vault" {
  address = "https://vault.customappsteam.co.uk"
}

provider "aws" {
  assume_role {
    role_arn     = "arn:aws:iam::${module.pdata.account_id}:role/SSOAdmin"
    session_name = "${module.pdata.project}-${module.pdata.environment}-terraform"
  }

  allowed_account_ids = [module.pdata.account_id]
  region              = module.pdata.region

  default_tags {
    tags = merge(module.pdata.default_tags, {
      "repo" = "fs-tech-scuk-motor-finance"
    })
  }
}

data "aws_eks_cluster_auth" "k8s_cluster" {
  name = module.pdata.eks_cluster_name
}

provider "kubernetes" {
  host                   = data.aws_eks_cluster.eks_cluster.endpoint
  cluster_ca_certificate = base64decode(data.aws_eks_cluster.eks_cluster.certificate_authority[0].data)
  token                  = data.aws_eks_cluster_auth.k8s_cluster.token
}

data "vault_generic_secret" "argocd" {
  path = "kv-v2/aws/${module.pdata.account_id}/argocd"
}

provider "argocd" {
  server_addr        = data.vault_generic_secret.argocd.data["server_addr"]
  grpc_web_root_path = "argocd"
  username           = data.vault_generic_secret.argocd.data["username"]
  password           = data.vault_generic_secret.argocd.data["password"]
}
provider "okta" {
  org_name  = data.vault_generic_secret.okta.data["org_name"]
  base_url  = data.vault_generic_secret.okta.data["base_url"]
  api_token = data.vault_generic_secret.okta.data["api_token"]
}

data "vault_generic_secret" "okta" {
  path = "kv-v2/aws/${module.pdata.account_id}/okta"
}