locals {
  log_level = var.environment == "test-1" ? "debug" : "info"
  lambda_nodejs_file_patterns = [
    "!.*", # exclude all by default for zip contents, then opt-in
    "node_modules/.+",
    "src/.+",
    "package\\.json",
    "package-lock\\.json"
  ]
  lambda_nodejs_bundle_file_patterns = [
    "!.*", # exclude all by default for zip contents, then opt-in
    "bundle/.+",
    "src/.+", # Include the src folder purely to enable viewing the original code in AWS console
    "package\\.json",
    "package-lock\\.json"
  ]
  lambda_nodejs_build_commands = [
    "set -e",
    "npm config --location=project set engine-strict false", # setup-node version may not strictly satisfy package.json engines
    "npm ci",
    ":zip"
  ]
  lambda_nodejs_build_bundle_commands = [
    "set -e",
    "npm config --location=project set engine-strict false", # setup-node version may not strictly satisfy package.json engines
    "npm ci",
    "esbuild ./src/main.js --bundle --sourcemap --sources-content=false --platform=node --target=node22.16.0 --format=esm --main-fields=module,main --external:pg-native --outfile=bundle/main.mjs --banner:js='import { createRequire } from \"module\";const require = createRequire(import.meta.url);'",
    ":zip"
  ]
  lambda_nodejs_build_self_bundle_commands = [
    "set -e",
    "npm config --location=project set engine-strict false", # setup-node version may not strictly satisfy package.json engines
    "NODE_ENV=development npm ci",
    "npm run bundle",
    ":zip"
  ]
  lambdas = {
    "restate-runner" = {
      "runtime"            = "nodejs22.x"
      "handler"            = "bundle/main.handler"
      "timeout"            = 300 # 5 min
      "restate_deployment" = true
      "restate_post"       = []
      "source_path" = [
        {
          "path"     = "${path.module}/../lambdas/restate-runner"
          "patterns" = local.lambda_nodejs_bundle_file_patterns
          "commands" = local.lambda_nodejs_build_self_bundle_commands
        }
      ]
      "envs" = {
        AWS_ENV                     = var.environment
        LOGGING_LEVEL               = local.log_level
        NODE_ENV                    = "production"
        NODE_OPTIONS                = "--enable-source-maps"
        PG_CA                       = data.aws_ssm_parameter.rds_ca_root.value
        PG_SCUK_HOST                = local.aws_db_infos["scuk"].endpoint
        PG_SCUK_PORT                = local.aws_db_infos["scuk"].port
        PG_SCUK_USER                = local.aws_db_infos["scuk"].service_username
        RESTATE_IDENTITY_PUBLIC_KEY = data.kubernetes_config_map_v1.restate.data["RESTATE_IDENTITY_PUBLIC_KEY"]
        RESTATE_LOGGING             = "INFO"
        RESTATE_JOURNAL_RETENTION   = var.restate_journal_retention
      }
      "iam" = [
        {
          "Effect"   = "Allow"
          "Action"   = "rds-db:connect"
          "Resource" = "arn:aws:rds-db:${module.pdata.region}:${module.pdata.account_id}:dbuser:${local.aws_db_infos["scuk"].resource_id}/${local.aws_db_infos["scuk"].service_username}"
        },
        {
          "Effect" = "Allow"
          "Action" = [
            "secretsmanager:GetSecretValue",
            "secretsmanager:PutSecretValue",
          ]
          "Resource" = data.aws_secretsmanager_secret.shared_cookie_secret.arn
        },
        {
          "Effect"   = "Allow"
          "Action"   = "secretsmanager:GetRandomPassword"
          "Resource" = "*"
        },
      ]
      "access_sgs" = [
        {
          "name"              = "scuk db" # Just for sg decription
          "security_group_id" = local.aws_db_infos["scuk"].sg
          "from_port"         = 5432
          "to_port"           = 5432
        }
      ]
    }
  }
}
