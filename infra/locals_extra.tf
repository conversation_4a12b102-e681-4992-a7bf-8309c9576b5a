locals {
  redis = [
    {
      "name" = "portals"
    }
  ]

  s3s = [
    # {
    #    "name" = "extra-bucket"
    #    "notifications" = {
    #      "topic" = [{
    #        "name" = "policy-portal-policy-portal-events-topic"
    #      }]
    #    }
    #  }
  ]

  # This is for buckets outside of the repo
  outside_s3_notifications = [
    # {
    #   "name" = "mi-reports-rd-dashboard"
    #   "notifications" = {
    #     "queues" = [
    #       {
    #         name = "mi-report-email-mi-report-email"
    #       },
    #     ]
    #   }
    # },
  ]

  sqs = [
    # {
    #   "name"                       = "cra-report-queue"
    #   "enable_dlq"                 = false
    #   "visibility_timeout_seconds" = 70 # cra-report lambda timeout + 10
    # },
  ]

  sns = []
}