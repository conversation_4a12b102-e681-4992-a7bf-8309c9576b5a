locals {
  # Service with other resource associated to their name (DB/S3/SNS/SQS)
  service_creates = {
    "claims-portal" = {},
    "scuk-db" = {
      "db" = {}
    }
  }

  # Service configuration (IAM/Secret/Configs)
  service_configs = {
    "claims-portal" = {
      "configs" = {
        AWS_ENV                 = var.environment
        PLAUSIBLE_SCRIPT_URL    = var.environment == "prod" ? "TODO" : "https://plausible.test-1.scf.kpmgcdd-fincrime.com/js/script.js"
        PUBLIC_HOST             = var.environment == "prod" ? "TODO" : "claims-portal.${module.pdata.domain}"
        RESTATE_INGRESS_URL     = "http://restate.restate:8080"
        SECRET_ID_COOKIE_SECRET = "services/${var.environment}-cookie-secret"
      }
      "secrets" = {
        REDIS_HOSTNAME = "redis://${module.redis["portals"].endpoint}:${module.redis["portals"].port}"
      }
      "iam" = [
        {
          "Effect"   = "Allow"
          "Action"   = "secretsmanager:GetSecretValue"
          "Resource" = data.aws_secretsmanager_secret.shared_cookie_secret.arn
        },
      ]
    },
    "scuk-db" = {
      "secrets" = {
        "pg-connection-string" = "postgres://${local.aws_db_infos["scuk"].root_username}:${local.aws_db_infos["scuk"].root_password}@${local.aws_db_infos["scuk"].endpoint}:${local.aws_db_infos["scuk"].port}/${local.aws_db_infos["scuk"].dbname}"
      }
    },
  }
}
