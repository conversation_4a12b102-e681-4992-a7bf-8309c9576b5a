resource "argocd_repository" "service" {
  count = var.create_ecr ? 1 : 0
  repo  = local.repo_url
}
resource "argocd_application" "services" {
  for_each = toset([for k in local.service_names : k if lookup(lookup(local.service_configs, k, {}), "argocd", true)])
  metadata {
    name      = "${var.environment}-${local.repo_namespace}-${each.value}"
    namespace = "argocd"
    labels = {
      environment = var.environment
      service     = each.value
      repo        = local.repo_name
    }
  }

  wait = false

  spec {
    project = "default"
    source {
      path            = "k8s/${each.value}"
      repo_url        = var.create_ecr ? argocd_repository.service[0].repo : local.repo_url
      target_revision = var.argocd_sync_branch != "" ? var.argocd_sync_branch : "argo-sync-${var.environment}"

      helm {
        skip_crds = false
        value_files = [
          "environments/${var.environment}.yaml",
        ]
      }
    }

    sync_policy {
      dynamic "automated" {
        for_each = var.argocd_auto_sync ? [1] : []
        content {
          allow_empty = false
          prune       = false
          self_heal   = false
        }
      }
      sync_options = []
      retry {
        limit = "3" # Max number of allowed sync retries
        backoff {
          duration     = "5s" # Sync retry backoff base duration
          factor       = "2"  # Factor multiplies the base duration after each failed sync retry
          max_duration = "5m" # Max sync retry backoff duration
        }
      }
    }

    destination {
      namespace = "ms"
      server    = data.aws_eks_cluster.eks_cluster.endpoint
    }
  }
}