module "pdata" {
  source = "git::https://github.com/KPMG-UK/ie-cdd-general-data.git/?ref=main"

  project     = "scf"
  environment = var.environment
  include_aws = true
}
module "kpmg_data" {
  source = "git::https://github.com/KPMG-UK/terraform-common-data.git/?ref=main"
}
data "aws_eks_cluster" "eks_cluster" {
  name = module.pdata.eks_cluster_name
}
data "aws_security_group" "cluster_sg" {
  vpc_id = module.pdata.vpc_id
  filter {
    name   = "group-name"
    values = ["eks-cluster-sg-${module.pdata.eks_cluster_name}-*"]

  }
}

data "aws_kms_key" "rds" {
  key_id = "alias/aws/rds"
}

data "aws_iam_openid_connect_provider" "eks_cluster_oidc" {
  arn = module.pdata.eks_cluster_oidc_arn
}
data "aws_lambda_function" "db_autoroles" {
  function_name = "${var.environment}-db-autoroles"
}
data "aws_lambda_function" "restate_registrer" {
  function_name = "cluster-${var.environment}-restate-registrer"
}
data "kubernetes_config_map_v1" "restate" {
  metadata {
    name      = "restate-pub"
    namespace = module.pdata.eks_cluster_microservices_namespace
  }
}

data "aws_ssm_parameter" "rds_ca_root" {
  name = "/${var.environment}/rds_sslrootcert/RDS_CA_2048_ROOT"
}
data "okta_group" "admin" {
  name = "${title(module.pdata.project)} Admins"
}
locals {
  outside_s3_buckets = [
  ]
  outside_sg_names = [
  ]
  outside_db_names = [
  ]
}
data "okta_policy" "onefa" {
  name = "One factor access"
  type = "ACCESS_POLICY"
}
data "okta_policy" "twofa" {
  name = "Any two factors"
  type = "ACCESS_POLICY"
}

#secrets
data "aws_secretsmanager_secret" "apikey_shared" {
  name = "services/${var.environment}-apikey-shared"
}
data "aws_secretsmanager_secret" "shared_cookie_secret" {
  name = "services/${var.environment}-cookie-secret"
}

data "aws_security_group" "outside_sgs" {
  for_each = toset(local.outside_sg_names)
  vpc_id   = module.pdata.vpc_id
  filter {
    name   = "group-name"
    values = ["${var.environment}-${each.value}"]
  }
}

data "aws_s3_bucket" "outside_buckets" {
  for_each = toset(concat(local.outside_s3_buckets, keys(local.pre_s3_outside_s3_notifications)))
  bucket   = "${module.pdata.project}-${each.key}-${var.environment}"
}

data "aws_db_instance" "outside_db_names" {
  for_each               = toset(local.outside_db_names)
  db_instance_identifier = "${var.environment}-${each.value}"
}
