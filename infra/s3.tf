resource "aws_s3_bucket" "bucket" {
  #checkov:skip=CKV_AWS_144: No need for cross replication
  #checkov:skip=CKV2_AWS_62: No need for notification
  for_each = local.s3_buckets
  bucket   = "${module.pdata.project}-${each.value.name}-${var.environment}"
  tags = {
    Name = "${module.pdata.project}-${each.value.name}-${var.environment}"
    role = "s3"
  }
}
resource "aws_s3_bucket_versioning" "bucket_versioning" {
  for_each = local.s3_buckets
  bucket   = aws_s3_bucket.bucket[each.key].id
  versioning_configuration {
    status = "Enabled"
  }
}
resource "aws_s3_bucket_logging" "bucket_logging" {
  for_each      = local.s3_buckets
  bucket        = aws_s3_bucket.bucket[each.key].id
  target_bucket = module.pdata.log_bucket
  target_prefix = "${aws_s3_bucket.bucket[each.key].id}-log/"
}

resource "aws_s3_bucket_lifecycle_configuration" "bucket_lifecycle" {
  for_each = local.s3_buckets
  bucket   = aws_s3_bucket.bucket[each.key].id

  transition_default_minimum_object_size = "all_storage_classes_128K"
  rule {
    id     = "managed"
    status = "Enabled"
    filter {
      prefix = ""
    }
    dynamic "expiration" {
      for_each = lookup(each.value, "lifecycle_expiration_days", null) != null ? [1] : []
      content {
        days = each.value.lifecycle_expiration_days
      }
    }
    dynamic "transition" {
      for_each = lookup(each.value, "lifecycle_transition_oia_days", var.s3_default_settings.lifecycle_transition_oia_days) != null ? [1] : []
      content {
        days          = lookup(each.value, "lifecycle_transition_oia_days", var.s3_default_settings.lifecycle_transition_oia_days)
        storage_class = "ONEZONE_IA"
      }
    }
    dynamic "noncurrent_version_expiration" {
      for_each = lookup(each.value, "lifecycle_noncurrent_expiration_days", var.s3_default_settings.lifecycle_noncurrent_expiration_days) != null ? [1] : []
      content {
        noncurrent_days = lookup(each.value, "lifecycle_noncurrent_expiration_days", var.s3_default_settings.lifecycle_noncurrent_expiration_days)
      }
    }
    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }
  }
}
resource "aws_s3_bucket_policy" "bucket_policy" {
  for_each = aws_s3_bucket.bucket
  bucket   = aws_s3_bucket.bucket[each.key].id
  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Deny",
        "Principal" : "*",
        "Action" : "*",
        "Resource" : [
          "arn:aws:s3:::${aws_s3_bucket.bucket[each.key].id}/*"
        ],
        "Condition" : {
          "Bool" : {
            "aws:SecureTransport" : "false"
          }
        }
      }
    ]
  })
}
resource "aws_s3_bucket_server_side_encryption_configuration" "bucket_encryption" {
  #checkov:skip=CKV2_AWS_67: module.pdata.kms_arn is rotated
  for_each = local.s3_buckets
  bucket   = aws_s3_bucket.bucket[each.key].id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = module.pdata.kms_arn
      sse_algorithm     = "aws:kms"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "bucket_block" {
  for_each = local.s3_buckets
  bucket   = aws_s3_bucket.bucket[each.key].id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_s3_bucket_notification" "bucket_notifications" {
  for_each = local.s3_buckets_notifications
  bucket   = aws_s3_bucket.bucket[each.key].bucket

  dynamic "lambda_function" {
    for_each = { for k in lookup(each.value, "lambdas", []) : k.lambda => k }
    content {
      lambda_function_arn = module.lambdas[lambda_function.value.lambda].lambda_function_arn
      events              = lookup(lambda_function.value, "events", ["s3:ObjectCreated:*"])
      filter_prefix       = lookup(lambda_function.value, "prefix", null)
      filter_suffix       = lookup(lambda_function.value, "suffix", null)
    }
  }
  dynamic "queue" {
    for_each = { for k in lookup(each.value, "queues", []) : k.name => k }
    content {
      queue_arn     = aws_sqs_queue.services[queue.value.name].arn
      events        = lookup(queue.value, "events", ["s3:ObjectCreated:*"])
      filter_prefix = lookup(queue.value, "prefix", null)
      filter_suffix = lookup(queue.value, "suffix", null)
    }
  }
  dynamic "topic" {
    for_each = { for k in lookup(each.value, "topic", []) : k.name => k }
    content {
      topic_arn     = aws_sns_topic.sns_topic[topic.value.name].arn
      events        = lookup(topic.value, "events", ["s3:ObjectCreated:*"])
      filter_prefix = lookup(topic.value, "prefix", null)
      filter_suffix = lookup(topic.value, "suffix", null)
    }
  }
  depends_on = [
    aws_sqs_queue_policy.services,
    aws_lambda_permission.s3_lambdas,
  ]
}
resource "aws_s3_bucket_notification" "outside_bucket_notifications" {
  for_each = local.s3_outside_s3_notifications
  bucket   = data.aws_s3_bucket.outside_buckets[each.key].bucket

  dynamic "queue" {
    for_each = { for k in lookup(each.value, "queues", []) : k.name => k if lookup(k, "enabled", true) }
    content {
      queue_arn     = aws_sqs_queue.services[queue.value.name].arn
      events        = lookup(queue.value, "events", ["s3:ObjectCreated:*"])
      filter_prefix = lookup(queue.value, "prefix", null)
      filter_suffix = lookup(queue.value, "suffix", null)
    }
  }
  dynamic "topic" {
    for_each = { for k in lookup(each.value, "topic", []) : k.name => k }
    content {
      topic_arn     = aws_sns_topic.sns_topic[topic.value.name].arn
      events        = lookup(topic.value, "events", ["s3:ObjectCreated:*"])
      filter_prefix = lookup(topic.value, "prefix", null)
      filter_suffix = lookup(topic.value, "suffix", null)
    }
  }
  depends_on = [
    aws_sqs_queue_policy.services,
    aws_lambda_permission.s3_lambdas,
  ]
}

# resource "aws_s3_object" "base_folders" {
#   for_each = toset([
#     "archive",
#     "error",
#     "outbound_MI"
#   ])
#   bucket = aws_s3_bucket.bucket["letters-honcho-mailing-outbound-bucket"].id
#   acl    = "private"
#   key    = "${each.value}/"
# }