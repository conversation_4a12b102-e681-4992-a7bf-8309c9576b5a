environment = "prod"

db_default_settings = {
  # allocated_storage               = 10
  # max_allocated_storage           = 50
  # engine_version                  = "14"
  # instance_class                  = "db.t3.small"
  # backup_retention_period         = 2
  # db_name                         = "postgres"
  # performance_insights_kms_key_id = "arn:aws:kms:eu-west-2:109286117078:key/4f3981a8-5460-4438-9ae7-db743b40abdc"
  # ca_cert_name                    = "rds-ca-rsa2048-g1"
}

# branded_domain = ""
service_dbs = {
}

skip_services = [
]

skip_lambdas = [
]
