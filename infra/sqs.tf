resource "aws_sqs_queue" "services" {
  for_each                   = local.sqs_queues
  name                       = "${var.environment}-${each.value.name}"
  delay_seconds              = lookup(each.value, "delay_seconds", var.sqs_default_settings.delay_seconds)
  max_message_size           = lookup(each.value, "max_message_size", var.sqs_default_settings.max_message_size)
  message_retention_seconds  = lookup(each.value, "message_retention_seconds", var.sqs_default_settings.message_retention_seconds)
  receive_wait_time_seconds  = lookup(each.value, "receive_wait_time_seconds", var.sqs_default_settings.receive_wait_time_seconds)
  visibility_timeout_seconds = lookup(each.value, "visibility_timeout_seconds", var.sqs_default_settings.visibility_timeout_seconds)
  kms_master_key_id          = module.pdata.kms_arn
}

resource "aws_sqs_queue_redrive_policy" "services" {
  for_each  = { for k, v in local.sqs_queues : k => v if lookup(v, "enable_dlq", false) }
  queue_url = aws_sqs_queue.services[each.key].id
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.services_dlq[each.key].arn
    maxReceiveCount     = lookup(each.value, "dlq_max_receive_count", 4)
  })
}

resource "aws_sqs_queue_policy" "services" {
  for_each  = { for k, v in local.sqs_queues : k => v if lookup(v, "enable_dlq", false) || length(lookup(v, "subscribed_buckets", [])) > 0 || length(lookup(v, "subscribed_sns", [])) > 0 }
  queue_url = aws_sqs_queue.services[each.key].id
  policy = jsonencode({
    "Version" = "2012-10-17",
    "Statement" = concat(lookup(each.value, "enable_dlq", false) ? [
      {
        "Effect" : "Allow",
        "Action" : [
          "sqs:SendMessageBatch",
          "sqs:SendMessage",
          "sqs:PurgeQueue",
          "sqs:ListQueues",
          "sqs:DeleteMessageBatch",
          "sqs:DeleteMessage",
        ],
        "Resource" : aws_sqs_queue.services_dlq[each.key].arn
      }] : [],
      length(lookup(each.value, "subscribed_buckets", [])) > 0 ? [
        {
          "Effect" : "Allow",
          "Principal" : {
            "Service" : "s3.amazonaws.com"
          },
          "Action" : "sqs:SendMessage",
          "Resource" : aws_sqs_queue.services[each.key].arn,
          "Condition" : {
            "ArnEquals" : {
              "aws:SourceArn" : [for b in each.value.subscribed_buckets : contains(keys(local.s3_buckets), b) ? aws_s3_bucket.bucket[b].arn : data.aws_s3_bucket.outside_buckets[b].arn]
            } // add permission for any bucket sending notifications to this queue
          }
      }] : [],
      length(lookup(each.value, "subscribed_sns", [])) > 0 ? [
        {
          "Effect" : "Allow",
          "Principal" : {
            "Service" : "sns.amazonaws.com"
          },
          "Action" : "sqs:SendMessage",
          "Resource" : aws_sqs_queue.services[each.key].arn,
          "Condition" : {
            "ArnEquals" : {
              "aws:SourceArn" : [for subscription in each.value.subscribed_sns : aws_sns_topic.sns_topic[subscription.identifier].arn]
            } // add permission for any bucket sending notifications to this queue
          }
      }] : []
    )
  })
}

resource "aws_sns_topic_subscription" "sns_to_sqs" {
  for_each  = local.sqs_sns_subscriptions
  topic_arn = aws_sns_topic.sns_topic[each.value.identifier].arn
  protocol  = "sqs"
  endpoint  = aws_sqs_queue.services[each.value.sqs].arn

  raw_message_delivery = lookup(each.value, "raw_message_delivery", false)
}

resource "aws_sqs_queue" "services_dlq" {
  for_each                   = { for k, v in local.sqs_queues : k => v if lookup(v, "enable_dlq", false) }
  name                       = "${var.environment}-${each.value.name}-dql"
  delay_seconds              = lookup(each.value, "dlq_delay_seconds", var.sqs_default_settings.dlq_delay_seconds)
  max_message_size           = lookup(each.value, "dlq_max_message_size", var.sqs_default_settings.dlq_max_message_size)
  message_retention_seconds  = lookup(each.value, "dlq_message_retention_seconds", var.sqs_default_settings.dlq_message_retention_seconds)
  receive_wait_time_seconds  = lookup(each.value, "dlq_receive_wait_time_seconds", var.sqs_default_settings.dlq_receive_wait_time_seconds)
  visibility_timeout_seconds = lookup(each.value, "dlq_visibility_timeout_seconds", var.sqs_default_settings.dlq_visibility_timeout_seconds)
  kms_master_key_id          = module.pdata.kms_arn
}