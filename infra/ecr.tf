data "aws_iam_policy_document" "service_ecr_policy_doc" {
  dynamic "statement" {
    for_each = module.pdata.account_ids
    content {
      sid    = "Share-${statement.value}"
      effect = "Allow"

      actions = [
        "ecr:BatchCheckLayerAvailability",
        "ecr:BatchDeleteImage",
        "ecr:BatchGetImage",
        "ecr:CompleteLayerUpload",
        "ecr:DescribeImages",
        "ecr:DescribeRepositories",
        "ecr:GetDownloadUrlForLayer",
        "ecr:GetLifecyclePolicy",
        "ecr:GetLifecyclePolicyPreview",
        "ecr:GetRepositoryPolicy",
        "ecr:InitiateLayerUpload",
        "ecr:ListImages",
        "ecr:PutImage",
        "ecr:UploadLayerPart",
      ]

      principals {
        type        = "AWS"
        identifiers = formatlist("arn:aws:iam::%v:root", [statement.value])
      }
    }

  }
}

resource "aws_ecr_repository" "repo" {
  #checkov:skip=CKV_AWS_136: No need for KMS
  for_each             = var.create_ecr ? local.service_names : []
  name                 = "${local.repo_namespace}-${each.value}"
  image_tag_mutability = "IMMUTABLE"

  image_scanning_configuration {
    scan_on_push = true
  }
}

resource "aws_ecr_repository_policy" "repos" {
  for_each   = var.create_ecr ? local.service_names : []
  repository = aws_ecr_repository.repo[each.value].name

  policy = data.aws_iam_policy_document.service_ecr_policy_doc.json
}