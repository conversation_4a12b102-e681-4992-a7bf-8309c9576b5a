resource "kubernetes_config_map_v1" "service_configmap" {
  for_each = toset(keys(local.service_configs_filtered))
  metadata {
    name      = lower("${local.repo_namespace}-${each.key}")
    namespace = module.pdata.eks_cluster_microservices_namespace
  }

  data = local.configs_services[each.key]
}

resource "kubernetes_secret_v1" "service_secrets" {
  for_each = toset(keys(local.service_configs_filtered))
  metadata {
    name      = lower("${local.repo_namespace}-${each.key}")
    namespace = module.pdata.eks_cluster_microservices_namespace
  }

  data = local.secrets_services[each.key]
}

