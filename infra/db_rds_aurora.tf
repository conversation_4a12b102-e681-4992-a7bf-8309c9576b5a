resource "aws_db_subnet_group" "auroraserv_dbs_subnet_group" {
  for_each    = local.aurora_db_instances
  name        = "${each.key} subnet group"
  description = "${each.key} subnet group"
  subnet_ids  = module.pdata.data_subnet_ids
}
data "aws_rds_cluster" "existing_clusters" {
  for_each           = { for k, v in local.aurora_db_instances : k => v if lookup(v, "engine_version", var.db_default_settings.aurora_engine_version) == "" }
  cluster_identifier = "${var.environment}-${each.key}"
}
data "aws_rds_engine_version" "auroraserv_postgresql" {
  for_each = local.aurora_db_instances
  engine   = "aurora-postgresql"
  version  = lookup(each.value, "engine_version", var.db_default_settings.aurora_engine_version) == "" ? data.aws_rds_cluster.existing_clusters[each.key].engine_version : lookup(each.value, "engine_version", var.db_default_settings.aurora_engine_version)
}

resource "aws_db_parameter_group" "auroraserv_dbs_parameter_group" {
  for_each    = local.aurora_db_instances
  name        = "${var.environment}-${each.key}-postgre-pg-${split(".", data.aws_rds_engine_version.auroraserv_postgresql[each.key].version)[0]}"
  family      = data.aws_rds_engine_version.auroraserv_postgresql[each.key].parameter_group_family
  description = "postgres parameter group for ${each.key}"

  dynamic "parameter" {
    for_each = merge({
      "max_slot_wal_keep_size" : { value : 2048 }, # MB https://www.postgresql.org/docs/current/runtime-config-replication.html#RUNTIME-CONFIG-REPLICATION-SENDER
      "log_connections" : { value : 1 },
      "log_disconnections" : { value : 1 },
      "log_error_verbosity" : { value : "default" },
      "log_statement" : { "value" : var.environment == "prod" ? "mod" : "none" },
    }, lookup(each.value, "parameters", {}))
    content {
      apply_method = lookup(parameter.value, "apply_method", "immediate")
      name         = parameter.key
      value        = parameter.value["value"]
    }
  }
}

resource "aws_rds_cluster_parameter_group" "auroraserv_dbs_parameter_group" {
  for_each = local.aurora_db_instances
  # FIXME this should be prefixed by the env name otherwise there might be conflicts
  name        = "${each.key}-aurora-pg-${split(".", data.aws_rds_engine_version.auroraserv_postgresql[each.key].version)[0]}"
  family      = data.aws_rds_engine_version.auroraserv_postgresql[each.key].parameter_group_family
  description = "postgres cluster parameter group for ${each.key}"

  dynamic "parameter" {
    for_each = lookup(each.value, "aurora_parameters", {
      "max_slot_wal_keep_size" : { value : 2048 }, # MB https://www.postgresql.org/docs/current/runtime-config-replication.html#RUNTIME-CONFIG-REPLICATION-SENDER
    })
    content {
      apply_method = lookup(parameter.value, "apply_method", "immediate")
      name         = parameter.key
      value        = parameter.value["value"]
    }
  }
}



module "aurora_serverless_dbs" {
  for_each = local.aurora_db_instances
  source   = "terraform-aws-modules/rds-aurora/aws"
  version  = "9.3.1"

  name                        = "${var.environment}-${each.key}"
  engine                      = data.aws_rds_engine_version.auroraserv_postgresql[each.key].engine
  engine_mode                 = "provisioned"
  engine_version              = data.aws_rds_engine_version.auroraserv_postgresql[each.key].version
  ca_cert_identifier          = lookup(each.value, "ca_cert_name", var.db_default_settings.ca_cert_name)
  storage_encrypted           = true
  master_username             = local.db_admin_username
  master_password             = random_password.dbs_random_string[each.key].result
  manage_master_user_password = false
  database_name               = lookup(each.value, "db_name", "postgres")

  vpc_id               = module.pdata.vpc_id
  db_subnet_group_name = aws_db_subnet_group.auroraserv_dbs_subnet_group[each.key].name
  vpc_security_group_ids = [
    aws_security_group.dbs[each.key].id,
    aws_security_group.dbs_accessor[each.key].id,
  ]
  iam_database_authentication_enabled = true
  monitoring_interval                 = lookup(each.value, "monitoring_interval", 60)

  performance_insights_enabled          = lookup(each.value, "performance_insights_enabled", module.pdata.is_prod_env)
  performance_insights_kms_key_id       = lookup(each.value, "performance_insights_enabled", module.pdata.is_prod_env) ? data.aws_kms_key.rds.arn : null
  performance_insights_retention_period = lookup(each.value, "performance_insights_enabled", module.pdata.is_prod_env) ? 7 : 0

  create_db_parameter_group         = false
  create_db_cluster_parameter_group = false
  db_parameter_group_name           = aws_db_parameter_group.auroraserv_dbs_parameter_group[each.key].name
  db_cluster_parameter_group_name   = aws_rds_cluster_parameter_group.auroraserv_dbs_parameter_group[each.key].id

  apply_immediately   = true
  skip_final_snapshot = lookup(each.value, "skip_final_snapshot", false)

  create_cloudwatch_log_group            = length(lookup(each.value, "cloudwatch_logs", ["postgresql", "iam-db-auth-error"])) > 0
  cloudwatch_log_group_retention_in_days = module.pdata.log_retention_days
  enabled_cloudwatch_logs_exports        = lookup(each.value, "cloudwatch_logs", ["postgresql", "iam-db-auth-error"])

  serverlessv2_scaling_configuration = {
    min_capacity = lookup(each.value, "min_capacity", 1)
    max_capacity = lookup(each.value, "max_capacity", 10)
  }

  storage_type = lookup(each.value, "storage_type", null)

  instances = lookup(each.value, "instances", {
    "writer" : {
      "promotion_tier" : 0 # failover priority
    }
    "reader-1" : {
      "promotion_tier" : 1 # failover priority
    }
    "reader-2" : {
      "promotion_tier" : 2 # failover priority
    }
  })
  endpoints             = lookup(each.value, "endpoints", {})
  copy_tags_to_snapshot = true
  deletion_protection   = true
  instance_class        = each.value.type == "aurora" ? lookup(each.value, "instance_class", "db.r5.large") : "db.serverless"

  tags = {
    description                    = each.key
    role                           = each.key
    projectName                    = module.pdata.project
    identifier                     = each.key
    "team"                         = "engineering"
    "department"                   = "cdd"
    "service"                      = each.key
    (module.pdata.aws_backup_key)  = module.pdata.aws_backup_value
    "${var.environment}-scheduled" = module.pdata.scheduled ? "on" : "off"
  }
}

locals {
  aws_db_aurora_infos = {
    for k, v in local.aurora_db_instances : k => {
      "root_username"         = local.db_admin_username
      "root_password"         = random_password.dbs_random_string[k].result
      "service_username"      = "service_${replace(k, "-", "_")}"
      "service_read_username" = "service_${replace(k, "-", "_")}_read"
      "endpoint"              = module.aurora_serverless_dbs[k].cluster_endpoint
      "resource_id"           = module.aurora_serverless_dbs[k].cluster_resource_id
      "reader_endpoint"       = module.aurora_serverless_dbs[k].cluster_reader_endpoint
      "port"                  = module.aurora_serverless_dbs[k].cluster_port
      "dbname"                = lookup(v, "db_name", "postgres")
      "identifier"            = module.aurora_serverless_dbs[k].cluster_id
      "sg"                    = aws_security_group.dbs_accessor[k].id
    }
  }
}


resource "aws_secretsmanager_secret_version" "aurora_serverless_dbs_secret_val" {
  for_each      = local.aurora_db_instances
  secret_id     = aws_secretsmanager_secret.dbs_secret[each.key].id
  secret_string = "postgres://${local.db_admin_username}:${random_password.dbs_random_string[each.key].result}@${module.aurora_serverless_dbs[each.key].cluster_endpoint}:${module.aurora_serverless_dbs[each.key].cluster_port}/${lookup(each.value, "db_name", "postgres")}"

  lifecycle {
    create_before_destroy = true
  }
}
