locals {
  service_creates_filtered = { for k, v in local.service_creates : k => v if !contains(var.skip_services, k) }
  service_configs_filtered = { for k, v in local.service_configs : k => v if !contains(var.skip_services, k) }
  service_names            = toset(concat(keys(local.service_creates_filtered), keys(local.service_configs_filtered)))
  # IAM
  iam_services = {
    for k, v in local.service_configs_filtered : k => v.iam if lookup(v, "iam", null) != null
  }
  iam_service_names = toset(keys(local.iam_services))
  iam_with_access_services = {
    for k, v in local.iam_services : k => v if length(v) > 0
  }
  iam_with_access_service_names = toset(keys(local.iam_with_access_services))
  # Configs
  configs_services = {
    for k, v in local.service_configs_filtered : k => merge(lookup(v, "configs", {}), lookup(var.service_extra_configs, k, {}))
  }

  secrets_services = {
    for k, v in local.service_configs_filtered : k => merge(lookup(v, "secrets", {}), lookup(var.service_extra_secrets, k, {}))
  }
  # DBs
  db_migration_service_names = toset([for i in local.service_names : i if endswith(i, "-db")])
  db_instances = {
    for k, v in local.service_creates_filtered : lookup(v.db, "name", replace(k, "-db", "")) => (merge(v.db, lookup(var.service_dbs, replace(k, "-db", ""), {}))) if lookup(v, "db", null) != null
  }
  db_instance_names = toset(keys(local.db_instances))
  aurora_db_instances = {
    for k, v in local.db_instances : k => v if contains(["aurora", "aurora_serverless"], lookup(v, "type", ""))
  }

  rds_db_instances = {
    for k, v in local.db_instances : k => v if lookup(v, "type", "postgres") == "postgres"
  }

  aws_db_infos = merge(local.aws_db_instance_infos, local.aws_db_aurora_infos)
  # Redis
  redis_instances = merge({
    for k, v in local.service_creates_filtered : k => merge(v.redis, lookup(var.service_redis, k, {})) if lookup(v, "redis", null) != null
  }, { for v in local.redis : v.name => merge(v, lookup(var.service_redis, v.name, {})) })
  # S3
  s3_extras = { "s3-extras" : local.s3s }
  s3_buckets_pre = merge({
    for k, v in local.service_creates_filtered : k => v.s3 if lookup(v, "s3", null) != null
  }, local.s3_extras, var.service_s3)
  s3_buckets_keys              = flatten([for k, v in local.s3_buckets_pre : formatlist("${k}-%s", [for i in v : i.name])])
  s3_buckets_values            = flatten([for k, v in local.s3_buckets_pre : v])
  s3_buckets                   = zipmap(local.s3_buckets_keys, local.s3_buckets_values)
  s3_buckets_notifications_pre = { for k, v in local.s3_buckets : k => merge(lookup(v, "notifications", {}), { "lambdas" : lookup(local.s3_lambdas, k, []) }) }
  s3_buckets_notifications     = { for k, v in local.s3_buckets_notifications_pre : k => v if anytrue([for ki, kv in v : length(kv) > 0]) }

  pre_s3_outside_s3_notifications = { for v in local.outside_s3_notifications : v.name => {
    "queues" = [for q in lookup(lookup(v, "notifications", {}), "queues", []) : q if lookup(q, "enabled", true)],
  } }
  s3_outside_s3_notifications = { for k, v in local.pre_s3_outside_s3_notifications : k => v if length(v.queues) > 0 }

  # SNS
  sns_extras = { "sns-extras" : local.sns }
  sns_pre = merge({
    for k, v in local.service_creates_filtered : k => v.sns if lookup(v, "sns", null) != null
  }, local.sns_extras, var.service_sns)
  sns_keys   = flatten([for k, v in local.sns_pre : formatlist("${k}-%s", [for i in v : i.name])])
  sns_values = flatten([for k, v in local.sns_pre : v])
  sns_topics = zipmap(local.sns_keys, local.sns_values)

  # SQS
  sqs_extras = { "sqs-extras" : local.sqs }
  sqs_pre = merge({
    for k, v in local.service_creates_filtered : k => v.sqs if lookup(v, "sqs", null) != null
  }, local.sqs_extras, var.service_sqs)
  sqs_keys   = flatten([for k, v in local.sqs_pre : formatlist("${k}-%s", [for i in v : i.name])])
  sqs_values = flatten([for k, v in local.sqs_pre : v])
  sqs_queues = zipmap(local.sqs_keys, local.sqs_values)

  sqs_sns_pre           = { for k, v in local.sqs_queues : k => v.subscribed_sns if length(lookup(v, "subscribed_sns", [])) > 0 }
  sqs_sns_keys          = flatten([for k, v in local.sqs_sns_pre : formatlist("${k}-%s", [for i in v : i.identifier])])
  sqs_sns_values        = flatten([for k, v in local.sqs_sns_pre : [for i in v : merge({ "sqs" : k }, i)]])
  sqs_sns_subscriptions = zipmap(local.sqs_sns_keys, local.sqs_sns_values)

  # VPC Endpoints
  vpc_endpoint_prefix_s3 = contains(keys(module.pdata.vpc_endpoints), "s3") ? module.pdata.vpc_endpoints.s3.prefix_list_id : ""

  # Lambdas
  lambdas_filtered = { for k, v in local.lambdas : k => v if !contains(var.skip_lambdas, k) }
  restate_lambdas  = { for k, v in local.lambdas_filtered : k => v if lookup(v, "restate_deployment", false) }
  lambda_required_vpc_endpoints = [
    "logs",
    "secretsmanager",
  ]
  lambda_shared_account_vpc_endpoints = module.pdata.shared_vpc_endpoint_services
  lambda_vpc_endpoints_pre = { for k, v in local.lambdas_filtered : k => distinct(concat(local.lambda_required_vpc_endpoints, lookup(v, "needed_vpc_endpoints", [])))
  }
  lambda_vpc_sgs = { for k, v in local.lambdas_filtered : k => {
    "vpc_endpoints"     = local.lambda_vpc_endpoints_pre[k]
    "has_all_endpoints" = alltrue([for e in local.lambda_vpc_endpoints_pre[k] : e != "s3" ? length(lookup(lookup(module.pdata.vpc_endpoints, e, {}), "security_group_ids", [])) > 0 || contains(local.lambda_shared_account_vpc_endpoints, e) : contains(keys(module.pdata.vpc_endpoints), "s3")])
    "vpc_endpoints_targets_sgs" = {
      for e in local.lambda_vpc_endpoints_pre[k] :
      e => module.pdata.vpc_endpoints[e].security_group_ids if e != "s3" && contains(keys(module.pdata.vpc_endpoints), e)
    }
    "vpc_endpoints_targets_prefix_list_id" = {
      for e in local.lambda_vpc_endpoints_pre[k] :
      e => module.pdata.vpc_endpoints[e].prefix_list_id if e == "s3" && contains(keys(module.pdata.vpc_endpoints), e)
    }
    "vpc_endpoints_available_via_shared_account" = [
      for e in local.lambda_vpc_endpoints_pre[k] :
      e if e != "s3" && contains(local.lambda_shared_account_vpc_endpoints, e)
    ]
    "access_sgs" = lookup(local.lambdas[k], "access_sgs", [])
    }
  }

  lambda_vpc_ingress_keys   = flatten([for k, v in local.lambdas_filtered : formatlist("${k}-%s", [for i in v.access_sgs : i.security_group_id]) if length(lookup(v, "access_sgs", [])) > 0])
  lambda_vpc_ingress_values = flatten([for k, v in local.lambdas_filtered : [for i in v.access_sgs : merge(i, { "lambda" : k })] if length(lookup(v, "access_sgs", [])) > 0])
  lambda_vpc_ingress        = zipmap(local.lambda_vpc_ingress_keys, local.lambda_vpc_ingress_values)

  lambda_db_accesses_keys   = flatten([for k, v in local.lambdas_filtered : formatlist("${k}-%s", [for i in v.db_accesses : i.name]) if length(lookup(v, "db_accesses", [])) > 0])
  lambda_db_accesses_values = flatten([for k, v in local.lambdas_filtered : [for i in v.db_accesses : merge(i, { "lambda" : k })] if length(lookup(v, "db_accesses", [])) > 0])
  lambda_db_accesses        = zipmap(local.lambda_db_accesses_keys, local.lambda_db_accesses_values)

  # Trigger Cron
  lambda_crons_pre   = { for k, v in local.lambdas_filtered : k => v.triggers.cron if length(lookup(lookup(v, "triggers", {}), "cron", [])) > 0 }
  lambda_cron_keys   = flatten([for k, v in local.lambda_crons_pre : formatlist("${k}-%s", [for i in v : i.name])])
  lambda_cron_values = flatten([for k, v in local.lambda_crons_pre : [for i in v : merge(i, { "lambda" : k })]])
  lambda_crons       = zipmap(local.lambda_cron_keys, local.lambda_cron_values)

  # Trigger S3
  lambda_s3_pre    = { for k, v in local.lambdas_filtered : k => v.triggers.s3 if length(lookup(lookup(v, "triggers", {}), "s3", [])) > 0 }
  lambda_s3_keys   = flatten([for k, v in local.lambda_s3_pre : formatlist("${k}-%s", [for i in v : i.bucket])])
  lambda_s3_values = flatten([for k, v in local.lambda_s3_pre : [for i in v : merge(i, { "lambda" : k })]])
  lambda_s3        = zipmap(local.lambda_s3_keys, local.lambda_s3_values)
  s3_lambdas       = { for k, v in local.lambda_s3 : v.bucket => v... }

  # Trigger SNS
  lambda_sns_pre    = { for k, v in local.lambdas_filtered : k => v.triggers.sns if length(lookup(lookup(v, "triggers", {}), "sns", [])) > 0 }
  lambda_sns_keys   = flatten([for k, v in local.lambda_sns_pre : formatlist("${k}-%s", [for i in v : i.topic])])
  lambda_sns_values = flatten([for k, v in local.lambda_sns_pre : [for i in v : merge(i, { "lambda" : k })]])
  lambda_sns        = zipmap(local.lambda_sns_keys, local.lambda_sns_values)

  # Trigger SQS
  lambda_sqs_pre    = { for k, v in local.lambdas_filtered : k => v.triggers.sqs if length(lookup(lookup(v, "triggers", {}), "sqs", [])) > 0 }
  lambda_sqs_keys   = flatten([for k, v in local.lambda_sqs_pre : formatlist("${k}-%s", [for i in v : i.queue])])
  lambda_sqs_values = flatten([for k, v in local.lambda_sqs_pre : [for i in v : merge(i, { "lambda" : k })]])
  lambda_sqs        = zipmap(local.lambda_sqs_keys, local.lambda_sqs_values)

  # Monorepo
  has_hooyu = !contains(var.skip_lambdas, "hooyu-api-authoriser") && !contains(var.skip_services, "hooyu-request-service")
}


