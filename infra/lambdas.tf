resource "aws_s3_bucket" "lambda_code_bucket" {
  #checkov:skip=CKV_AWS_144: No need for cross replication
  #checkov:skip=CKV2_AWS_62: No need for notification

  bucket = "${module.pdata.project}-lambda-source-code-${var.environment}"
  tags = {
    Name = "${module.pdata.project}-lambda-source-code-${var.environment}"
    role = "s3"
  }
}
resource "aws_s3_bucket_versioning" "lambda_code_bucket_versioning" {
  bucket = aws_s3_bucket.lambda_code_bucket.id
  versioning_configuration {
    status = "Enabled"
  }
}
resource "aws_s3_bucket_logging" "lambda_code_bucket_logging" {
  bucket        = aws_s3_bucket.lambda_code_bucket.id
  target_bucket = module.pdata.log_bucket
  target_prefix = "${aws_s3_bucket.lambda_code_bucket.id}-log/"
}

resource "aws_s3_bucket_lifecycle_configuration" "lambda_code_bucket_lifecycle" {
  bucket = aws_s3_bucket.lambda_code_bucket.id

  rule {
    id     = "managed"
    status = "Enabled"
    filter {
      prefix = ""
    }
    transition {
      days          = var.s3_default_settings.lifecycle_transition_oia_days
      storage_class = "ONEZONE_IA"
    }
    noncurrent_version_expiration {
      noncurrent_days = var.s3_default_settings.lifecycle_noncurrent_expiration_days
    }
    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }
  }
}
resource "aws_s3_bucket_policy" "lambda_code_bucket_policy" {
  for_each = aws_s3_bucket.bucket
  bucket   = aws_s3_bucket.lambda_code_bucket.id
  policy = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Deny",
        "Principal" : "*",
        "Action" : "*",
        "Resource" : [
          "arn:aws:s3:::${aws_s3_bucket.lambda_code_bucket.id}/*"
        ],
        "Condition" : {
          "Bool" : {
            "aws:SecureTransport" : "false"
          }
        }
      }
    ]
  })
}
resource "aws_s3_bucket_server_side_encryption_configuration" "lambda_code_bucket_encryption" {
  #checkov:skip=CKV2_AWS_67: module.pdata.kms_arn is rotated
  bucket = aws_s3_bucket.lambda_code_bucket.id

  rule {
    apply_server_side_encryption_by_default {
      kms_master_key_id = module.pdata.kms_arn
      sse_algorithm     = "aws:kms"
    }
  }
}

resource "aws_s3_bucket_public_access_block" "lambda_code_bucket_block" {
  bucket = aws_s3_bucket.lambda_code_bucket.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

resource "aws_security_group" "lambdas" {
  #checkov:skip=CKV2_AWS_5: Will be attached
  for_each    = local.lambda_vpc_sgs
  name        = "${var.environment}-lambda-${local.repo_namespace}-${each.key}"
  vpc_id      = module.pdata.vpc_id
  description = "SG for lambda-${local.repo_namespace}-${each.key}"

  egress {
    description = "Access to eks service"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [module.pdata.vpc_cidr]
  }
  dynamic "egress" {
    for_each = { for v in each.value.access_sgs : v["security_group_id"] => v }
    content {
      description     = "Access to ${egress.value.name}"
      from_port       = egress.value.from_port
      to_port         = egress.value.to_port
      protocol        = lookup(egress.value, "protocol", "tcp")
      security_groups = [egress.value.security_group_id]
    }
  }
  # If not vpc endpoint then need full outbound
  dynamic "egress" {
    for_each = each.value.has_all_endpoints ? [] : [1]
    content {
      description = "Access to AWS services"
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      cidr_blocks = ["0.0.0.0/0"]
    }
  }
  # Else assign all endpoints
  dynamic "egress" {
    for_each = each.value.has_all_endpoints ? each.value.vpc_endpoints_targets_sgs : {}
    content {
      description     = "Access to AWS services ${egress.key} vpc endpoint sg"
      from_port       = 443
      to_port         = 443
      protocol        = "tcp"
      security_groups = egress.value
    }
  }
  dynamic "egress" {
    for_each = each.value.has_all_endpoints ? each.value.vpc_endpoints_targets_prefix_list_id : {}
    content {
      description     = "Access to AWS services vpc ${egress.key} endpoint prefix id"
      from_port       = 443
      to_port         = 443
      protocol        = "tcp"
      prefix_list_ids = [egress.value]
    }
  }

  dynamic "egress" {
    for_each = each.value.has_all_endpoints && length(each.value.vpc_endpoints_available_via_shared_account) > 0 ? [1] : []
    content {
      description = "Access to AWS services via peer connection to vpc endpoints in ${module.pdata.shared_vpc_endpoint_account} account"
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      cidr_blocks = [module.pdata.shared_vpc_endpoint_service_cidr]
    }
  }
  tags = {
    Name = "${var.environment}-lambda-${local.repo_namespace}-${each.key}"
  }
}


resource "aws_security_group_rule" "lambda_allowed" {
  for_each                 = local.lambda_vpc_ingress
  security_group_id        = each.value.security_group_id
  type                     = "ingress"
  from_port                = each.value.from_port
  to_port                  = each.value.to_port
  protocol                 = lookup(each.value, "protocol", "tcp")
  description              = "Access from ${each.value.lambda} lambda"
  source_security_group_id = aws_security_group.lambdas[each.value.lambda].id
}
locals {
  # https://docs.aws.amazon.com/AmazonCloudWatch/latest/monitoring/Lambda-Insights-extension-versionsx86-64.html
  lambda_insight_layer = {
    "eu-west-1" = "arn:aws:lambda:eu-west-1:************:layer:LambdaInsightsExtension:21",
    "eu-west-2" = "arn:aws:lambda:eu-west-2:************:layer:LambdaInsightsExtension:21",
  }
}
module "lambdas" {
  for_each = local.lambdas_filtered
  source   = "terraform-aws-modules/lambda/aws"
  version  = "7.21.0"

  function_name            = "${var.environment}-${local.repo_namespace}-${each.key}"
  description              = lookup(each.value, "description", each.key)
  handler                  = each.value.handler
  runtime                  = lookup(each.value, "runtime", var.lambda_default_settings.runtime)
  memory_size              = lookup(each.value, "memory_size", var.lambda_default_settings.memory_size)
  publish                  = true
  hash_extra               = "${var.environment}-${local.repo_namespace}-${each.key}"
  timeout                  = lookup(each.value, "timeout", var.lambda_default_settings.timeout)
  recreate_missing_package = false # Dont rebuild just because checksum changed

  tracing_mode = "Active"

  trigger_on_package_timestamp = false
  store_on_s3                  = true
  s3_bucket                    = aws_s3_bucket.lambda_code_bucket.id
  s3_prefix                    = "${each.key}/"

  cloudwatch_logs_retention_in_days = module.pdata.log_retention_days
  logging_log_format                = "JSON"
  logging_application_log_level     = "DEBUG" # This do NOT set the log level on the lambda. It is handled via env vars. But CWL will filter logs below this log level

  source_path = lookup(each.value, "source_path", "${path.module}/../lambdas/${each.key}")

  environment_variables = lookup(each.value, "envs", {})

  vpc_subnet_ids         = module.pdata.private_subnet_ids
  vpc_security_group_ids = [aws_security_group.lambdas[each.key].id]
  attach_network_policy  = true

  layers = [
    local.lambda_insight_layer[module.pdata.region],
  ]

  create_role = true

  attach_policy_json = true

  assume_role_policy_statements = lookup(each.value, "assume_role_policy_statements", {})

  reserved_concurrent_executions = lookup(each.value, "reserved_concurrency", -1)

  policy_json = jsonencode({
    "Version" : "2012-10-17",
    "Statement" : concat([
      {
        "Effect" : "Allow",
        "Action" : "logs:CreateLogGroup",
        "Resource" : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        "Resource" : "arn:aws:logs:*:*:log-group:/aws/lambda-insights:*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "sqs:ReceiveMessage",
          "sqs:DeleteMessage",
          "sqs:GetQueueAttributes"
        ],
        "Resource" : [
          "*"
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "xray:PutTraceSegments",
          "xray:PutTelemetryRecords",
          "xray:GetSamplingRules",
          "xray:GetSamplingTargets",
          "xray:GetSamplingStatisticSummaries"
        ],
        "Resource" : [
          "*"
        ]
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "codeguru-profiler:ConfigureAgent",
          "codeguru-profiler:CreateProfilingGroup",
          "codeguru-profiler:PostAgentProfile"
        ],
        "Resource" : "arn:aws:codeguru-profiler:*:*:profilingGroup/*"
      },
      {
        "Sid" : "KMSAllow"
        "Effect" : "Allow"

        "Action" : [
          "kms:Decrypt",
          "kms:GenerateDataKey",
        ]

        "Resource" : [
          module.pdata.kms_arn
        ]
      }],
      lookup(each.value, "iam", [])
    )
  })
}

resource "aws_lambda_invocation" "restate_registration" {
  for_each      = local.restate_lambdas
  function_name = data.aws_lambda_function.restate_registrer.arn
  input = jsonencode({
    "uri" : module.lambdas[each.key].lambda_function_qualified_arn,
    "post" : lookup(each.value, "restate_post", []),
  })
}

# Trigger it
# Cron
resource "aws_cloudwatch_event_rule" "cron_lambdas" {
  for_each            = local.lambda_crons
  name                = "${var.environment}-${local.repo_namespace}-${each.value.name}-cronjob"
  description         = lookup(each.value, "description", "Trigger for ${module.lambdas[each.value.lambda].lambda_function_name}")
  schedule_expression = "cron(${each.value.cron})"
}

resource "aws_cloudwatch_event_target" "cron_lambdas" {
  for_each = local.lambda_crons
  rule     = aws_cloudwatch_event_rule.cron_lambdas[each.key].name
  arn      = module.lambdas[each.value.lambda].lambda_function_arn
  input    = lookup(each.value, "input", null) != null ? jsonencode(each.value.input) : null
}

resource "aws_lambda_permission" "cron_lambdas" {
  for_each      = local.lambda_crons
  statement_id  = substr("allow-${module.lambdas[each.value.lambda].lambda_function_name}-cron-${each.value.name}", 0, 100)
  action        = "lambda:InvokeFunction"
  principal     = "events.amazonaws.com"
  function_name = module.lambdas[each.value.lambda].lambda_function_arn
  source_arn    = aws_cloudwatch_event_rule.cron_lambdas[each.key].arn
}

# S3
# resource "aws_s3_bucket_notification" "s3_lambdas" {
#   for_each = local.lambda_s3
#   bucket   = aws_s3_bucket.bucket[each.value.bucket].bucket

#   dynamic "lambda_function" {
#     for_each = lookup(each.value, "notifications", [{}])
#     content {
#       lambda_function_arn = module.lambdas[each.value.lambda].lambda_function_arn
#       events              = lookup(lambda_function.value, "events", ["s3:ObjectCreated:*"])
#       filter_prefix       = lookup(lambda_function.value, "prefix", null)
#       filter_suffix       = lookup(lambda_function.value, "suffix", null)
#     }
#   }
#   depends_on = [
#     aws_lambda_permission.s3_lambdas,
#   ]
# }

resource "aws_lambda_permission" "s3_lambdas" {
  for_each      = local.lambda_s3
  statement_id  = substr("allow-${module.lambdas[each.value.lambda].lambda_function_name}-s3-${each.value.bucket}", 0, 100)
  action        = "lambda:InvokeFunction"
  function_name = module.lambdas[each.value.lambda].lambda_function_arn
  principal     = "s3.amazonaws.com"
  source_arn    = aws_s3_bucket.bucket[each.value.bucket].arn
}

# SNS
resource "aws_sns_topic_subscription" "sns_lambda_subscription" {
  for_each             = local.lambda_sns
  topic_arn            = aws_sns_topic.sns_topic[each.value.topic].arn
  protocol             = "lambda"
  endpoint             = module.lambdas[each.value.lambda].lambda_function_arn
  raw_message_delivery = lookup(each.value, "raw_message_delivery", false)
}

resource "aws_lambda_permission" "sns_lambda_permission" {
  for_each      = local.lambda_sns
  statement_id  = substr("allow-${module.lambdas[each.value.lambda].lambda_function_name}-sns-${each.value.topic}", 0, 100)
  action        = "lambda:InvokeFunction"
  principal     = "sns.amazonaws.com"
  function_name = module.lambdas[each.value.lambda].lambda_function_arn
  source_arn    = aws_sns_topic.sns_topic[each.value.topic].arn
}

# SQS
resource "aws_lambda_event_source_mapping" "sqs_lambda_trigger_event" {
  for_each                           = local.lambda_sqs
  event_source_arn                   = aws_sqs_queue.services[each.value.queue].arn
  function_name                      = module.lambdas[each.value.lambda].lambda_function_arn
  batch_size                         = lookup(each.value, "batch_size", 1)
  maximum_batching_window_in_seconds = lookup(each.value, "maximum_batching_window_in_seconds", lookup(each.value, "batch_size", 1) == 1 ? null : 5)
  function_response_types            = lookup(each.value, "function_response_types", lookup(each.value, "batch_size", 1) == 1 ? null : ["ReportBatchItemFailures"])


  dynamic "scaling_config" {
    for_each = lookup(each.value, "maximum_concurrency", null) != null ? [1] : []
    content {
      maximum_concurrency = each.value.maximum_concurrency
    }
  }
}

# Access to DBs
resource "aws_lambda_invocation" "lambda_db_autoroles" {
  for_each      = local.lambda_db_accesses
  function_name = data.aws_lambda_function.db_autoroles.arn
  input = jsonencode({
    "secretname" : local.db_secret_names[each.value.name],
    "userdetails" : [
      {
        "name" : replace(lower("lambda_service_${local.repo_namespace}_${each.value.lambda}"), "/[^a-z_]*/", ""),
        "iamauth" : true,
        "roles" : [each.value.access]
      }
    ]
  })
  depends_on = [aws_lambda_invocation.db_autoroles]
}