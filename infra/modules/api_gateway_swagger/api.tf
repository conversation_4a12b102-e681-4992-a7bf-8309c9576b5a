# API Gateway 
resource "aws_api_gateway_rest_api" "api_gateway" {
  name                         = "${var.prefix}apigateway-${var.identifier}"
  description                  = "${var.prefix}apigateway-${var.identifier}"
  body                         = var.api_definition
  disable_execute_api_endpoint = true
  lifecycle {
    create_before_destroy = true
  }

  dynamic "endpoint_configuration" {
    for_each = length(var.api_vpc_endpoint_ids) > 0 ? [1] : []
    content {
      types            = ["PRIVATE"]
      vpc_endpoint_ids = var.api_vpc_endpoint_ids
    }
  }
}


resource "aws_api_gateway_rest_api_policy" "api_gateway" {
  rest_api_id = aws_api_gateway_rest_api.api_gateway.id
  policy = var.api_policy != "" ? var.api_policy : jsonencode({
    "Version" : "2012-10-17",
    "Statement" : [
      {
        "Effect" : "Deny",
        "Principal" : "*",
        "Action" : "execute-api:Invoke",
        "Resource" : "${aws_api_gateway_rest_api.api_gateway.execution_arn}/*",
        "Condition" : {
          "StringNotEquals" : {
            "aws:sourceVpc" : var.vpc_id
          }
        }
      },
      {
        "Effect" : "Allow",
        "Principal" : "*",
        "Action" : "execute-api:Invoke",
        "Resource" : "${aws_api_gateway_rest_api.api_gateway.execution_arn}/*"
      }
    ]
  })
}

# API Gateway Deployment - Refer to values in template
resource "aws_api_gateway_deployment" "api_gateway_deployment" {
  rest_api_id = aws_api_gateway_rest_api.api_gateway.id
  description = "Created by terraform"
  triggers = {
    redeployment = sha1(jsonencode(aws_api_gateway_rest_api.api_gateway.body))
    repolicy     = sha1(jsonencode(aws_api_gateway_rest_api_policy.api_gateway.policy))
  }
  lifecycle {
    create_before_destroy = true
  }
}

# Cloud watch log group
resource "aws_cloudwatch_log_group" "apigateway_api_gateway_stage" {
  #checkov:skip=CKV_AWS_338: another lambda handle the retention time
  #checkov:skip=CKV_AWS_158: CWL get auto encrypted
  name              = "/aws/api/${aws_api_gateway_rest_api.api_gateway.name}"
  retention_in_days = var.log_retention_days
}

# API Gateway Stage - Refer to values in template
resource "aws_api_gateway_stage" "api_gateway_stage" {
  #checkov:skip=CKV_AWS_120: Caching is not needed for this endpoint
  #checkov:skip=CKV2_AWS_51: Not using client certs
  #checkov:skip=CKV2_AWS_29: Restricted API, no need for WAF
  deployment_id        = aws_api_gateway_deployment.api_gateway_deployment.id
  rest_api_id          = aws_api_gateway_rest_api.api_gateway.id
  stage_name           = "stage"
  xray_tracing_enabled = true
  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.apigateway_api_gateway_stage.arn
    format = jsonencode({
      "requestId" : "$context.requestId",
      "ip" : "$context.identity.sourceIp",
      "caller" : "$context.identity.caller",
      "user" : "$context.identity.user",
      "requestTime" : "$context.requestTime",
      "httpMethod" : "$context.httpMethod",
      "resourcePath" : "$context.resourcePath",
      "status" : "$context.status",
      "protocol" : "$context.protocol",
      "responseLength" : "$context.responseLength"
    })
  }
}

resource "aws_api_gateway_base_path_mapping" "api_route" {
  count = var.api_domain_name != "" ? 1 : 0

  domain_name = var.api_domain_name
  api_id      = aws_api_gateway_rest_api.api_gateway.id
  stage_name  = aws_api_gateway_stage.api_gateway_stage.stage_name
  base_path   = var.api_domain_base_path
}

# API Gateway Method - Refer to values in template
resource "aws_api_gateway_method_settings" "api_gateway_settings" {
  #checkov:skip=CKV_AWS_120: Caching is not needed for this endpoint
  #checkov:skip=CKV_AWS_225: Caching is not needed for this endpoint
  #checkov:skip=CKV_AWS_276: Data trace is useful
  rest_api_id = aws_api_gateway_rest_api.api_gateway.id
  stage_name  = aws_api_gateway_stage.api_gateway_stage.stage_name
  method_path = "*/*"

  settings {
    metrics_enabled      = true
    logging_level        = "INFO"
    data_trace_enabled   = true
    cache_data_encrypted = true
  }
}