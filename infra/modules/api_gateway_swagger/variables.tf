variable "prefix" {
  type    = string
  default = ""
}
variable "identifier" {
  type = string
}
variable "api_definition" {
  type        = string
  description = ""
}
variable "vpc_id" {
  type        = string
  description = ""
}
variable "api_vpc_endpoint_ids" {
  type        = list(string)
  default     = []
  description = ""
}
variable "api_policy" {
  type        = string
  default     = ""
  description = ""
}
variable "log_retention_days" {
  type        = number
  default     = 30
  description = ""
}
variable "api_domain_name" {
  type        = string
  default     = ""
  description = ""
}
variable "api_domain_base_path" {
  type        = string
  default     = ""
  description = ""
}