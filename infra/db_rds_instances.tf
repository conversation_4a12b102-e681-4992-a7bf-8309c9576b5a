data "aws_rds_engine_version" "postgresql" {
  for_each = local.rds_db_instances
  engine   = "postgres"
  version  = lookup(each.value, "engine_version", var.db_default_settings.engine_version)
}

resource "aws_db_parameter_group" "dbs_parameter_group" {
  for_each    = local.rds_db_instances
  name        = "${var.environment}-${each.key}-postgre-pg-${split(".", data.aws_rds_engine_version.postgresql[each.key].version)[0]}"
  family      = data.aws_rds_engine_version.postgresql[each.key].parameter_group_family
  description = "postgres parameter group for ${each.key}"

  dynamic "parameter" {
    for_each = merge({
      "max_slot_wal_keep_size" : { value : 2048 }, # MB https://www.postgresql.org/docs/current/runtime-config-replication.html#RUNTIME-CONFIG-REPLICATION-SENDER
      "log_connections" : { value : 1 },
      "log_disconnections" : { value : 1 },
      "log_error_verbosity" : { value : "default" },
      "log_statement" : { "value" : var.environment == "prod" ? "mod" : "none" },
    }, lookup(each.value, "parameters", {}))
    content {
      apply_method = lookup(parameter.value, "apply_method", "immediate")
      name         = parameter.key
      value        = parameter.value["value"]
    }
  }
}

resource "aws_db_subnet_group" "dbs_subnet_group" {
  for_each    = local.rds_db_instances
  name        = "${var.environment} ${each.key} subnet group"
  description = "${var.environment} ${each.key} subnet group"
  subnet_ids  = module.pdata.data_subnet_ids
}

resource "aws_secretsmanager_secret_version" "dbs_secret_val" {
  for_each      = local.rds_db_instances
  secret_id     = aws_secretsmanager_secret.dbs_secret[each.key].id
  secret_string = "postgres://${local.db_admin_username}:${random_password.dbs_random_string[each.key].result}@${aws_db_instance.dbs[each.key].address}:${aws_db_instance.dbs[each.key].port}/${aws_db_instance.dbs[each.key].db_name}"

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_db_instance" "dbs" {
  #checkov:skip=CKV_AWS_133: 2 days default retention
  #checkov:skip=CKV_AWS_211: Ensure RDS uses a modern CaCert
  for_each              = local.rds_db_instances
  identifier            = "${var.environment}-${each.key}"
  allocated_storage     = lookup(each.value, "allocated_storage", var.db_default_settings.allocated_storage)
  max_allocated_storage = lookup(each.value, "max_allocated_storage", var.db_default_settings.max_allocated_storage)
  engine                = data.aws_rds_engine_version.postgresql[each.key].engine
  engine_version        = lookup(each.value, "engine_version", var.db_default_settings.engine_version)
  instance_class        = lookup(each.value, "instance_class", var.db_default_settings.instance_class)
  multi_az              = lookup(each.value, "multi_az", module.pdata.rds_multi_az)
  username              = local.db_admin_username
  password              = random_password.dbs_random_string[each.key].result
  port                  = 5432
  publicly_accessible   = false
  db_subnet_group_name  = aws_db_subnet_group.dbs_subnet_group[each.key].id
  parameter_group_name  = aws_db_parameter_group.dbs_parameter_group[each.key].name
  ca_cert_identifier    = lookup(each.value, "ca_cert_name", var.db_default_settings.ca_cert_name)
  storage_encrypted     = true
  storage_type          = lookup(each.value, "storage_type", "gp3")
  kms_key_id            = lookup(each.value, "kms_key_id", module.pdata.kms_arn)
  vpc_security_group_ids = [
    aws_security_group.dbs[each.key].id,
    aws_security_group.dbs_accessor[each.key].id,
  ]

  apply_immediately          = var.environment != "prod"
  backup_retention_period    = lookup(each.value, "backup_retention_period", var.db_default_settings.backup_retention_period)
  db_name                    = lookup(each.value, "db_name", var.db_default_settings.db_name)
  final_snapshot_identifier  = "${each.key}-db-final-snapshot-${var.environment}"
  auto_minor_version_upgrade = true
  backup_window              = "01:30-02:00"
  maintenance_window         = module.pdata.rds_maintenance_window
  deletion_protection        = "true"
  monitoring_interval        = module.pdata.is_prod_env ? 5 : 60
  monitoring_role_arn        = aws_iam_role.rds_monitoring_role.arn
  enabled_cloudwatch_logs_exports = lookup(each.value, "cloudwatch_logs",
    [
      "postgresql",
      "upgrade",
      "iam-db-auth-error",
    ]
  )
  iam_database_authentication_enabled   = true
  copy_tags_to_snapshot                 = true
  performance_insights_enabled          = lookup(each.value, "performance_insights_enabled", module.pdata.is_prod_env)
  performance_insights_kms_key_id       = lookup(each.value, "performance_insights_enabled", module.pdata.is_prod_env) ? lookup(each.value, "performance_insights_kms_key_id", lookup(var.db_default_settings, "performance_insights_kms_key_id", data.aws_kms_key.rds.arn)) : null
  performance_insights_retention_period = lookup(each.value, "performance_insights_enabled", module.pdata.is_prod_env) ? 7 : 0

  tags = {
    description                    = each.key
    role                           = each.key
    projectName                    = module.pdata.project
    identifier                     = each.key
    "team"                         = "engineering"
    "department"                   = "cdd"
    "service"                      = each.key
    (module.pdata.aws_backup_key)  = module.pdata.aws_backup_value
    "${var.environment}-scheduled" = module.pdata.scheduled ? "on" : "off"
  }
}

locals {
  aws_db_instance_infos = {
    for k, v in local.rds_db_instances : k => {
      "root_username"         = local.db_admin_username
      "root_password"         = random_password.dbs_random_string[k].result
      "service_username"      = "service_${replace(k, "-", "_")}"
      "service_read_username" = "service_${replace(k, "-", "_")}_read"
      "endpoint"              = aws_db_instance.dbs[k].address
      "resource_id"           = aws_db_instance.dbs[k].resource_id
      "reader_endpoint"       = aws_db_instance.dbs[k].address
      "port"                  = aws_db_instance.dbs[k].port
      "dbname"                = aws_db_instance.dbs[k].db_name
      "identifier"            = aws_db_instance.dbs[k].identifier
      "sg"                    = aws_security_group.dbs_accessor[k].id
    }
  }
}