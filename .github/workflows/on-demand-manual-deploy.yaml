name: On demand manual deploy

on:
  workflow_dispatch:
    inputs:
      target_env:
        type: choice
        description: Target Env
        options:
          - test-1
          - test-2
          - preprod
          - prod

# IMPOSSIBLE to get the inspector precheck mapping as env var: https://github.com/actions/runner/issues/1189
# MAPPING: {"preprod":"test-2","prod":"preprod"}
jobs:
  # inspector:
  #   if: ${{ fromJSON('{"preprod":"test-2","prod":"preprod"}')[inputs.target_env] != '' }}
  #   uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/inspector_check.yaml@main
  #   with:
  #     environment_name: scf_${{ fromJSON('{"preprod":"test-2","prod":"preprod"}')[inputs.target_env] }}
  approve:
    if: ${{ !cancelled() }}
    # needs: [inspector]
    runs-on: pcoe-runner-linux-small-prod
    environment: "${{ inputs.target_env == 'prod' && 'approve' || '' }}"
    steps:
      - name: Echo
        run: echo "${{ github.ref_name }} - ${{ github.git_ref }} -> Approved"

  # list services/db migrations
  filters:
    if: ${{ !cancelled() }}
    needs:
      - approve
    runs-on: pcoe-runner-linux-small-prod
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: chrisdickinson/setup-yq@v1.0.1
        with:
          yq-version: v4.28.1

      - name: Pre list
        id: prelist
        run: |
          SKIP=`yq -r -I=0 -o=json '.skip // {} | .${{ inputs.target_env }} // [""] ' orchestration.yaml`
          echo "Skipping services: $SKIP"
          services=$(yq -r -o=json ".services // {}  | del(.$SKIP) | keys" orchestration.yaml)
          echo 'services<<EOF' >> $GITHUB_OUTPUT
          echo ${services} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

          restateservices=$(yq -r -o=json ".restate-services // {} | del(.$SKIP) | keys" orchestration.yaml)
          echo 'restateservices<<EOF' >> $GITHUB_OUTPUT
          echo ${restateservices} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

          servicedbmigrations=$(yq -r -o=json ".migrations-db // {} | del(.$SKIP) | keys" orchestration.yaml)
          echo 'servicedbmigrations<<EOF' >> $GITHUB_OUTPUT
          echo ${servicedbmigrations} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

          declare -A accounts=( ["test-1"]="************" ["test-2"]="************" ["preprod"]="************" ["prod"]="************")
          ac_id="${accounts[${{ inputs.target_env }}]}"
          echo "ac_id=${ac_id}" >> $GITHUB_OUTPUT

    outputs:
      services: ${{ steps.prelist.outputs.services }}
      restateservices: ${{ steps.prelist.outputs.restateservices }}
      servicedbmigrations: ${{ steps.prelist.outputs.servicedbmigrations }}
      account_id: ${{ steps.prelist.outputs.ac_id }}

  terraform-apply:
    if: ${{ !cancelled() }}
    needs:
      - approve
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/terraform.yml@main
    with:
      infra_dir_path: infra/
      target_env_name: ${{ inputs.target_env }}
      aws_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      terraform_apply: true
      pre_apply_script: npm install -g esbuild@0.19.11
      rsync_install: true
      vault_role_id: ff98ebe8-61a4-65f0-3e32-59d4e40d143e
      terraform_version: 1.6.6
      nodejs_version: 'lts/*'
      npm_url: https://npm.pkg.github.com
      python_version: '3.11'
    secrets:
      infracost_api_key: ${{ secrets.INFRACOST_API_KEY }}
      github_app_id: ${{ secrets.TF_MODULES_GITHUB_APP_ID }}
      github_app_private_key: ${{ secrets.TF_MODULES_GITHUB_PRIVATE_KEY }}
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}
      vault_secret_id: ${{ secrets.VAULT_SECRET_ID }}

  # wait-for-inspector:
  #   if: ${{ !cancelled() }}
  #   needs: [terraform-apply]
  #   runs-on: pcoe-runner-linux-small-prod
  #   steps:
  #     - name: Wait for Inspector scans to finish
  #       run: sleep 120
  #       continue-on-error: true
  # inspector-post:
  #   needs: [wait-for-inspector]
  #   if: ${{ !cancelled() }}
  #   uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/inspector_check.yaml@main
  #   with:
  #     environment_name: scf_${{ inputs.target_env }}

  svc-build:
    needs: filters
    if: ${{ !cancelled() && needs.filters.outputs.services != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.services) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: services/${{ matrix.service }}
      target_env_name: ${{ inputs.target_env }}
      target_branch_name: argo-sync-${{ inputs.target_env }}
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      version: ${{ github.ref_name }}
      deploy_location: artifact
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  restate-services-changed-deploy:
    needs: filters
    if: ${{ !cancelled() && needs.filters.outputs.restateservices != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.restateservices) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_restate_helm.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: restate-services/${{ matrix.service }}
      target_env_name: ${{ inputs.target_env }}
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      aws_service_account_arn: 'arn:aws:iam::${{ needs.filters.outputs.account_id }}:role/SSOAdmin'
      version: ${{ github.ref_name }}
    secrets:
      kpmg_helm_password: ${{ secrets.KPMG_HELM_PASSWORD }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  svcdb-build:
    needs: filters
    if: ${{ !cancelled() && needs.filters.outputs.servicedbmigrations != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.servicedbmigrations) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: db/${{ matrix.service }}
      target_env_name: ${{ inputs.target_env }}
      target_branch_name: argo-sync-${{ inputs.target_env }}
      values_repository_keypath: '.job.image.repository'
      k8s_image_version_value_keypath: '.job.image.tag'
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      version: ${{ github.ref_name }}
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  services-changed-deploy:
    needs:
      - svc-build
      - svcdb-build
    if: ${{ !cancelled() && (needs.filters.outputs.servicechanges != '[]' || needs.filters.outputs.servicedbmigrations != '[]' ) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/gather-script-commit.yaml@main
    with:
      target_branch_name: argo-sync-${{ inputs.target_env }}
      commit_message: argocd k8s auto ${{ inputs.target_env }} ${{ github.ref_name }}
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  get-argo-creds:
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/get-argo-creds.yaml@main
    with:
      friendly_name: scf_${{ contains(fromJSON('["preprod", "prod"]'), inputs.target_env) && 'prod' || 'test-1' }}
    secrets:
      VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

  argo-trigger-sync:
    needs:
      - get-argo-creds
      - services-changed-deploy
    if: ${{ !cancelled() && (needs.filters.outputs.servicechanges != '[]' || needs.filters.outputs.servicedbmigrations != '[]' ) }}
    runs-on: pcoe-runner-linux-small-prod
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJson(needs.services-changed-deploy.outputs.changes) }}
    steps:
      - name: Format argo service name
        id: argo-service-name
        run: |
          service_name="${{ inputs.target_env }}-mono-"
          service_name+=`echo '${{ matrix.service }}' | grep 'k8s/' | cut -d '/' -f 2`
          echo $service_name
          echo "service_name=$service_name" >> $GITHUB_OUTPUT

      - name: Sync ArgoCD App
        if: ${{ steps.argo-service-name.outputs.service_name != '' }}
        uses: KPMG-UK/ie-cdd-github-actions-library/actions/argo-sync-service@main
        with:
          service_name: ${{ steps.argo-service-name.outputs.service_name }}
          argo_context: ${{ needs.get-argo-creds.outputs.argo_config }}

  notify:
    if: ${{ !cancelled() && inputs.target_env == 'prod' }}
    needs:
      - terraform-apply
      - services-changed-deploy
      - restate-services-changed-deploy
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/aws_authed_run_script.yml@main
    with:
      bash_steps: |
        echo '{
          "Subject": {
            "Data": "Prod Monorepo Release '${{ github.ref_name }}' Completed",
            "Charset": "UTF-8"
          },
          "Body": {
            "Text": {
              "Data": "${{ github.ref_name }} released",
              "Charset": "UTF-8"
            },
            "Html": {
              "Data": "${{ github.ref_name }} was released to production: <a class=\"ulink\" href=\"${{github.server_url}}/${{github.repository}}/actions/runs/${{github.run_id}}\" target=\"_blank\">Action link</a>.",
              "Charset": "UTF-8"
            }
          }
        }' > message.json
        aws ses send-email --from <EMAIL> --destination 'ToAddresses=<EMAIL>,<EMAIL>,<EMAIL>' --message file://message.json
      aws_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_sub_role_arn: 'arn:aws:iam::600859352358:role/SSOAdmin'

  auto-prod:
    if: ${{ !cancelled() && inputs.target_env == 'prod' }}
    needs:
      - terraform-apply
      - services-changed-deploy
      - restate-services-changed-deploy
    runs-on: pcoe-runner-linux-small-prod
    steps:
      - name: Echo
        run: echo "Post prod step"

      - name: Service Team token
        uses: navikt/github-app-token-generator@v1.2.1
        # https://github.com/navikt/github-app-token-generator/commits/main
        id: get-service-token
        with:
          private-key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
          app-id: ${{ secrets.GIT_APP_ID }}

      - name: Checkout auto-prod
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 0 indicates all history for all branches and tags
          show-progress: false
          fetch-tags: true
          ref: auto-prod

      - name: Set GitHub Token
        run: |
          git config --local --remove-section http."https://github.com/"
          git config --global url."https://x-access-token:${GITHUB_TOKEN}@github.com/KPMG-UK".insteadOf "https://github.com/KPMG-UK"
        env:
          GITHUB_TOKEN: ${{ steps.get-service-token.outputs.token }}

      - name: Sync auto-prod
        run: |
          GIT_COMMIT=`git show -s --format="%H" "${{ github.ref_name }}"`
          echo "Hard reseting auto-prod to ${{ github.ref_name }} -> ${GIT_COMMIT}"
          git reset --hard "${GIT_COMMIT}"
          git push --force --set-upstream "origin" "HEAD:refs/heads/auto-prod"
        env:
          GIT_AUTHOR_EMAIL: <EMAIL>
          GIT_AUTHOR_NAME: infrastructure-cdd-jenkins
          GIT_COMMITTER_EMAIL: <EMAIL>
          GIT_COMMITTER_NAME: infrastructure-cdd-jenkins
