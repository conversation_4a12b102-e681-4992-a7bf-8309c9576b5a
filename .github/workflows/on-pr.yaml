name: On PR

on:
  workflow_dispatch:
  pull_request:

jobs:
  general:
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/general_ci.yml@main
    with:
      include_dependency_review: false

  # check what has changed
  filters:
    runs-on: pcoe-runner-linux-small-prod
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: ch<PERSON><PERSON><PERSON><PERSON>/setup-yq@v1.0.1
        with:
          yq-version: v4.28.1

      - name: Split filter files
        run: |
          yq '.infra // {}' orchestration.yaml > orchestration-infra.yaml
          yq '.lambdas // {}' orchestration.yaml > orchestration-lambdas.yaml
          yq '.migrations-db // {}' orchestration.yaml > orchestration-migrations-db.yaml
          yq '.services // {}' orchestration.yaml > orchestration-services.yaml
          yq '.restate-services // {}' orchestration.yaml > orchestration-restate-services.yaml

      - name: Filter infra
        uses: dorny/paths-filter@v2.11.1
        id: infrafilters
        with:
          filters: orchestration-infra.yaml

      - name: Filter lambdas
        uses: dorny/paths-filter@v2.11.1
        id: lambdasfilters
        with:
          filters: orchestration-lambdas.yaml

      - name: Filter DB migrations
        uses: dorny/paths-filter@v2.11.1
        id: servicedbmigrations
        with:
          filters: orchestration-migrations-db.yaml

      - name: Filter services
        uses: dorny/paths-filter@v2.11.1
        id: servicefilters
        with:
          filters: orchestration-services.yaml

      - name: Filter restate-services
        uses: dorny/paths-filter@v2.11.1
        id: restateservicefilters
        with:
          filters: orchestration-restate-services.yaml

      - name: Post filter
        id: postfilter
        run: |
          # Among service changed, are they python or nodes
          echo '---' > servicesnodejs.yaml
          echo '---' > servicesnodejsapi.yaml
          echo '---' > servicesnodejswebapp.yaml
          echo '---' > servicespython.yaml
          for i in $(echo '${{ steps.servicefilters.outputs.changes }}' | jq -r '.[]'); do
            if [ -f "services/${i}/package.json" ]; then
              echo "Changed service '${i}' is a nodejs service"
              if [ -f "services/${i}/api/package-lock.json" ]; then
                  echo "- ${i}" >> servicesnodejsapi.yaml
              fi
              if [ -f "services/${i}/webapp/package-lock.json" ]; then
                  echo "- ${i}" >> servicesnodejswebapp.yaml
              fi
              if [ -f "services/${i}/package-lock.json" ]; then
                  echo "- ${i}" >> servicesnodejs.yaml
              fi
            else
              echo "Changed service '${i}' is a python service"
              echo "- ${i}" >> servicespython.yaml
            fi
          done

          nodejsserviceapichanges=$(yq -r -o=json '. // []' servicesnodejsapi.yaml)
          echo 'nodejsserviceapichanges<<EOF' >> $GITHUB_OUTPUT
          echo ${nodejsserviceapichanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT
          nodejsservicewebappchanges=$(yq -r -o=json '. // []' servicesnodejswebapp.yaml)
          echo 'nodejsservicewebappchanges<<EOF' >> $GITHUB_OUTPUT
          echo ${nodejsservicewebappchanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT
          nodejsservicechanges=$(yq -r -o=json '. // []' servicesnodejs.yaml)
          echo 'nodejsservicechanges<<EOF' >> $GITHUB_OUTPUT
          echo ${nodejsservicechanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

          pythonservicechanges=$(yq -r -o=json '. // []' servicespython.yaml)
          echo 'pythonservicechanges<<EOF' >> $GITHUB_OUTPUT
          echo ${pythonservicechanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

    outputs:
      infrachanges: ${{ steps.infrafilters.outputs.changes }}
      lambdachanges: ${{ steps.lambdasfilters.outputs.changes }}
      servicedbmigrations: ${{ steps.servicedbmigrations.outputs.changes }}
      nodejsserviceapichanges: ${{ steps.postfilter.outputs.nodejsserviceapichanges }}
      nodejsservicewebappchanges: ${{ steps.postfilter.outputs.nodejsservicewebappchanges }}
      nodejsservicechanges: ${{ steps.postfilter.outputs.nodejsservicechanges }}
      pythonservicechanges: ${{ steps.postfilter.outputs.pythonservicechanges }}
      restateservicechanges: ${{ steps.restateservicefilters.outputs.changes }}

  terraform-validate:
    needs: filters
    if: ${{ needs.filters.outputs.infrachanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        # This is a fake matrix, just to keep code coherent
        service: ${{ fromJSON(needs.filters.outputs.infrachanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/terraform.yml@main
    with:
      infra_dir_path: infra/
      target_env_name: ${{ github.event.pull_request.base.ref == 'main' && 'test-1' || 'prod' }}
      aws_role_arn: 'arn:aws:iam::277165315813:role/github-ie-cdd-general-read'
      authenticate_gcp: false
      vault_role_id: ff98ebe8-61a4-65f0-3e32-59d4e40d143e
      terraform_version: 1.6.6
    secrets:
      infracost_api_key: ${{ secrets.INFRACOST_API_KEY }}
      github_app_id: ${{ secrets.TF_MODULES_GITHUB_APP_ID }}
      github_app_private_key: ${{ secrets.TF_MODULES_GITHUB_PRIVATE_KEY }}
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}
      vault_secret_id: ${{ secrets.VAULT_SECRET_ID }}

  lambdas-validate:
    needs: filters
    if: ${{ needs.filters.outputs.lambdachanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.lambdachanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/service_node_ci.yml@main
    with:
      k8s_dir_path: ''
      nodejs_dir_path: lambdas/${{ matrix.service }}
      node_version_file: lambdas/${{ matrix.service }}/package.json

  migrations-db-validate:
    needs: filters
    if: ${{ needs.filters.outputs.servicedbmigrations != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.servicedbmigrations) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/postgrator_ci.yml@main
    with:
      db_scripts_path: db/${{ matrix.service }}/scripts
      k8s_dir_path: k8s/${{ matrix.service }}

  services-nodejs-validate:
    needs: filters
    if: ${{ needs.filters.outputs.nodejsservicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.nodejsservicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/service_node_ci.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      nodejs_dir_path: services/${{ matrix.service }}
      node_version_file: services/${{ matrix.service }}/package.json

  services-nodejsapi-validate:
    needs: filters
    if: ${{ needs.filters.outputs.nodejsserviceapichanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.nodejsserviceapichanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/service_node_ci.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      nodejs_dir_path: services/${{ matrix.service }}/api
      node_version_file: services/${{ matrix.service }}/api/package.json

  services-nodejswebapp-validate:
    needs: filters
    if: ${{ needs.filters.outputs.nodejsservicewebappchanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.nodejsservicewebappchanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/service_node_ci.yml@main
    with:
      k8s_dir_path: ''
      nodejs_dir_path: services/${{ matrix.service }}/webapp
      node_version_file: services/${{ matrix.service }}/webapp/package.json

  services-python-validate:
    needs: filters
    if: ${{ needs.filters.outputs.pythonservicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.pythonservicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/service_python_ci.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      source: services/${{ matrix.service }}
      python_version: '3.11'
      ruff_auto_format: true
      ruff_auto_lint: true

  restate-services-validate:
    needs: filters
    if: ${{ needs.filters.outputs.restateservicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.restateservicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/service_node_ci.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      nodejs_dir_path: restate-services/${{ matrix.service }}
      node_version_file: restate-services/${{ matrix.service }}/package.json

  final-validate:
    # Make sure to include all jobs
    needs:
      - general
      - terraform-validate
      - migrations-db-validate
      - lambdas-validate
      - services-nodejs-validate
      - services-nodejsapi-validate
      - services-nodejswebapp-validate
      - services-python-validate
      - restate-services-validate
    runs-on: pcoe-runner-linux-small-prod
    if: ${{ !cancelled() }}
    steps:
      - uses: martialonline/workflow-status@v3
        id: check
      - name: fail the workflow checked job
        if: steps.check.outputs.status == 'failure'
        run: exit 1
