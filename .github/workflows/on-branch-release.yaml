name: On push to release

on:
  workflow_dispatch:
  push:
    branches:
      - release

jobs:
  # check what has changed
  filters:
    runs-on: pcoe-runner-linux-small-prod
    steps:
      - name: Wait For Other Pipelines
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        uses: otto-de/github-actions-wait@v1.0
        with:
          step-name: Wait For Other Pipelines

      - name: Checkout
        uses: actions/checkout@v4

      - uses: ch<PERSON><PERSON><PERSON><PERSON>/setup-yq@v1.0.1
        with:
          yq-version: v4.28.1

      - name: Split filter files
        run: |
          yq '.migrations-db // {}' orchestration.yaml > orchestration-migrations-db.yaml
          yq '.services // {}' orchestration.yaml > orchestration-services.yaml
          yq '.restate-services // {}' orchestration.yaml > orchestration-restate-services.yaml

      - name: Filter DB migrations
        uses: dorny/paths-filter@v2.11.1
        id: servicedbmigrations
        with:
          base: release
          filters: orchestration-migrations-db.yaml

      - name: Filter services
        uses: dorny/paths-filter@v2.11.1
        id: servicefilters
        with:
          base: release
          filters: orchestration-services.yaml

      - name: Filter restate-services
        uses: dorny/paths-filter@v2.11.1
        id: restateservicefilters
        with:
          base: release
          filters: orchestration-restate-services.yaml

      - name: Post filter
        id: postfilter
        run: |
          nonservicechanges=$(yq -r -o=json '. | keys - ${{ steps.servicefilters.outputs.changes }}' orchestration-services.yaml)
          echo "nonservicechanges: ${nonservicechanges}"
          echo 'nonservicechanges<<EOF' >> $GITHUB_OUTPUT
          echo ${nonservicechanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

          nonrestateservicechanges=$(yq -r -o=json '. | keys - ${{ steps.restateservicefilters.outputs.changes }}' orchestration-restate-services.yaml)
          echo "nonrestateservicechanges: ${nonrestateservicechanges}"
          echo 'nonrestateservicechanges<<EOF' >> $GITHUB_OUTPUT
          echo ${nonrestateservicechanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

          nondbmigrationschanges=$(yq -r -o=json '. | keys - ${{ steps.servicedbmigrations.outputs.changes }}' orchestration-migrations-db.yaml)
          echo "nondbmigrationschanges: ${nondbmigrationschanges}"
          echo 'nondbmigrationschanges<<EOF' >> $GITHUB_OUTPUT
          echo ${nondbmigrationschanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

    outputs:
      servicechanges: ${{ steps.servicefilters.outputs.changes }}
      restateservicechanges: ${{ steps.restateservicefilters.outputs.changes }}
      servicedbmigrations: ${{ steps.servicedbmigrations.outputs.changes }}
      nonservicechanges: ${{ steps.postfilter.outputs.nonservicechanges }}
      nonrestateservicechanges: ${{ steps.postfilter.outputs.nonrestateservicechanges }}
      nonservicedbmigrations: ${{ steps.postfilter.outputs.nondbmigrationschanges }}

  svc-chg-dp:
    needs: filters
    if: ${{ needs.filters.outputs.servicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.servicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: services/${{ matrix.service }}
      target_env_name: release
      target_branch_name: ""
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      include_deploy: false # do not deploy
      retag_env: false # not a retag
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  svc-nchg-tag:
    needs:
      # Non changed afterwards to speed up
      - filters
      - waitchanges
    if: ${{ !cancelled() && needs.filters.outputs.nonservicechanges != '[]' }}
    # See: https://github.com/orgs/community/discussions/68214
    # if: ${{ needs.filters.outputs.nonservicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.nonservicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: services/${{ matrix.service }}
      target_env_name: release
      target_branch_name: ""
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      include_deploy: false # do not build
      retag_env: true # retag from last env marker
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  restate-svc-chg-dp:
    needs: filters
    if: ${{ needs.filters.outputs.restateservicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.restateservicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_restate_helm.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: restate-services/${{ matrix.service }}
      target_env_name: release
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      aws_service_account_arn: 'arn:aws:iam::************:role/SSOAdmin' # NP account
      include_deploy: false # do not build
      retag_env: false # not a retag
    secrets:
      kpmg_helm_password: ${{ secrets.KPMG_HELM_PASSWORD }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  restate-svc-nchg-tag:
    needs:
      # Non changed afterwards to speed up
      - filters
      - waitchanges
    if: ${{ !cancelled() && needs.filters.outputs.nonrestateservicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.nonrestateservicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main # Same build steps does not matter
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: restate-services/${{ matrix.service }}
      target_env_name: release
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      include_deploy: false # do not build
      retag_env: true # retag from last env marker
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  svcdb-chg-dp:
    needs: filters
    if: ${{ needs.filters.outputs.servicedbmigrations != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.servicedbmigrations) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: db/${{ matrix.service }}
      target_env_name: release
      target_branch_name: ""
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      values_repository_keypath: '.job.image.repository'
      k8s_image_version_value_keypath: '.job.image.tag'
      include_deploy: false # build only
      retag_env: false # not a retag
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  svcdb-nchg-tag:
    needs:
      # Non changed afterwards to speed up
      - filters
      - waitchanges
    if: ${{ !cancelled() && needs.filters.outputs.nonservicedbmigrations != '[]' }}
    # See: https://github.com/orgs/community/discussions/68214
    # if: ${{ needs.filters.outputs.nonservicedbmigrations != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.nonservicedbmigrations) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: db/${{ matrix.service }}
      target_env_name: release
      target_branch_name: ""
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      values_repository_keypath: '.job.image.repository'
      k8s_image_version_value_keypath: '.job.image.tag'
      include_deploy: false # do not build
      retag_env: true # retag from last env marker
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  waitchanges:
    # Needed to force a wait on service changes
    needs:
      - svc-chg-dp
      - restate-svc-chg-dp
      - svcdb-chg-dp
    runs-on: pcoe-runner-linux-small-prod
    if: ${{ !cancelled() }}
    steps:
      - name: Echo
        run: echo "All changes have been deployed"
