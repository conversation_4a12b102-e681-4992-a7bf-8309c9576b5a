name: On demand service-hotfix

on:
  workflow_dispatch:
    inputs:
      git_ref:
        description: The Git Ref To Be Deployed
        required: true
      target_env:
        type: choice
        description: Target Env
        options:
          - test-1
          - test-2
          - preprod
          - prod
      service_name:
        type: choice
        description:
        options:
          - claims-portal
          - scuk-db

jobs:
  approve:
    runs-on: pcoe-runner-linux-small-prod
    environment: "${{ inputs.target_env == 'prod' && 'approve' || '' }}"
    steps:
      - name: Echo
        run: echo "${{ github.ref_name }} - ${{ github.git_ref }} -> Approved"

  get-argo-creds:
    needs:
      - approve
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/get-argo-creds.yaml@main
    with:
      friendly_name: scf_${{ contains(fromJSON('["preprod", "prod"]'), inputs.target_env) && 'prod' || 'test-1' }}
    secrets:
      VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

  service_hotfix:
    needs:
      - approve
      - get-argo-creds
    if: ${{ !endsWith(inputs.service_name, '-db') }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      git_ref: ${{ inputs.git_ref }}
      k8s_dir_path: "k8s/${{ inputs.service_name }}"
      build_dir: "services/${{ inputs.service_name }}"
      target_env_name: ${{ inputs.target_env }}
      target_branch_name: argo-sync-${{ inputs.target_env }}
      aws_sso_root_role_arn: 'arn:aws:iam::277165315813:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      version: ${{ github.git_ref }}
      force_deploy: true
      argocd_service_name: ${{ inputs.target_env }}-mono-${{ inputs.service_name }}
      argocd_config_path: ${{ needs.get-argo-creds.outputs.argo_config }}
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  service_db_hotfix:
    needs:
      - approve
      - get-argo-creds
    if: ${{ endsWith(inputs.service_name, '-db') }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      git_ref: ${{ inputs.git_ref }}
      k8s_dir_path: "k8s/${{ inputs.service_name }}"
      build_dir: "db/${{ inputs.service_name }}"
      target_env_name: ${{ inputs.target_env }}
      target_branch_name: argo-sync-${{ inputs.target_env }}
      postfix_env_name_to_branch: false
      values_repository_keypath: '.job.image.repository'
      k8s_image_version_value_keypath: '.job.image.tag'
      aws_sso_root_role_arn: 'arn:aws:iam::277165315813:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      version: ${{ github.git_ref }}
      force_deploy: true
      argocd_service_name: ${{ inputs.target_env }}-mono-${{ inputs.service_name }}
      argocd_config_path: ${{ needs.get-argo-creds.outputs.argo_config }}
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}
