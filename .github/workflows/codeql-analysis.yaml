---
name: Code Quality Check

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - release
  pull_request:
    branches:
      - main
      - release
  schedule:
    - cron: 0 8 * * 6

jobs:
  analyze:
    strategy:
      fail-fast: false
      matrix:
        language:
          - javascript
          # - python
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/codeql_common.yml@main
    with:
      language: ${{matrix.language}}
