name: On closed PR to main

on:
  workflow_dispatch:
  # pull_request:
  #   types:
  #     - closed
  #   branches:
  #     - main

jobs:
  pr-feature-preparation:
    runs-on: pcoe-runner-linux-small-prod
    if: github.event.pull_request.merged == true && github.head_ref != 'release' && github.head_ref != 'release-merge'
    steps:
      - name: Suffix removal
        id: branch_clean
        run: |
          source_branch='${{ github.head_ref }}'
          branch=${source_branch%"-merge"}
          echo "branch=${branch}" >> $GITHUB_OUTPUT

      - name: Echo
        run: echo "${{ steps.branch_clean.outputs.branch }} -> release"

      - uses: actions/checkout@v4
        with:
          ref: ${{ steps.branch_clean.outputs.branch }}
          fetch-depth: 0

      - name: Service Team token
        uses: navikt/github-app-token-generator@v1.2.1
        id: get-service-token
        with:
          private-key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
          app-id: ${{ secrets.GIT_APP_ID }}

      - name: <PERSON><PERSON> <PERSON>ull Request
        uses: devops-infra/action-pull-request@v0.5.5
        with:
          github_token: ${{ steps.get-service-token.outputs.token }}
          source_branch: ${{ steps.branch_clean.outputs.branch }}
          target_branch: release
          title: "Release - ${{ github.event.pull_request.title }}"
          body: '**Automated pull request**: Only merge when the feature has been tested properly in test-1'
          label: ${{ contains(github.event.pull_request.labels.*.name, 'non-functional') && 'non-functional,release' || 'release' }}
          assignee: ${{ github.event.pull_request.user.login }}
