name: On closed PR to release

on:
  pull_request:
    types:
      - closed
    branches:
      - release

jobs:
  pr-feature-preparation:
    if: github.event.pull_request.merged == true
    runs-on: pcoe-runner-linux-small-prod
    steps:
      - name: Echo
        run: echo "release -> main"

      - uses: actions/checkout@v4
        with:
          ref: release
          fetch-depth: 0

      - name: Service Team token
        uses: navikt/github-app-token-generator@v1.2.1
        id: get-service-token
        with:
          private-key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
          app-id: ${{ secrets.GIT_APP_ID }}

      - name: Create Pull Request
        uses: devops-infra/action-pull-request@v0.5.5
        with:
          github_token: ${{ steps.get-service-token.outputs.token }}
          source_branch: release
          target_branch: main
          title: "release -> main"
          body: '**Automated pull request**: Verify no breaking change'
          label: main
          allow_no_diff: true
