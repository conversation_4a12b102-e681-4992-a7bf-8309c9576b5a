name: On push to main

on:
  workflow_dispatch:
  push:
    branches:
      - main

jobs:
  # check what has changed
  filters:
    runs-on: pcoe-runner-linux-small-prod
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - uses: chris<PERSON><PERSON><PERSON>/setup-yq@v1.0.1
        with:
          yq-version: v4.28.1

      - name: Split filter files
        run: |
          SKIP=`yq -r -I=0 -o=json '.skip // {} | .test-1 // [""] ' orchestration.yaml`
          echo "Skipping services: $SKIP"
          yq '.infra // {}' orchestration.yaml > orchestration-infra.yaml
          yq ".migrations-db // {} | del(.$SKIP)" orchestration.yaml > orchestration-migrations-db.yaml
          yq ".services // {} | del(.$SKIP)" orchestration.yaml > orchestration-services.yaml
          yq ".restate-services // {} | del(.$SKIP)" orchestration.yaml > orchestration-restate-services.yaml

      - name: Filter infra
        uses: dorny/paths-filter@v2.11.1
        id: infrafilters
        with:
          filters: orchestration-infra.yaml

      - name: Filter DB migrations
        uses: dorny/paths-filter@v2.11.1
        id: servicedbmigrations
        with:
          filters: orchestration-migrations-db.yaml

      - name: Filter services
        uses: dorny/paths-filter@v2.11.1
        id: servicefilters
        with:
          filters: orchestration-services.yaml

      - name: Filter restate-services
        uses: dorny/paths-filter@v2.11.1
        id: restateservicefilters
        with:
          filters: orchestration-restate-services.yaml

      - name: Post filter
        id: postfilter
        run: |
          nonservicechanges=$(yq -r -o=json '. | keys - ${{ steps.servicefilters.outputs.changes }}' orchestration-services.yaml)
          echo "nonservicechanges: ${nonservicechanges}"
          echo 'nonservicechanges<<EOF' >> $GITHUB_OUTPUT
          echo ${nonservicechanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

          nonrestateservicechanges=$(yq -r -o=json '. | keys - ${{ steps.restateservicefilters.outputs.changes }}' orchestration-restate-services.yaml)
          echo "nonrestateservicechanges: ${nonrestateservicechanges}"
          echo 'nonrestateservicechanges<<EOF' >> $GITHUB_OUTPUT
          echo ${nonrestateservicechanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

          nondbmigrationschanges=$(yq -r -o=json '. | keys - ${{ steps.servicedbmigrations.outputs.changes }}' orchestration-migrations-db.yaml)
          echo "nondbmigrationschanges: ${nondbmigrationschanges}"
          echo 'nondbmigrationschanges<<EOF' >> $GITHUB_OUTPUT
          echo ${nondbmigrationschanges} >> $GITHUB_OUTPUT
          echo 'EOF' >> $GITHUB_OUTPUT

    outputs:
      infrachanges: ${{ steps.infrafilters.outputs.changes }}
      servicechanges: ${{ steps.servicefilters.outputs.changes }}
      restateservicechanges: ${{ steps.restateservicefilters.outputs.changes }}
      servicedbmigrations: ${{ steps.servicedbmigrations.outputs.changes }}
      nonservicechanges: ${{ steps.postfilter.outputs.nonservicechanges }}
      nonrestateservicechanges: ${{ steps.postfilter.outputs.nonrestateservicechanges }}
      nonservicedbmigrations: ${{ steps.postfilter.outputs.nondbmigrationschanges }}

  terraform-apply-test-1:
    needs: filters
    if: ${{ needs.filters.outputs.infrachanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        # This is a fake matrix, just to keep code coherent
        service: ${{ fromJSON(needs.filters.outputs.infrachanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/terraform.yml@main
    with:
      infra_dir_path: infra/
      target_env_name: test-1
      aws_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      terraform_apply: true
      pre_apply_script: npm install -g esbuild@0.19.11
      rsync_install: true
      vault_role_id: ff98ebe8-61a4-65f0-3e32-59d4e40d143e
      terraform_version: 1.6.6
      nodejs_version: 'lts/*'
      npm_url: https://npm.pkg.github.com
      python_version: '3.11'
    secrets:
      infracost_api_key: ${{ secrets.INFRACOST_API_KEY }}
      github_app_id: ${{ secrets.TF_MODULES_GITHUB_APP_ID }}
      github_app_private_key: ${{ secrets.TF_MODULES_GITHUB_PRIVATE_KEY }}
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}
      vault_secret_id: ${{ secrets.VAULT_SECRET_ID }}

  # wait-for-inspector:
  #   needs: [terraform-apply-test-1]
  #   runs-on: pcoe-runner-linux-small-prod
  #   steps:
  #     - name: Wait for Inspector scans to finish
  #       run: sleep 120
  #       continue-on-error: true
  # inspector:
  #   needs: [wait-for-inspector]
  #   if: ${{ !cancelled() }}
  #   uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/inspector_check.yaml@main
  #   with:
  #     environment_name: test-1

  service-changed-build:
    needs: filters
    if: ${{ needs.filters.outputs.servicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.servicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: services/${{ matrix.service }}
      target_env_name: test-1
      target_branch_name: argo-sync-test-1
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      include_deploy: true # build and deploy
      retag_env: false # not a retag
      deploy_location: artifact
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  service-not-changed-retag:
    needs:
      # Non changed afterwards to speed up
      - filters
      - waitchanges
    if: ${{ !cancelled() && needs.filters.outputs.nonservicechanges != '[]' }}
    # See: https://github.com/orgs/community/discussions/68214
    # if: ${{ needs.filters.outputs.nonservicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.nonservicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: services/${{ matrix.service }}
      target_env_name: test-1
      target_branch_name: argo-sync-test-1
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      include_deploy: false # do not build
      retag_env: true # retag from last env marker
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  restate-service-changed-deploy:
    needs: filters
    if: ${{ needs.filters.outputs.restateservicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.restateservicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_restate_helm.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: restate-services/${{ matrix.service }}
      target_env_name: test-1
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      aws_service_account_arn: 'arn:aws:iam::************:role/SSOAdmin' # NP account
      include_deploy: true # build and deploy
      retag_env: false # not a retag
    secrets:
      kpmg_helm_password: ${{ secrets.KPMG_HELM_PASSWORD }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  restate-service-not-changed-retag:
    needs:
      # Non changed afterwards to speed up
      - filters
      - waitchanges
    if: ${{ !cancelled() && needs.filters.outputs.nonrestateservicechanges != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.nonrestateservicechanges) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main # Same build steps does not matter
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: restate-services/${{ matrix.service }}
      target_env_name: test-1
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      include_deploy: false # do not build
      retag_env: true # retag from last env marker
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  servicedbmigrations-changed-build:
    needs: filters
    if: ${{ needs.filters.outputs.servicedbmigrations != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.servicedbmigrations) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: db/${{ matrix.service }}
      target_env_name: test-1
      target_branch_name: argo-sync-test-1
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      values_repository_keypath: '.job.image.repository'
      k8s_image_version_value_keypath: '.job.image.tag'
      include_deploy: true # build and deploy
      retag_env: false # not a retag
      deploy_location: artifact
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  services-changed-deploy:
    needs:
      - service-changed-build
      - servicedbmigrations-changed-build
    if: ${{ !cancelled() && (needs.filters.outputs.servicechanges != '[]' || needs.filters.outputs.servicedbmigrations != '[]') }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/gather-script-commit.yaml@main
    with:
      target_branch_name: argo-sync-test-1
      commit_message: argocd k8s auto test-1 ${{ github.sha }}
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  get-argo-creds:
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/get-argo-creds.yaml@main
    with:
      friendly_name: scf_test-1
    secrets:
      VAULT_SECRET_ID: ${{ secrets.VAULT_SECRET_ID }}

  argo-trigger-sync:
    needs:
      - get-argo-creds
      - services-changed-deploy
    runs-on: pcoe-runner-linux-small-prod
    if: ${{ !cancelled() && (needs.filters.outputs.servicechanges != '[]' || needs.filters.outputs.servicedbmigrations != '[]') }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJson(needs.services-changed-deploy.outputs.changes) }}
    steps:
      - name: Format argo service name
        id: argo-service-name
        run: |
          service_name="test-1-mono-"
          service_name+=`echo '${{ matrix.service }}' | grep 'k8s/' | cut -d '/' -f 2`
          echo $service_name
          echo "service_name=$service_name" >> $GITHUB_OUTPUT

      - name: Sync ArgoCD App
        if: ${{ steps.argo-service-name.outputs.service_name != '' }}
        uses: KPMG-UK/ie-cdd-github-actions-library/actions/argo-sync-service@main
        with:
          service_name: ${{ steps.argo-service-name.outputs.service_name }}
          argo_context: ${{ needs.get-argo-creds.outputs.argo_config }}

  servicedbmigrations-not-changed-retag:
    needs:
      # Non changed afterwards to speed up
      - filters
      - waitchanges
    if: ${{ !cancelled() && needs.filters.outputs.nonservicedbmigrations != '[]' }}
    # See: https://github.com/orgs/community/discussions/68214
    # if: ${{ needs.filters.outputs.nonservicedbmigrations != '[]' }}
    strategy:
      fail-fast: false
      matrix:
        service: ${{ fromJSON(needs.filters.outputs.nonservicedbmigrations) }}
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/k8s_argocd.yml@main
    with:
      k8s_dir_path: k8s/${{ matrix.service }}
      build_dir: db/${{ matrix.service }}
      target_env_name: test-1
      target_branch_name: argo-sync-test-1
      aws_sso_root_role_arn: 'arn:aws:iam::************:role/github-ie-cdd-scf'
      aws_region: eu-west-2
      values_repository_keypath: '.job.image.repository'
      k8s_image_version_value_keypath: '.job.image.tag'
      include_deploy: false # do not build
      retag_env: true # retag from last env marker
    secrets:
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}

  waitchanges:
    # Needed to force a wait on service changes
    needs:
      - services-changed-deploy
      - restate-service-changed-deploy
    runs-on: pcoe-runner-linux-small-prod
    if: ${{ !cancelled() }}
    steps:
      - name: Echo
        run: echo "All changes have been deployed"
