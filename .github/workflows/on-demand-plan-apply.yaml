name: On demand terraform

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Select the environment to deploy'
        required: true
        type: choice
        options:
          - test-1
          - test-2
          - preprod
          - prod

jobs:
  tf_plan:
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/terraform.yml@main
    with:
      target_env_name: ${{ github.event.inputs.environment }}
      infra_dir_path: infra/
      aws_role_arn: 'arn:aws:iam::277165315813:role/github-ie-cdd-scf'
      terraform_apply: false
      vault_role_id: ff98ebe8-61a4-65f0-3e32-59d4e40d143e
      terraform_version: 1.6.6
      wiki_page_name: "TF ${{ github.event.inputs.environment }} Plan"
    secrets:
      infracost_api_key: ${{ secrets.INFRACOST_API_KEY }}
      github_app_id: ${{ secrets.TF_MODULES_GITHUB_APP_ID }}
      github_app_private_key: ${{ secrets.TF_MODULES_GITHUB_PRIVATE_KEY }}
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}
      vault_secret_id: ${{ secrets.VAULT_SECRET_ID }}

  approve:
    runs-on: pcoe-runner-linux-small-prod
    environment: approve
    steps:
      - name: Echo
        run: echo "${{ github.ref_name }} - ${{ github.git_ref }} -> Approved"

  tf_apply:
    uses: KPMG-UK/ie-cdd-github-actions-library/.github/workflows/terraform.yml@main
    needs:
      - tf_plan
      - approve
    with:
      target_env_name: ${{ github.event.inputs.environment }}
      infra_dir_path: infra/
      aws_role_arn: 'arn:aws:iam::277165315813:role/github-ie-cdd-scf'
      terraform_apply: true
      pre_apply_script: npm install -g esbuild@0.19.11
      rsync_install: true
      vault_role_id: ff98ebe8-61a4-65f0-3e32-59d4e40d143e
      terraform_version: 1.6.6
      nodejs_version: 'lts/*'
      npm_url: https://npm.pkg.github.com
      python_version: '3.11'
      wiki_page_name: "TF ${{ github.event.inputs.environment }} Apply"
    secrets:
      infracost_api_key: ${{ secrets.INFRACOST_API_KEY }}
      github_app_id: ${{ secrets.TF_MODULES_GITHUB_APP_ID }}
      github_app_private_key: ${{ secrets.TF_MODULES_GITHUB_PRIVATE_KEY }}
      team_github_app_id: ${{ secrets.GIT_APP_ID }}
      team_github_app_private_key: ${{ secrets.GIT_APP_PRIVATE_KEY }}
      teams_webhook: ${{ secrets.TEAMS_WEBHOOK }}
      vault_secret_id: ${{ secrets.VAULT_SECRET_ID }}
